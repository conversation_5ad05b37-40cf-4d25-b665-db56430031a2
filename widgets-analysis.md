# Анализ системы интерфейса и архитектуры виджетов в РМО

## Обзор системы виджетов

РМО (Рабочее Место Оператора) представляет собой комплексное приложение, построенное на модульной архитектуре виджетов. Каждый виджет является независимым компонентом с собственным набором входов, выходов и поведением. Система спроектирована таким образом, чтобы обеспечить гибкость, расширяемость и удобство обслуживания.

## Основные классы виджетов

### Индикаторы и дисплеи
- **TextWidget**: Простые текстовые элементы для отображения статического содержимого
- **TextComplicatedWidget**: Текстовые виджеты, которые могут динамически обновлять содержимое из телеметрии
- **ConditionalTextWidget**: Текст, который показывается/скрывается на основе условий состояния системы
- **CircleWidget**: Круговые индикаторы, которые меняют цвет в зависимости от состояния (вкл/выкл/промежуточное)
- **DialIndicator**: Индикаторы в стиле приборной панели для отображения значений давления, скорости и т.д.
- **MovingDataTranslator**: Отображает значения телеметрии с единицами измерения и цветовой кодировкой на основе пороговых значений

### Элементы управления
- **SmartButton**: Интерактивные кнопки с отслеживанием состояния
- **SmartButtonGroup**: Группы связанных кнопок, работающих вместе
- **SmartButtonGroupForCore**: Специализированная группа кнопок для управления параметрами ядра
- **SmartCheckbox**: Чекбоксы с расширенной функциональностью
- **SendHoleButton**, **PhotoButton**, **DropDrillError** и другие специализированные кнопки действий
- **CustomSliderWrapper**: Обертка для UI слайдеров с дополнительной функциональностью
- **JoystickWidgetWrapper**: Виртуальный джойстик для управления

### Специализированные дисплеи
- **GPSWidget**: Показывает информацию о статусе GPS
- **RvizMap**: Виджет визуализации карты
- **Cameras**: Дисплеи для отображения видеопотока с различными настраиваемыми режимами
- **DepthWidgetWrapper**: Показывает информацию о глубине бурения
- **MessagesWidget**: Отображает системные сообщения и журналы
- **AngleIndicator**: Отображает угол наклона станка
- **Distance2HoleWidget**: Показывает расстояние до следующей скважины

## Архитектура и поток данных

### Процесс инициализации
1. Виджеты определяются в конфигурационном файле `modules.json`
2. Каждый виджет имеет флаг `is_active`, `class_name`, `file_name`, и наборы параметров (`init_args` и `prep_args`)
3. Основное приложение загружает каждый активный модуль из `modules.json` используя `import_module` Python
4. Каждый виджет инициализируется с экземпляром ядра (core) и настраивается с указанными параметрами

### Взаимодействие виджетов с ядром
1. Все виджеты получают ссылку на центральный класс `Core` при инициализации
2. Виджеты используют вспомогательную функцию `get_from_telemetry(core, key, default_value)` для доступа к данным телеметрии
3. Класс Core поддерживает словари состояний, такие как `telemetry`, `cameras`, `log_msgs` и т.д.
4. Виджеты связываются с определенными значениями телеметрии через параметр `field_to_read`

### Паттерн подписки на телеметрию
1. Виджеты используют экземпляры QTimer для периодического вызова своего метода `update()`
2. Во время обновлений виджеты проверяют, есть ли активный транспорт (`in_rc_vehid` или `watched_vehid`)
3. Если транспорт активен, виджеты получают и отображают соответствующие значения телеметрии
4. Многие виджеты используют соединения сигнал-слот для асинхронных обновлений

### Управление видимостью и положением виджетов
1. Позиции виджетов определяются параметрами `x`, `y`, `width` и `height`
2. Параметр `screen_num` определяет, на каком экране появляется виджет (настройка для нескольких мониторов)
3. Видимость виджетов может управляться:
   - Флагом `is_active` в определении модуля
   - Видимостью, специфичной для режима, используя списки `modes_visible` (например, показывать только в режиме "drill")
   - Условной видимостью на основе значений телеметрии

## Примеры виджетов и их зависимости

### Индикаторы типа "циферблат" (DialIndicator)
Пример из `modules.json`:
```json
"dial_indicator_rot_speed": {
  "is_active": true,
  "file_name": "gui_modules",
  "class_name": "DialIndicator",
  "init_args": {},
  "prep_args": {
    "width": 240,
    "height": 240,
    "x": 30,
    "y": 102,
    "field_to_read": "drill_rotation_speed",
    "initial_value": 25,
    "scale_range": 14,
    "scale_step": 2,
    "caption": "Revolutions\n(revs/min) x10",
    "scale_gradient": {
      "green": 0.34,
      "yellow": 0.30,
      "red": 0.15
    },
    "coeff": 0.1,
    "digit_value_coeff": 10,
    "modes_visible": ["drill"],
    "screen_num": 2
  }
}
```
- Этот индикатор отображает скорость вращения бура
- Он виден только в режиме "drill" и отображается на экране 2
- Значение получается из поля телеметрии `drill_rotation_speed`
- Цвета меняются в зависимости от пороговых значений (зеленая/желтая/красная зоны)

### Круговые индикаторы (CircleWidget)
```python
def prepare(self, r=500, x=500, y=800, from_telemetry=False, field_to_read="", second_field_to_read=None,
          on_color="#00ff00", off_color="#FF0000", disabled_color="#AAAAAA", intermediate_color="#FFA500",
          screen=1):
```
- Показывает цветной круг, указывающий состояние (вкл=зеленый, выкл=красный, промежуточный=оранжевый)
- Может отображать сложные состояния, используя два поля телеметрии
- Отключенное состояние показывается, когда не выбран транспорт

### Виджет сообщений (MessagesWidget)
- Фильтрует и отображает сообщения журнала ROS
- Цветовая кодировка сообщений по уровню важности
- Обновляется в реальном времени на основе полученных сообщений журнала

## Режимы системы и состояния виджетов

Система работает в нескольких режимах, которые влияют на видимость и поведение виджетов:

### Операционные режимы
- `drilling`: Управление операцией бурения и индикаторы
- `driving`: Управление движением транспорта
- `leveling`: Управление стабилизацией машины
- `tower_control`: Управление башней/мачтой
- `buildup`: Операции по сборке штанг
- `carousel_control`: Управление каруселью штанг

### Конечные автоматы состояний
- Сложные конечные автоматы управляют различными подсистемами (Main, Driller, Arm, Leveler и т.д.)
- Виджеты обновляются, чтобы отражать текущее состояние этих подсистем
- Виджет `MachineStateManagement` предоставляет интерфейс для просмотра и изменения этих состояний

## Ключевые наблюдения

### Модульная архитектура
Система использует высоко модульный подход, где виджеты настраиваются в JSON и динамически загружаются во время выполнения. Это позволяет легко добавлять новые компоненты или модифицировать существующие без изменения основного кода.

### Отображение на основе телеметрии
Большинство виджетов связаны с определенными полями телеметрии и автоматически обновляются при изменении значений. Это создает реактивный интерфейс, который отражает текущее состояние системы.

### Поддержка нескольких экранов
Виджеты могут быть расположены на нескольких экранах с помощью параметра `screen_num`, что позволяет создавать сложные многоэкранные интерфейсы для операторов.

### Интерфейс, специфичный для режима
Виджеты могут быть настроены так, чтобы появляться только в определенных операционных режимах, создавая контекстно-зависимые интерфейсы, которые показывают только релевантную в данный момент информацию.

### Интернационализация
Система поддерживает несколько языков через систему переводов Qt, с обработкой текста виджетов через `QApplication.translate()`.

### Интеграция с оборудованием
Виджеты, такие как `JoystickWidgetWrapper`, взаимодействуют с физическими элементами управления через модуль `joystick_adapter`.

### Иерархия визуализации
Система использует различные уровни визуализации, от простых индикаторов до сложных интерактивных дисплеев, таких как `RvizMap`.

## Проблемы текущей архитектуры

1. **Избыточность подписок**: Многие виджеты подписываются на одни и те же данные телеметрии, что приводит к избыточной обработке.

2. **Тесная связь с ядром**: Виджеты имеют прямой доступ к ядру, что создает тесную связь между компонентами.

3. **Смешение данных и представления**: Некоторые виджеты содержат как логику обработки данных, так и визуализацию, что затрудняет тестирование и повторное использование.

4. **Недостаточное разделение обязанностей**: Некоторые виджеты выполняют слишком много функций, что затрудняет их понимание и поддержку.

5. **Статическая конфигурация**: Хотя модули настраиваются через JSON, изменение конфигурации требует перезапуска приложения.

## Рекомендации по улучшению архитектуры

1. **Внедрение паттерна публикации/подписки**: Создание централизованного брокера событий, который будет распространять обновления телеметрии только заинтересованным виджетам.

2. **Применение паттерна Model-View-ViewModel (MVVM)**: Разделение данных, бизнес-логики и представления для улучшения тестируемости и поддержки.

3. **Создание базового класса виджета**: Разработка универсального базового класса виджета с общей функциональностью для упрощения создания новых виджетов.

4. **Динамическая компоновка интерфейса**: Возможность изменять компоновку виджетов во время выполнения без перезапуска приложения.

5. **Кэширование и оптимизация обновлений**: Минимизация обновлений виджетов, когда значения телеметрии не меняются.

6. **Улучшение управления зависимостями**: Использование техник инъекции зависимостей для уменьшения тесной связи между компонентами.

7. **Внедрение DDS**: Переход на стандартизированный протокол обмена данными Data Distribution Service (DDS) для более надежной и масштабируемой коммуникации.

## Заключение

Текущая архитектура виджетов в РМО является функциональной и гибкой, но имеет несколько областей для улучшения. Внедрение более строгих паттернов проектирования и улучшение управления данными может значительно повысить поддерживаемость, тестируемость и производительность системы. Переход на DDS для обмена данными обеспечит стандартизированный и надежный механизм коммуникации между компонентами системы.