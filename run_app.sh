#!/bin/bash

set -e

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Virtual environment not found. Creating one now..."
    ./venv-create.sh
fi

# Activate virtual environment
source venv/bin/activate

# Detect operating system
if [[ "$OSTYPE" == "darwin"* ]]; then
    # Mac OS X
    echo "Running on macOS"
    # Handle QT platform plugins if needed
    export QT_MAC_WANTS_LAYER=1
    # For Retina display
    export QT_AUTO_SCREEN_SCALE_FACTOR=1
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "Running on Linux"
    # Allow X11 connections if needed
    xhost +local:docker &>/dev/null || true
fi

# Run the application
echo "Starting application..."
cd core && python3 main.py dev_mode

# Deactivate virtual environment when done
deactivate