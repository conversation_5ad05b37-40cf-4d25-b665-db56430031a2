# РМО
## Предварительная настройка системы

1. Предварительно должны быть установлены: Python 3.7+, ROS и нижеперечисленные пакеты
1.1. Быстрая установка ROS http://wiki.ros.org/ROS/Installation/TwoLineInstall 
1.2. Дополнительные пакеты

```bash
sudo apt-get install -y python3-rospkg
```

## Создание виртуального окружения

1. Запустите консоль
2. В консоли перейдите в папку с программой (iplace).
3. Запустите скрипт 

```bash
./venv-create.sh
```

Удостоверьтесь, что создалась папка venv и она не пустая

## Запуск

Запуск из виртуального окружения в режиме разработчика

```bash
cd core
../venv/bin/python main.py dev_move
```

Запуск из виртуального окружения в рабочем режиме

```bash
# (из папки core)
../venv/bin/python main.py
```

В результате будет запущено многооконное приложение рабочего места оператора управления роботизированного бурового станка. 
При начальной установке на производстве может потребоваться настройка расположения дисплеев средствами операционной системы.

## Дополнительно
https://vistrobotics.atlassian.net/wiki/spaces/DD/pages/613285907/Iplace+3.0
