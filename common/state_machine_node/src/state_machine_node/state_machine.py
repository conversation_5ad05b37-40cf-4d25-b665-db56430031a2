#!/usr/bin/env python
# -*- coding: utf-8 -*-

import itertools
import traceback
import rospy

from threading import RLock

from .state import FAILUR<PERSON>
from .status_topic_name import generate_status_topic_name
from .state import FailureState
from .subscriber import SubscriberJam
from .subscriber import NO_DATA_WARNING, TIMEOUT_WARNING
from common.base_node import BaseNode 

from std_msgs.msg import Header

from common_msgs.msg import MovePermission
from drill_msgs.msg import ManualState, StateMachineStatus, BoolStamped


class AbstractNodeStateMachine(BaseNode):
    u"""
    This is abstract State Machine node that can be use in different ways.

    This class implement main algorithm of state machine transition such as switch state, 
    current state processing, handle error while working, processing move permission request.

    Attributes:
        cur_state: current state of state machine.
            Should has the same interface as AbstractState.
        
        last_state: previous state of state machine ( state before cur_state ).  
            Initialized by None. Changes in `set_state` method 
            if flag prev_state_saving_flag is set.

        change_state_timestamp (float): timestamp of last transition from prev_state to cur_state.
            This attribute gets his value from abstract function `get_timestamp`.

        move_permission_flag (MovePermissionProcessor): instance that collects 
            move permissions flags from different sources and return permission flag. 
            Use in `do_work` method. If value is True will do `self.cur_state.do_work()` 
            else do `self.do_prohibit_state()`.
        
        state_map (StateMap): map that hold state's instances. You can get 
            instances by state's name.
        
        subs (SubscriberJam): class that can receive messages and check timeout of message receiving. 
            You can use it by subscribing status topics.

        _last_action_seq (int): last action seq.

        _cur_action_seq (int): current action seq.


    
    Examples:
    
        from state_machine_node import AbstractNodeStateMachine, AbstractState, FAILURE

        FIRST_STATE = 'move_straight'
        SECOND_STATE = 'rotate_straight'


        class MoveStraightState(AbstractState):
            def __init__(self, state_machine):
                super(FirstState, self).__init__(state_machine, FIRST_STATE)

            def do_work(self):
                self.move_straight()
                sleep(0.5)
                self.state_machine.set_state(SECOND_STATE)
                
                
        class SecondState(AbstractState):
            def  __init__(self, state_machine):
                super(SecondState, self).__init__(state_machine, SECOND_STATE)

            def do_work(self):
                self.turn(1)
                sleep(0.5)
                self.turn(-1)
                sleep(0.5)
                self.state_machine.set_state(FIRST_STATE)


        class SimpleStateMachine(AbstractNodeStateMachine):

            def drill_initialize(self, *args, **kwargs):

                super(SimpleStateMachine, self).initialize()

                self.set_current_state(FirstState(self))
                self.add_states([SecondState(self)])

                self.move_pub = rospy.Publish(...)

            def subscribers_initialize(self):
            
                self.add_subscriber('robot/state',
                                    RobotState,
                                    'state',
                                    2,
                                    0.5)

            def move_straight(self):
                self.move(1, 0)

            def turn(self, rotation):
                self.move(0, rotation)

            def move(speed, rotation):
                message = Control(speed, rotation)
                self.move_pub.publish(message)
        
            def check_data(self):
                if self.subs.state == None:
                    return False

                return True

    """
    def initialize(self, *args, **kwargs):

        self.change_state_timestamp = 0
        self.change_state_timestamp_full = 0
        self.__move_permission = True
        self.change_move_permission_timestamp = 0

        self.state_machine = StateMachine(self, fail_state=FailureState(self))

        self._last_action_seq = -1
        self._cur_action_seq = -1

        self.__robomode = False
        self.last_empty_loop_ts = 0

        # We shouldn't switching states in parallel
        self.__state_lock = RLock()

        self.subs = SubscriberJam()

        driller_status_topic = generate_status_topic_name(self._name)
        self.driller_status_pub = rospy.Publisher(driller_status_topic, StateMachineStatus, queue_size=10)
        self.move_permission_pub = rospy.Publisher(rospy.get_param('Global/topics/permission'), MovePermission,
                                                   queue_size=10)

        self.initialize_params()

        self.drill_initialize(*args, **kwargs)

        rospy.Subscriber(rospy.get_param('Global/topics/permission'), MovePermission, self.__move_permission_callback)
        rospy.Subscriber(rospy.get_param('Global/topics/current_robomode'), BoolStamped, self.__robomode_callback)
        rospy.Subscriber(rospy.get_param('Global/topics/manual_state'), ManualState, self.__manual_state_callback)

        self.subscribers_initialize()

    def drill_initialize(self, *args, **kwargs):
        u"""
        Initialize attributes here.
        """
        pass

    def subscribers_initialize(self):
        u"""
        Initialize subscribers here.
        """
        pass

    def initialize_params(self):
        u"""
        Set params from yaml file as node attributes.
        Set all data except rate and debug.
        """
        for param, value in self.node_params.items():
            if param not in ['rate', 'debug']:
                self.__dict__[param] = value

    #----------self.state_machine----------#

    def set_current_state(self, current_state):
        u"""
        Setting current state.

        Args:
            current_state (AbstractState): heir of AbstractState.
        """
        self.state_machine.set_current_state(current_state)

    def add_states(self, states):
        u"""
        Adding state or states to state machine.

        Args:
            states (list, AbstractState): the list of AbstractState class heirs 
                or instance of AbstractState class heirs. 
        """
        self.state_machine.add_states(states)
    
    def do_work(self):
        u"""
        Main state machine work loop.
        """
        self.state_machine.do_work()
    
    def set_state(self, new_state_name, **kwargs):
        u"""
        Switch state machine to new state.

        If some Exception happens while switching state TransitionFaildError 
        will raise and state switch to failure.

        Args:
            new_state_name: new state's name.

        Raises:
            TransitionFaildError: if some error occurs while switching state.
        """
        with self.__state_lock:
            self.state_machine.set_state(new_state_name, **kwargs)

    #----------/self.state_machine----------#

    def get_change_state_timestamp(self):
        u"""
        Return timestamp of changing state.

        Return:
            float.
        """
        return self.change_state_timestamp

    def get_current_state_duration(self):
        u"""
        Return the duration (in seconds) since the state was last changed.

        Return:
            float: Duration in seconds.
        """
        return rospy.get_time() - self.get_change_state_timestamp()

    def get_change_state_timestamp_full(self):
        u"""
        Return timestamp of changing state (not restarting on permission change).

        Return:
            float.
        """
        return self.change_state_timestamp_full

    def get_current_state_duration_full(self):
        u"""
        Return the duration (in seconds) since the state was last changed (not restarting on permission change).

        Return:
            float: Duration in seconds.
        """
        return rospy.get_time() - self.get_change_state_timestamp_full()

    def get_last_state(self):
        u"""
        Return last state instance.

        Return:
            Heir of AbstractState.
        """
        return self.state_machine.get_last_state()

    def get_current_state(self):
        u"""
        Return current state instance.

        Return:
            Heir of AbstractState.
        """
        return self.state_machine.get_current_state()

    def subs_process(self):
        u"""
        Do Subscriber's process.
        """
        return self.subs.process()

    def get_move_permission(self):
        u"""
        Return move permission 
        """
        return self.__move_permission

    def get_robomode(self):
        u"""
        Return move permission
        """
        return self.__robomode

    def mark_timestamp(self):
        u"""
        Marking current time. 
        """
        self.change_state_timestamp = self.get_timestamp()
        if not ("remote" in self.state_machine.get_current_state().get_name()):
            if self.state_machine.get_last_nonremote_state() is not self.state_machine.get_current_state():
                self.change_state_timestamp_full = self.get_timestamp()

    def do_work_after(self):
        u"""
        Called after `self.cur_state.do_work()` or `self.do_prohibit_state()`.
        """
        pass

    def do_prohibit_state(self):
        u"""
        Called in `do_work` if sets flag move permission or `self.check_data` returns False.
        """
        self.stop_control()

    def subs_timeout_handle(self, subs_topic_name, timeout_type):
        u"""
        Handle topic timeout notification and send internal error.
        If no data in topic than send warnings. If data delays that send error.

        Args:
            message_name (str).
            timeout_type (int).
        """
        if timeout_type == NO_DATA_WARNING:
            # send warnings
            self.handle_internal_error(error_message='No showing data in topic {}.'.format(subs_topic_name), error_type=403)
        else:
            # send error
            self.handle_internal_error(error_message='Data delay in topic {}.'.format(subs_topic_name), error_type=503)


    def handle_internal_error(self, error_message='', error_type=401, event=''):
        u"""
        Handle internal error.

        If error is one of warnings then switch to failure state.

        Args:
            error_message (str). 
            error_type (int): error code.
        """
        is_warning = error_type in rospy.get_param('Global/Reports/Warnings').values()
        loglevel = 2 if is_warning else 3
        self.publish_alert_once(error_type, error_message, loglevel, event)
        if not is_warning:
            self.stop_control()
            if error_type != 503 and not self.is_current_state_failure() and not self.state_machine.get_current_state().supress_failure:
                self.switch_to_failure()

    def transition_failed_process(self):
        u"""
        Called when state transition failed (exception is raised).
        """
        pass
    
    def do_work_failed(self):
        u"""
        Calls when do_work had Exception except TransitionFail.
        """
        self.stop_control()

    def check_data(self):
        u"""
        Check data before main state work. Return True if data is good else False.

        Return:
            bool.
        """
        return True

    def get_timestamp(self):
        u"""
        Abstract method of getting timestamp.

        Uses in switching state method when stamp switch event.

        Return:
            Timestamp.
        """
        return rospy.get_time()

    def stop_control(self):
        u"""
        Abstract method for stopping control.
        """
        pass

    def switch_to_failure(self):
        u"""
        Switching from current state to failure state.
        """
        self.state_machine.switch_to_failure()

    def is_current_state_failure(self):
        u"""
        Check current state. Return True if current state is FAILURE else False.
        """
        return self.state_machine.is_current_state_failure()

    def publish_status(self):
        u"""
        Publish state machine status.
        """
        message = StateMachineStatus()
        message.header.stamp = rospy.Time.now()
        message.status = self.get_current_state().get_name()
        message.last_action_seq = self._last_action_seq
        message.cur_action_seq = self._cur_action_seq
        message.state_duration_sec = self.get_timestamp() - self.get_change_state_timestamp_full()
        self.driller_status_pub.publish(message)

    def publish_move_permission(self, permission, source=None):
        u"""
        Publish move permission flag. You can set source, y default it uses `self._name`.

        Args:
            permission (bool): move permission flag.
            source (str): move permission source or None. If None will use `self._name`.
        """
        if not source:
            source = self._name

        msg = MovePermission()
        msg.header.stamp = rospy.Time.now()
        msg.permission = permission
        msg.source = source

        self.move_permission_pub.publish(msg)

    def __manual_state_callback(self, message):
        u"""
        Receive ManualState message.

        Args:
            message (ManualState).
        """
        if message.node_name == self._name:
            if message.mode == "prev":
                if self.state_machine.is_current_state_failure():
                    self.state_machine.switch_to_last_state()
            else:
                self.state_machine.set_state(message.mode)

    def __move_permission_callback(self, message):
        u"""
        Receive MovePermission message.

        Args:
            message (MovePermission).
        """
        if self.__move_permission != message.permission:
            if not message.permission:
                self.log(
                    "Receive MovePermission message with flag {}".format(message.permission),
                    loglevel=2
                )
            self.__move_permission = message.permission

            self.change_move_permission_timestamp = self.get_timestamp()

            self.change_state_timestamp = self.get_timestamp() if \
                self.get_move_permission() else \
                self.change_state_timestamp

    def __robomode_callback(self, message):
        u"""
        Receive Robomode message.

        Args:
            message (BoolStamped).
        """
        self.__robomode = message.value

    def add_subscriber(self, 
                       topic, 
                       class_, 
                       variable_name, 
                       timeout_not_shoving_up_data=None, 
                       timeout_data_obsolescence=None,
                       timeout_handler=None,
                       **kwargs):
        u"""
        Set subscriber to `self.subs`. 
        
        If timeout is not None than set 
        timeout_event_handler to self.simple_message_timeout_handler.
        """
        if timeout_data_obsolescence is None:
            timeout_data_obsolescence = rospy.get_param('Global/Timeouts/timeout_obsolescence')

        if timeout_not_shoving_up_data is None:
            timeout_not_shoving_up_data = rospy.get_param('Global/Timeouts/timeout_not_shoving_up')

        if timeout_handler is None:
            timeout_handler = self.subs_timeout_handle

        self.subs.add_subscriber(topic_name=topic,
                                 class_object=class_,
                                 variable_name=variable_name,
                                 timeout_not_shoving_up_data=timeout_not_shoving_up_data, 
                                 timeout_data_obsolescence=timeout_data_obsolescence,
                                 timeout_event_handler=timeout_handler,
                                 subs_topic_name=topic,
                                 **kwargs)


class StateMachine(object):
    def __init__(self, node, cur_state=None, fail_state=None, other_states=[]):
        self._node = node
        self._cur_state = cur_state
        self._last_state = cur_state
        self._last_nonremote_state = cur_state
        self._fail_state = fail_state

        self._state_map = StateMap()

        common_states = list()

        if self._cur_state:
            common_states.append(self._cur_state) 
        
        if self._fail_state:
            common_states.append(self._fail_state)

        chained_states = list(itertools.chain(other_states, common_states)) \
            if common_states else other_states

        self._state_map.append(chained_states)

    def set_fail_state(self, new_fail_state):
        u"""
        Setting fail state.

        Args:
            new_fail_state (heir of AbstractState): instance of state.
        """
        if not self._fail_state:
            self._fail_state = new_fail_state
            self._state_map.append(self._fail_state)

    def set_current_state(self, new_cur_state):
        u"""
        Setting current state.

        Args:
            new_cur_state (heir of AbstractState): instance of state.
        """
        if not self._cur_state:
            self._last_state = new_cur_state
            self._cur_state = new_cur_state
            self._state_map.append(self._cur_state)
            if not ("remote" in new_cur_state.get_name()):
                self._last_nonremote_state = new_cur_state

    def add_states(self, states):
        u"""
        Add states or state.

        Args:
            states (hier of AbstractState or list): the list of AbstractState class heirs 
                or instance of AbstractState class heirs.
        """
        self._state_map.append(states)

    def do_work(self):
        u"""
        Main state machine work loop.
        """
        try:
            all_data_present = self._node.subs_process()
            if (self._cur_state.ignore_missing_inputs or all_data_present) and \
                    (self._cur_state.ignore_permission or self._node.get_move_permission()) and \
                    (self._cur_state.ignore_robomode or self._node.get_robomode()) and \
                    (self._node.check_data() or self._cur_state.ignore_check):
                # main method
                self._cur_state.do_work()
            else:
                # in case move prohibited
                self._node.last_empty_loop_ts = rospy.get_time()
                self._node.do_prohibit_state()
        except Exception as e:
            self._node.log(traceback.format_exc(), loglevel=2)
            self._node.handle_internal_error(error_message=str(traceback.format_exc()),
                                             error_type=101)
            self._node.do_work_failed()
        finally:
            self._node.do_work_after()
            self._node.publish_status()
    
    def set_state(self, new_state_name, **kwargs):
        u"""
        Change current state.

        Args: 
            new_state_name (str): name of new current state. 
        """
        try:
            self._cur_state.on_transition_from()
            if self._cur_state.prev_state_saving_flag:
                self._last_state = self._cur_state
                if not ("remote" in self._cur_state.get_name()):
                    self._last_nonremote_state = self._cur_state

            self._node.log('Switching from {} to {}'.format(self._cur_state.get_name(), new_state_name), loglevel=1)

            self._cur_state = self._state_map.get(new_state_name)
            if not kwargs.get('prev'):  # to not reset kwargs, when set_state is called with prev=True argument (used in switch_to_last_state)
                self._cur_state.last_transition_kwargs = kwargs
            self._cur_state.on_transition_to()

            self._node.mark_timestamp()

        except Exception as e:
            self._node.log(traceback.format_exc(), loglevel=2)
            self._node.transition_failed_process()
    
    def get_current_state(self):
        """
        Return current state.

        Return:
            Hier of AbstractState.
        """
        return self._cur_state
    
    def get_last_state(self):
        u"""
        Return preveous state.

        Return:
            Heir of AbstractState.
        """
        return self._last_state

    def get_last_nonremote_state(self):
        u"""
        Return preveous non-remote state.

        Return:
            Heir of AbstractState.
        """
        return self._last_nonremote_state

    def switch_to_last_state(self):
        u"""
        Change current state to previous state.
        """
        self.set_state(self._last_state.get_name(), prev=True)

    def switch_to_failure(self):
        u"""
        Switching from current state to failure state.
        """
        if not self.is_current_state_failure():
            self.set_state(self._fail_state.get_name())

    def is_current_state_failure(self):
        u"""
        Check current state. Return True if current state is FAILURE else False.
        """
        return self._cur_state is self._fail_state


# class MovePermissionProcessor(object):
#     def __init__(self):
#         self.__sources_dict = dict()
#
#     def add_message(self, message):
#         u"""
#         Add message for watching.
#         """
#         self.__sources_dict[message.source] = message.permission
#
#     def is_work_permitted(self):
#         u"""
#         Return permission flag. If all of sources have True flag that return True else False.
#
#         Return:
#             bool.
#         """
#         for permission in self.__sources_dict.values():
#             if not permission:
#                 return False
#
#         return True


class StateMap(object):
    u"""
    Map that holds state name as a key and state instance as a value.
    """
    def append(self, instances):
        u"""
        Add instances to map by his key.

        Args:
            instances (list): list of heir of AbstractState.
        
        Raises:
            AttributeError: if state name already in map.
        """
        if not isinstance(instances, list):
            instances = [instances]

        for instance in instances:
            if instance.get_name() in self.__dict__:
            #if self.__dict__.has_key(instance.get_name()):  #python3 depricated has_key
                raise AttributeError('State with name `{}` already EXIST!'.format(
                    instance.get_name()
                ))
            self.__dict__[instance.get_name()] = instance

    def get(self, state_name):
        u"""
        Return state instance. If state_name does not exist as key return None.

        Args:
            state_name (str).

        Return:
            Heir of AbstractState or None.
        """
        return  self.__dict__.get(state_name, None)


class TransitionFaildError(Exception):
    pass
