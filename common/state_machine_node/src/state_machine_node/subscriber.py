#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

from std_msgs.msg import Header

# Type of warning when did not receive data from topic
NO_DATA_WARNING = 1 
# Type of warning when data is delayed
TIMEOUT_WARNING = 2


class SubscriberJam(object):
    u"""
    Class that create subscribtion for specific topic and check timeouts for them.

    It use class _Subscriber for callback processing.

    Examples:
        subscribers = SubscriberJam()

        # Add new subscriber
        subscribers.add_subscriber('topic_name_1', SomeMessage, 'message_name')
        # Refer to message next way
        subscribers.message_name

        # Add subscriber and callback
        # Define callback
        def timeout_callback(text, timeout_type):
            timeout_descr = 'Data not shoving up' if timeout_type == NO_DATA_WARNING else 'Data delayed'
            print('Got next {}: {}'.format(text, timeout_descr))

        # Add new subscriber
        subscribers.add_subscriber('topic_name_2', SomeMessage, 'message_name_2', 1.0, 1.0, timeout_callback, text='TIMEOUT')

        # Check for timeout expired
        subscribers.process()
    """

    def __init__(self):
        self.__inner_dict = dict()

    def add_subscriber(self,
                       topic_name, 
                       class_object, 
                       variable_name, 
                       timeout_not_shoving_up_data=None, 
                       timeout_data_obsolescence=None,
                       timeout_event_handler=None,
                       **kwargs):
        u"""
        Subscribe to topic.

        Args:
            topic_name (str): name of topic for subscribing.
            class_object: topic's message class.
            variable_name: SubscriberJam attribute name where you can get subscriber's message.
            timeout_data_obsolescence (float or None): timeout for checking data obsolescence.
            timeout_not_shoving_up_data (float or None): timeout in case if data not shoving up.
            timeout_event_handler (function): function that will be call if timeout catch.
            **kwargs: dict of argumets that will pass to timeout_event_handler.

        Raises:
            AttributeError: if variable_name exist in __inner_dict.
        """
        if variable_name in self.__inner_dict:
            raise AttributeError('Instance already has variable with name {}'.format(variable_name))
        
        self.__inner_dict[variable_name] = _Subscriber(topic_name=topic_name, 
                                                       class_object=class_object, 
                                                       timeout_data_obsolescence=timeout_data_obsolescence,
                                                       timeout_not_shoving_up_data=timeout_not_shoving_up_data,
                                                       timeout_event_handler=timeout_event_handler, 
                                                       **kwargs)

    def process(self):
        u"""
        Check every subscriber for timeout.
        """
        for value in self.__inner_dict.values():
            value.check()

        for value in self.__inner_dict.values():
            if value.get_message() is None:
                return False

        return True
    
    def __getitem__(self, key):
        return self.__get_data(key)

    def __getattr__(self, attr):
        return self.__get_data(attr)

    def __get_data(self, attr):
        u"""
        Return data from __inner_dict.

        Args:
            attr (str).
        
        Return:
            object.
        """
        if attr not in self.__inner_dict:
            raise AttributeError('There is no attribute {}'.format(attr))
        data = self.__inner_dict[attr]
        return data.get_message()


class _Subscriber(object):
    u"""
    This class subscribe to special topic and do some work.

    If `timeout_data_obsolescence`, `timeout_not_shoving_up_data` and `timeout_event_handler` is set 
    than received message timeout checks in method `check`.
    """
    def __init__(self, 
                 topic_name, 
                 class_object,
                 timeout_not_shoving_up_data, 
                 timeout_data_obsolescence,
                 timeout_event_handler,
                 **kwargs):

        self.message = None

        if (
            (not timeout_data_obsolescence and timeout_not_shoving_up_data) 
            or (timeout_data_obsolescence and not timeout_not_shoving_up_data)
        ):
            raise AttributeError(
                'You should set both timeout_data_obsolescence and timeout_not_shoving_up_data')

        # Если установлен таймаут
        if timeout_data_obsolescence and timeout_not_shoving_up_data:
            # Задержка данных таймаут
            self.timeout_data_obsolescence = timeout_data_obsolescence
            # Непоявление данных таймаут
            self.timeout_not_shoving_up_data = timeout_not_shoving_up_data
            self.timeout_event_handler = timeout_event_handler
            if timeout_data_obsolescence and not timeout_event_handler:
                raise AttributeError('You should set both timeout and timeout_event_handler')
            self.timestamp = rospy.get_time()
            self.kwargs = kwargs
            self.check = self.__check_timeout
            self.callback = self.__callback_with_timeout
        # При отсутствии таймаута
        else:
            if not timeout_data_obsolescence and timeout_event_handler:
                raise AttributeError('You should set both timeout and timeout_event_handler')
            self.check = self.__check_no_timeout
            self.callback = self.__callback   

        rospy.Subscriber(topic_name, class_object, self.callback)

    def __callback_with_timeout(self, message):
        u"""
        Message receiver.

        Args:
            message (ROS message).
        """
        self.timestamp = rospy.get_time() 
        self.message = message

    def __callback(self, message):
        self.message = message 
    
    def __check_no_timeout(self):
        return
    
    def __check_timeout(self):
        u"""
        Call timeout_event_handler if timeout.
        """
        message = self.message

        timeout = self.timeout_not_shoving_up_data if message is None else self.timeout_data_obsolescence
        timeout_type = NO_DATA_WARNING if message is None else TIMEOUT_WARNING
        if rospy.get_time() - self.timestamp > timeout:
            complete_kwargs = dict(self.kwargs)
            complete_kwargs['timeout_type'] = timeout_type
            self.timeout_event_handler(**complete_kwargs)
        
    def get_message(self):
        u"""
        Return last message.

        Return:
            message.
        """
        return self.message
