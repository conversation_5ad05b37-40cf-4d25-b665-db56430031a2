#!/usr/bin/env python
PKG='state_machine_node'

import rospy
import sys
import time
import unittest

from state_machine_node.state_machine import StateMachine


FIRST_STATE = 'first_state'
SECOND_STATE = 'second_state'
BAD_STATE = 'bad_state'
NOT_BAD_STATE = 'not_bad_state'
FAIL_STATE = 'fail'


class MyAbsState(object):
    def __init__(self):
        self.prev_state_saving_flag = True

    def on_transition_to(self):
        pass

    def on_transition_from(self):
        pass
        

class FirstState(MyAbsState):
    def __init__(self, state_machine):
        super(FirstState, self).__init__()
        self.node = state_machine

    def do_work(self):
        self.node.set_state(SECOND_STATE)

    def get_name(self):
        return FIRST_STATE
        
        
class SecondState(MyAbsState):
    def  __init__(self, state_machine):
        super(SecondState, self).__init__()
        self.node = state_machine

    def do_work(self):
        self.node.set_state(FIRST_STATE)

    def get_name(self):
        return SECOND_STATE


class NotBadState(MyAbsState):
    def  __init__(self, state_machine):
        super(NotBadState, self).__init__()
        self.node = state_machine

    def do_work(self):
        self.node.set_state(FIRST_STATE)

    def get_name(self):
        return NOT_BAD_STATE


class BadState(MyAbsState):
    def  __init__(self, state_machine):
        super(BadState, self).__init__()
        self.node = state_machine

    def on_transition_to(self):
        raise Exception()
    def do_work(self):
        self.node.set_state(FIRST_STATE)

    def get_name(self):
        return BAD_STATE


class FailState(MyAbsState):
    def  __init__(self, state_machine):
        super(FailState, self).__init__()
        self.node = state_machine

    def do_work(self):
        pass

    def get_name(self):
        return FAIL_STATE


class NodeClass1(object):
    def __init__(self):
        self.permission = True
        self.check_data_flag = True
        self.prohibit_state_flag = False
        self.transition_failed_process_flag = False
        self.sm = None

    def set_state_machine(self, sm):
        self.sm = sm

    def set_state(self, new_state):
        self.sm.set_state(new_state)

    def log(self, *args, **kwargs):
        pass

    def subs_process(self):
        pass

    def get_move_permission(self):
        return self.permission

    def check_data(self):
        return self.check_data_flag

    def do_prohibit_state(self):
        self.prohibit_state_flag = True

    def handle_internal_error(self, *args, **kwargs):
        pass

    def do_work_failed(self):
        pass

    def do_work_after(self):
        pass

    def publish_status(self):
        pass

    def mark_timestamp(self):
        pass

    def transition_failed_process(self):
        self.transition_failed_process_flag = True

    def set_move_permission(self, permission):
        self.permission = permission

    def set_check_data_return(self, flag):
        self.check_data_flag = flag

    def did_prohibit_state(self):
        return self.prohibit_state_flag

    def did_transition_failed_process(self):
        return self.transition_failed_process_flag


class TestStateMachine(unittest.TestCase):
    def test_do_work(self):
        node = NodeClass1()

        first_state = FirstState(node)
        second_state = SecondState(node)
        fail_state = FailState(node)
        states = [second_state]

        sm = StateMachine(node, first_state, fail_state, states)
        node.set_state_machine(sm)

        sm.do_work()
        self.assertEqual(sm.get_current_state(), second_state)
        sm.do_work()
        self.assertEqual(sm.get_current_state(), first_state)
    
    def test_move_permission(self):
        node = NodeClass1()

        first_state = FirstState(node)
        second_state = SecondState(node)
        fail_state = FailState(node)
        states = [second_state]

        sm = StateMachine(node, first_state, fail_state, states)
        node.set_state_machine(sm)

        node.set_move_permission(False)
        sm.do_work()
        self.assertTrue(node.did_prohibit_state())

    def test_check_data(self):
        node = NodeClass1()

        first_state = FirstState(node)
        second_state = SecondState(node)
        fail_state = FailState(node)
        states = [second_state]

        sm = StateMachine(node, first_state, fail_state, states)
        node.set_state_machine(sm)

        node.set_check_data_return(False)
        sm.do_work()
        self.assertTrue(node.did_prohibit_state())
    
    def test_bad_transition(self):
        node = NodeClass1()

        first_state = NotBadState(node)
        bad_state = BadState(node)
        fail_state = FailState(node)
        states = [bad_state]

        sm = StateMachine(node, first_state, fail_state, states)
        node.set_state_machine(sm)

        sm.do_work()
        self.assertTrue(node.did_transition_failed_process())

    def test_switch_to_fail_state(self):
        node = NodeClass1()

        first_state = FirstState(node)
        second_state = SecondState(node)
        fail_state = FailState(node)
        states = [second_state]

        sm = StateMachine(node, first_state, fail_state, states)
        # node.set_state_machine(sm)

        sm.switch_to_failure()
        self.assertTrue(sm.is_current_state_failure())


if __name__ == '__main__':
    import rosunit
    rosunit.unitrun(PKG, 'asm', TestStateMachine)
