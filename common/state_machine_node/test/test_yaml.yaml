# Настройки топиков
topics:
  driver_out: 'driver_out'
  cat_feedback: '/st/cat_fb'
  speed_ctrl_stopped: 'speed_ctrl_stopped'
  planned_route: 'planned_route'
  moving_error: 'moving_error'
  internal_error: 'internal_error'
  planner_status: 'planner_status'
  planner_stoppage: 'planner_stoppage'
  kobus_state: 'kobus_state'
  state: 'state'
  permission: 'permission'
  jacks_control_speed: 'jacks_control_speed'
  jacks_state: 'jacks_state'
  leveler_status: 'leveler_status'
  platform_level: 'platform_level'
  kobus_platform_level: 'platform_level'
  main_mode: 'main_mode'
  manual_state: 'manual_state'
  main_action: 'main_action'
  planner_action: 'planner_action'
  tower_control: 'tower_control'
  tower_state: 'tower_state'
  tower_controller_status: 'tower_controller_status'
  tower_action: 'tower_action'
  drive_drill_mode: 'drive_drill_mode'
  drive_drill_mode_remote: 'drive_drill_mode_remote'
  progress_topic: 'progress_topic'
  driller_action: 'driller_action'
  shaft_action: 'shaft_action'
  driller_status: 'driller_status'
  shaft_status: 'shaft_status'
  drill_state: 'drill_state'
  driller_out: 'driller_out'
  drill_actuator: 'drill_actuator'
  fork_state: 'fork_state'
  fork_control: 'fork_control'
  fork_switch_state: 'fork_switch_state'
  pressure_state: 'pressure_state'
  vibration_state: 'vibration_state'
  compressor_control: 'compressor_control'
  rod_changer_out: 'rod_changer_out'
  locker_out: 'locker_out'
  remote_drilling: 'remote_drilling'
  remote_moving: 'remote_moving'
  tower_switch_state: 'tower_switch_state'
  arm_action: 'arm_action'
  arm_control: 'arm_control'
  arm_controller_status: 'arm_controller_status'
  arm_switch_state: 'arm_switch_state'
  emergency_stop: 'emergency_stop'
  carousel_control: 'carousel_control'
  carousel_controller_status: 'carousel_controller_status'
  carousel_controller_action: 'carousel_controller_action'
  carousel_switch_state: 'carousel_switch_state'

  cat_control_direct: 'cat_control_direct'
  cat_control_smart: 'cat_control_smart'
  cat_control: 'cat_control'
  drill_control_direct: 'drill_control_direct'
  drill_control_smart: 'smart_drill_control'
  tower_control_direct: 'tower_control_direct'
  leveler_control_direct: 'leveler_control_direct'
  arm_control_direct: 'arm_control_direct'
  fork_control_direct: 'fork_control_direct'
  compressor_control_direct: 'compressor_control_direct'
  carousel_control_direct: 'carousel_control_direct'
  dust_collector_control_direct: 'dc_control_direct'

  drill_angle: "drill_angle"
  drill_rpm: "drill_rpm"

  wrench_state: "wrench_state"
  wrench_control: "wrench_control"
  wrench_control_direct: "wrench_control_direct"

  dust_collector_control: 'dust_collector_control'

  calib_tower_switch: 'calib_tower_switch'
  main_sm_drill_out: 'main_sm_drill_out'

  rod_changer_status: "rod_changer_status"
  rod_changer_action: "rod_changer_action"

  wrench_controller_status: "wrench_controller_status"
  wrench_controller_action: "wrench_controller_action"

  laser_data: "laser_data"
  counter_data: "counter_data"

  remote_type: 'remote_type'
  system_status: 'system_status'
  internal_report: 'internal_report'
  relative_depth: 'relative_depth'
  jack_rangefinder_data: 'jack_rangefinder_data'

  kobus_rotation_speed: 'kobus_rotation_speed'

  last_finished_holeid: 'last_finished_holeid'

  pwm_enabled: 'pwm_enabled'

  fork_length_data: 'fork_length_data'

  lamp_control: "lamp_control"


# Общая погрешность
EPS: 0.000001
# Допустимая задержка входных данных нод секунды
MSG_TTL: 0.5

# Допустимое ожидание нового стейта для возвращения из failure секунды
new_state_TTL: 3

# Настройки отчетов состояния нод и системы
Reports:
  Not_working:
    Initialization_exception: 100
    Runtime_exception: 101
  Info:
    Working: 200
    Initializing: 201
    Shutting down: 202
    Shut_down: 203
    Starting: 204
    Started: 205
  Warnings:
    Internal_warning: 401
    Connecting: 402
    Reconnecting: 402
  Errors:
    Internal_error: 500
    Initialization_failed: 501
    Connection_failed: 502
    Incoming_data_delay: 503
    Report_delay: 504
  Critical:
    Action_failed: 600
    Hardware_malfunction: 601
    Bad_conditions: 602
  Emergency:
    pwmbox_failed: 700


SYSTEM_STATUS:
  WARNING: 400
  ERROR: 401
  CRITICAL: 402


system_flags:
  arm_present: False

TestNode:
  rate: 30
  debug: true
  test_param: 10
