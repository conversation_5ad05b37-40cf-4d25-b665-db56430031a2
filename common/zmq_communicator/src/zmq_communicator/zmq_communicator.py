#!/usr/bin/env python
# -*- coding: utf-8 -*-


import zmq
import uuid
import collections

import simplejson
import time

__all__ = (
    'ZmqMsgClient',
    'ZmqMsgServer'
)


TIMEOUT = 2000 # 2 sec
KEEP_ALIVE = "keep_alive"


class ZmqMsgClient(object):
    u"""
    Класс-интерфейс осуществляющий отправку и прием сообщений через ZMQ
    Работает в паре с ZmqMsgServer

    Тип передаваемых через ZMQ сообщений:
        dict(
            vehid {int} - ID машины
            msgid {str} - UUID - уникальный идентификатор сообщения
            last_recv_msgid {srt} - UUID - уникальный идентификатора послед-
                                    него принятого сообщения. Нужно для конт-
                                    роля доставки сообщения на той стороне
            msgtype {str} - тип передаваемого сообщения
            data {PyObj} - передаваемая информация.
        )
    """

    def __init__(self):
        self._zmq_context = zmq.Context()
        self._socket = self._zmq_context.socket(zmq.DEALER)
        self._socket.setsockopt(zmq.LINGER, 0)
        self._socket.setsockopt(zmq.CONFLATE, 1)
        self._socket.setsockopt(zmq.IMMEDIATE, 1)
        self._socket.setsockopt(zmq.SNDTIMEO, TIMEOUT)
        self._socket.setsockopt(zmq.RCVTIMEO, TIMEOUT)
        self._socket.setsockopt(zmq.TCP_KEEPALIVE, 1)
        self._socket.setsockopt(zmq.TCP_KEEPALIVE_IDLE, 300)
        self._socket.setsockopt(zmq.TCP_KEEPALIVE_INTVL, 300)
        self._poller = zmq.Poller()
        self._last_received_message_uuid = None
        self._can_send = True
        self._last_msg_delay = 0

    def initialize(self, protocol, ip, port, client_id):
        u"""
        Инициализация передатчика

        @param ip {str} - IP-адрес сервера
        @param port {int} - порт сервера
        @param protocol {str} - протокол общения ("tcp", "udp", etc.)
        @param client_id {int|str} - идентификатор клиента
        """
        self._client_id = client_id
        ip = ip
        port = port
        self._socket.setsockopt(zmq.IDENTITY, bytes(self._client_id))
        self._socket.connect('%s://%s:%d' % (protocol, ip, port))

    def make_sendable_message(self, msg_type, data, ts):
        u"""
        Создать сообщение для передачи

        @param msg_type {string} - строковое имя сообщения
        @param data {python object} - передаваемые данные
        @param ts {float} - текущий таймстемп

        @return dict
        """

        new_message = dict(
            ts=ts or time.time(),
            delay=self._last_msg_delay,

            client_id=self._client_id,
            msgid=str(uuid.uuid1()),
            last_recv_msgid=self._last_received_message_uuid,
            msgtype=msg_type,
            data=data
        )

        return new_message

    def speak(self, msg_type, msg, ts=None):
        u"""
        Функция отправляет сообщение и ждет ответа

        @param msg_type {str}
        @param msg {PyObj}
        @param ts {float}

        @return tuple(bool, str, PyObj) - Флаг о доставке сообщения,
                                          тип сообщения,
                                          принятое сообщение
        """

        request = self.make_sendable_message(msg_type, msg, ts)

        try:
            self._socket.send_json(request)

            responce = self._socket.recv_multipart()
            responce = simplejson.loads(responce[0])
            delivery_ok = responce['last_recv_msgid'] == request['msgid']
            self._last_received_message_uuid = responce['msgid']
            self._last_msg_delay = time.time() - responce['ts']

            return delivery_ok, responce['msgtype'], responce['data']

        except zmq.Again:
            return False, None, None


class ZmqMsgServer(object):
    u"""
    Класс-интерфейс осуществляющий отправку и прием сообщений через ZMQ
    Работает в паре с ZmqMsgClient

    Тип передаваемых через ZMQ сообщений:
        dict(
            vehid {int} - ID машины
            msgid {str} - UUID - уникальный идентификатор сообщения
            last_recv_msgid {srt} - UUID - уникальный идентификатора послед-
                                    него принятого сообщения. Нужно для конт-
                                    роля доставки сообщения на той стороне
            msgtype {str} - тип передаваемого сообщения
            data {PyObj} - передаваемая информация.
        )
    """

    def __init__(self):
        self._zmq_context = zmq.Context()
        self._socket = self._zmq_context.socket(zmq.ROUTER)
        self._socket.setsockopt(zmq.LINGER, 0)
        # self.client is my socket here
        self._socket.setsockopt(zmq.TCP_KEEPALIVE, 1)
        self._socket.setsockopt(zmq.TCP_KEEPALIVE_IDLE, 300)
        self._socket.setsockopt(zmq.TCP_KEEPALIVE_INTVL, 300)
        self._queues = collections.defaultdict(collections.deque)
        self._sended_messages = dict()
        self._last_received_messages = dict()

    def initialize(self, protocol, ip, port):
        self._socket.bind("%s://%s:%d" % (protocol, ip, port))

    def process_request(self, msg_getter):
        u"""
        Обработчик запросов от клиентов.
        Принимает входящее сообщение в виде запроса, проверяет доставку
        предыдущего сообщения, отправляет сообщение от сервера или keep_alive

        @param msg_getter {function} - функция, возвращающая сообщение, которое
                                       должно быть отправлено.
                                       Формат возвращаемого значения должен быть
                                       tuple(msg_type{str}, msg{PyObj})
        @return tuple({bool} - Флаг, указывающий доставлено ли предыдущее
                               сообщение
                      {str|int} - ID машины от которой пришло сообщение
                      {str} - тип сообщения
                      {PyObj} - принятое сообщение
                      {float} - задержка передачи и обработки сообщения
                     )
        """
        if self._socket.poll(TIMEOUT, zmq.POLLIN):
            _addr, request = self._socket.recv_multipart()
            request = simplejson.loads(request)
            addr = request['client_id']
            ts = request['ts']
            delay = request['delay']
            req_msg_type = request['msgtype']
            req_data = request['data']

            last_message_uuid = self._sended_messages.get(addr)

            no_message = last_message_uuid is None
            req_msg_id = request['last_recv_msgid']
            delivery_ok = no_message or last_message_uuid == req_msg_id

            msg_type, data = msg_getter(str(addr), req_msg_type, req_data)
            req_msg_id = request['msgid']
            responce = self.make_sendable_message(addr,
                                                req_msg_id,
                                                msg_type or KEEPALIVE,
                                                data,
                                                ts)
            self._sended_messages[addr] = responce['msgid']

            responce = simplejson.dumps(responce).encode()
            self._socket.send_multipart((_addr, responce))

            return delivery_ok, addr, req_msg_type, req_data, delay

    def make_sendable_message(self, addr, last_msg_id, msg_type, data, ts):
        u"""
        Создать сообщение для передачи

        @param addr {str|int} - ID машины
        @param last_msg_id {str} - UUID принятого сообщения
        @param msgtype {string} - строковое имя сообщения
        @param data {python object} - передаваемые данные
        @param ts {float} - таймстемп из принятого сообщения

        @return dict
        """

        new_message = dict(
            vehid=addr,
            msgid=str(uuid.uuid1()),
            last_recv_msgid=last_msg_id,
            msgtype=msg_type,
            data=data,
            ts=ts
        )

        return new_message

    def add_message(self, address, msg_type, msg):
        self._queues[address].append((msg_type, msg))


# test
# if __name__ == "__main__":
#     import multiprocessing
#     import time
#
#     def client_worker():
#         client = ZmqMsgClient()
#         client.initialize("tcp", "127.0.0.1", 50000, client_id=1)
#         time.sleep(2)
#         print("started client")
#         for i in range(10):
#             msg = client.speak(KEEP_ALIVE, None)
#             time.sleep(0.01)
#
#     server = ZmqMsgServer()
#     server.initialize("tcp", "0.0.0.0", 50000)
#
#     msg_getter = lambda x, y: (KEEP_ALIVE, None)
#
#     client_thread = multiprocessing.Process(target=client_worker)
#     client_thread.start()
#
#     while client_thread.is_alive():
#         msg = server.process_request(msg_getter)
#         print('msg: ', msg)
