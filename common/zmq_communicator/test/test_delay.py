#!/usr/bin/env python
# -*- coding: utf-8 -*-

import zmq
import time
import multiprocessing
import zmq_communicator.zmq_communicator as zmq_communicator


KEEP_ALIVE = 'keep_alive'


def client_worker():
    client = zmq_communicator.ZmqMsgClient()
    client.initialize('tcp', '127.0.0.1', 50000)
    time.sleep(2)
    print('client started')
    for i in range(20):
        client.speak(KEEP_ALIVE, None)
        time.sleep(0.001)


server = zmq_communicator.ZmqMsgServer()
server.initialize('tcp', '0.0.0.0', 50000)


client_thread = multiprocessing.Process(target=client_worker)
client_thread.start()

msg_getter = lambda x: (KEEP_ALIVE, None)

delay = 0.5
print('Expected delay: ~{}\n'.format(delay))

while client_thread.is_alive():
    time.sleep(delay)
    msg = server.process_request(msg_getter)
    if msg is not None:
        got_delay = msg[4]
        print('Delay: {}'.format(got_delay))
