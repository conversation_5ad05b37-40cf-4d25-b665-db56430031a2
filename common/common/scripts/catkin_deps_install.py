#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import xml.etree.ElementTree as ET
import itertools
import rospkg
import subprocess
import pip
import argparse


def find_xml(package_path):
    src = package_path
    filename = "package.xml"

    if not os.path.exists(src):
        raise Exception("This directory doesn't contains src")

    package_xml_files = (
        p[0] for p in os.walk(src)
        if filename in p[-1]
    )

    for xml_file_path in package_xml_files:
        path = os.path.join(xml_file_path, filename)
        xml_file = ET.parse(path)
        name = xml_file.getroot().find('name').text
        yield name, xml_file


def find_dependencies_xml(xml):
    depend = 'depend'
    root = xml.getroot()
    dep_tags = ((child.tag, child.text) for child in root)
    return (text for tag, text in dep_tags if depend in tag)


def find_dependencies_rospack():
    rp = rospkg.RosPack()
    pkgs = set(rp.list())
    deps = set()

    for pkg in pkgs:
        try:
            print rp.get_rosdeps(pkg)
        except Exception:
            pass


def find_dependencies_pip():
    p = subprocess.Popen("pip freeze", stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    p.wait()
    (o, e) = p.communicate()

    packages = o.split()
    just_name = [n for _p in o.split() for n in _p.split("==")[::2]]

    return just_name


def find_not_installed(xml_package_path):
    packs, xmls = itertools.izip(*find_xml(xml_package_path))
    deps = list(itertools.chain(*(find_dependencies_xml(xml) for xml in xmls)))
    not_installed = set(deps) - set(rospkg.RosPack().list())

    p = subprocess.Popen("apt list --installed ", stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    apt_installed, _ = p.communicate()
    pip_installed = find_dependencies_pip()

    all_find = list(itertools.chain(apt_installed, pip_installed, packs))

    not_installed = (
        deb for deb in not_installed
        if not deb in all_find
    )
    return not_installed


if __name__ == "__main__":

    try:
        xml_package_path = sys.argv[1]

    except IndexError:
        raise Exception("the SRC variable is not specified. Please call this script like 'catkin_deps_install <path_to_src_folder>'")

    if "ROS_DISTRO" not in os.environ.keys():
        print "ROS_DISTRO environment varaible doesn't set. Please call /opt/ros/<your distro>/setup.bash"
        sys.exit()

    print "Found %d packages to install" % len(list(find_not_installed(xml_package_path)))

    p = subprocess.Popen(". /opt/ros/kinetic/setup.sh", shell=True)
    p.wait()

    # try apt install
    for package in find_not_installed(xml_package_path):
        print "Try to install package %s via apt" % package

        p = subprocess.Popen("apt install -y %s" % package, shell=True)
        p.wait()

    # try ros DISTRO
    for package in find_not_installed(xml_package_path):
        print "Try to install package %s from apt ros-bla-bla-bla" % package

        p = subprocess.Popen("apt install -y ros-%s-%s" % (os.environ["ROS_DISTRO"], package), shell=True)
        p.wait()

    # try pip
    for package in find_not_installed(xml_package_path):
        print "Try to install package %s from pip" % package

        p = subprocess.Popen("pip install %s" % package, shell=True)
        p.wait()

    p = subprocess.Popen("catkin_make", shell=True)
    p.wait()

    p = subprocess.Popen(". devel/setup.sh", shell=True)
    p.wait()

    for package in find_not_installed(xml_package_path):
        print "Package: %s not found" % package
