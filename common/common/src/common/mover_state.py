import rospy
from common.geometry import MoverPoint
from std_msgs.msg import Header
from geometry_msgs.msg import Point
from common_msgs.msg import State


class MoverState(object):
    def __init__(self, x=0, y=0, z=0, speed=0, yaw=0, wheel_angle=0, quality=0):
        super(MoverState, self).__init__()
        self.timestamp = rospy.Time.now()
        self.seq = 0
        self.frame_id = ""

        self.position = MoverPoint(
            x=x,
            y=y,
            z=z
        )
        self.speed = speed
        self.yaw = yaw
        self.wheel_angle = wheel_angle
        self.quality = quality

    def from_msg(self, message):
        self.timestamp = message.header.stamp
        self.seq = message.header.seq
        self.frame_id = message.header.frame_id

        self.position = MoverPoint(
            x=message.position.x,
            y=message.position.y,
            z=message.position.z
        )

        self.speed = message.speed
        self.yaw = message.yaw
        self.wheel_angle = message.wheel_angle
        self.quality = message.quality

    def to_msg(self):
        message = State(
            header=Header(
                stamp=rospy.Time.now(),
                frame_id=self.frame_id
            )
        )

        message.position = Point(
            x=self.position.x,
            y=self.position.y,
            z=self.position.z
        )

        message.speed = self.speed
        message.yaw = self.yaw
        message.wheel_angle = self.wheel_angle
        message.quality = self.quality
        return message
