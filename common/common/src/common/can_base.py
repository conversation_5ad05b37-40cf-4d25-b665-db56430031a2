#!/usr/bin/env python
# -*- coding: utf-8 -*-

from __future__ import division

import rospy
import struct
import sys

from can_msgs.msg import Frame
from std_msgs.msg import <PERSON><PERSON>


def get_message_class(params, **kwargs):
    """This function defines class for representing and (de)serializing CAN frames.
    To use it, one has to pass params dictionary in accordance with
    the description below.

    name [str] (optional) - Message short description
    rate [int, couыщгnt per second] (required) - How often will be message sent/received, for non-repeating messages use 0
    id [int] (required) - ID of the frame
    is_extended [bool] (optional, False by default) - Whether frame ID is extended
    dlc [int, 1-8] (required for sending) - Data bytes count
    byte_order [str, 'big' or 'little'] (optional, 'little' by default) - Byte order for multi-byte fields (big endian or little endian)

    data [dict] (required)
      *field_name* [str] (required)
        type    [enum] (required) - Field type, see description below
        byte    [int] (required) - Start byte number
        bit     [int] (required for some types, see below) - Start bit number for corresponding byte
        default [int] (optional) - Default field value, used only for sending
        defines [dict] (optional) - Defines for conveniency
          *define_name*: *value* (optional)

    Field types description:
        t - one-bit bool (REQUIRES 'bit' FIELD)
        ti - one-bit inverted bool (REQUIRES 'bit' FIELD)
        u[1-8] - multi-bit integer (REQUIRES 'bit' FIELD)
        B - one-byte unsigned int
        H - two-byte unsigned int
        I - four-byte unsigned int

        It is possible to use other types defined in Python struct library without code changing.


    Usage:
        1) Define param file (or build python dict using another method)
        2) Pass it into function and receive class which you can use for
        serializing and deserializing CAN frames according to defined protocol.
    """

    _module = sys.modules[__name__]

    # Check if protocol is written for this version of parser.
    parser_version = params.get('parser_version')
    assert parser_version == 2

    # Allow to override params in arguments. Be careful!
    # Most likely use case: setting source_id for J1939 messages
    params.update(kwargs)

    name = params.get('name')
    class_name = params.get('class_name')
    fields_repr = params.get('data')
    rate = params.get('rate')
    is_extended = params.get('is_extended')
    dlc = params.get('dlc', 8)
    byte_order = params.get('byte_order')

    message_type = params.get('message_type')
    frame_id = params.get('id')

    source_id = params.get('source_id')
    pgn = params.get('pgn')
    # Default priority for messages is 0b110. One can change it by setting "priority" parameter.
    priority = params.get('priority', 0b110)
    packet_num = params.get('packet_num')

    functions = params.get('functions', [])

    # Set up CAN ID filter
    if message_type == 'J1939_general':
        # According to J1939, CAN ID is divided into three parts:
        # Priority (3 bits), PGN (18 bits) and Source ID (8 bits).
        # We compare only source id and pgn.

        assert type(source_id) is int
        assert type(pgn) is int

        filter_func = lambda self, can_id, _: (
            (can_id & 0xFF) == self._source_id and
            (can_id >> 8 & 0x3FFFF == self._pgn)
        )

        frame_id = (priority << 26) + (pgn << 8) + source_id

    elif message_type == 'J1939_multipacket':
        # In addition to source id and pgn we have to check first byte
        # which represents packet number in multi-packet message

        assert type(source_id) is int
        assert type(pgn) is int
        assert type(packet_num) is int

        filter_func = lambda self, can_id, data: (
            (can_id & 0xFF) == self._source_id and
            (can_id >> 8 & 0x3FFFF == self._pgn) and
            len(data) > 0 and
            (struct.unpack_from('B', data)[0] == self._packet_num)
        )

        frame_id = (priority << 26) + (pgn << 8) + source_id

    else:
        # Here we compare whole CAN ID.

        assert type(frame_id) is int

        filter_func = lambda self, can_id, _: can_id == self._frame_id

    for field in fields_repr:
        if type(fields_repr[field].get('resolution', 1)) == str:
            res = fields_repr[field]['resolution'].split('/')
            fields_repr[field]['resolution'] = int(res[0]) / int(res[1])


    class Message(object):
        """This class is used for representing and (de)serializing CAN frames.
        """
        _name = name
        _class_name = class_name
        _fields_repr = fields_repr
        _fields = list(fields_repr.keys())
        _rate = rate
        _frame_id = frame_id
        _is_extended = is_extended
        _dlc = dlc
        _byte_order = byte_order
        _byte_order_modifier = '>' if _byte_order == 'big' else '<'

        _message_type = message_type
        _source_id = source_id
        _pgn = pgn
        _priority = priority
        _packet_num = packet_num
        _filter_func = filter_func

        _functions = functions

        def __init__(self):
            """Inits message class and fills attributes with defauls values.
            """
            self.clear()
            self._build_format_string()

            # For checking message time
            self.header = Header()

        def clear(self):
            """Fills class attributes with default values.
            """
            for field in self._fields_repr:
                if 'default' in self._fields_repr[field]:
                    value = self._fields_repr[field]['default']
                else:
                    value = 0
                setattr(self, field, value)

        def safe_clear(self):
            """Fills class attributes with default values. Do not change fields with attribute do_not_clear.
            """
            for field in self._fields_repr:
                if self._fields_repr[field].get('do_not_clear'):
                    value = getattr(self, field)
                elif 'default' in self._fields_repr[field]:
                    value = self._fields_repr[field]['default']
                else:
                    value = 0
                setattr(self, field, value)

        def serialize(self):
            """Serializes and returns "Frame" message in accordance with the settings.

            Returns "Frame" message containing last data.
            """
            _bytes = bytearray(b'\x00' * 8)  # Although dlc can be less than 8, "Frame" msg requires exactly 8
            for field in self._fields:
                field_repr = self._fields_repr[field]

                value = getattr(self, field)
                if field_repr['type'][-1] not in ('s', 'c'):
                    value -= field_repr.get('offset', 0)
                    value /= field_repr.get('resolution', 1)
                    value = int(value)

                if field_repr['type'] in ['t', 'ti']:  # We have to pack manually (one-bit bool)
                    assert value in (0, 1)

                    if field_repr['type'] == 'ti':
                        value = 1 - value

                    _bytes[field_repr['byte'] - 1] |= value << (field_repr['bit'] - 1)

                elif field_repr['type'][0] == 'u':  # We have to pack manually (unsigned int <= 8 bits)
                    length = int(field_repr['type'][1:])

                    assert length in range(1, 9)
                    assert value in range(2**length)

                    _bytes[field_repr['byte'] - 1] |= value << (field_repr['bit'] - 1)

                elif field_repr['type'] == 'T': # 3-byte uint
                    packed = struct.pack(self._byte_order_modifier + 'i', value)
                    if self._byte_order_modifier == '>':
                        packed = packed[1:]
                    else:
                        packed = packed[:-1]
                    _bytes[field_repr['byte'] - 1: field_repr['byte'] - 1 + len(packed)] = packed

                else:  # We will use python struct library
                    packed = struct.pack(self._byte_order_modifier + field_repr['type'], value)
                    _bytes[field_repr['byte']-1 : field_repr['byte']-1+len(packed)] = packed

            for func in self._functions:
                _bytes = getattr(_module, func['name'])(self, _bytes, **func['args'])

            frame = Frame()
            frame.id = self._frame_id
            frame.dlc = self._dlc
            frame.data = tuple(_bytes)
            frame.is_extended = self._is_extended

            frame.header.stamp = rospy.get_rostime()

            return frame

        def _build_format_string(self):
            """Internal function for generating struct format string to speed up data parsing.
            """
            def bytes_number(x):
                if x[-1:] in ('s', 'c'):
                    test_val = b'0'
                elif x == 'T':
                    return 3
                else:
                    test_val = 0

                return len(struct.pack(x, test_val))

            format_string = self._byte_order_modifier
            reduced = []
            format_names = []
            bit_fields = []

            fields = [
                {
                    'name': field,
                    'type': value.get('type'),
                    'byte': value.get('byte'),
                    'bit': value.get('bit'),
                } for field, value in list(self._fields_repr.items())
            ]

            fields = sorted(fields, key=lambda x: x['byte'])

            for field in fields:
                if field['type'][0] in ('t', 'u'):
                    bit_fields.append(field)

                if len(reduced) == 0 or field['byte'] > reduced[-1]['byte']:
                    if len(reduced) == 0:
                        blank_bytes = field['byte'] - 1
                    else:
                        blank_bytes = (
                            field['byte'] -
                            reduced[-1]['byte'] -
                            bytes_number(reduced[-1]['type'])
                        )

                    assert blank_bytes >= 0

                    format_string += 'B' * blank_bytes
                    format_names += ['_blank'] * blank_bytes

                    if field['type'][0] in ('t', 'u'):
                        name = '_byte%s' % field['byte']
                        reduced.append({
                            'name': name,
                            'type': 'B',
                            'byte': field['byte'],
                        })
                        format_string += 'B'
                        format_names.append(name)

                    elif field['type'][0] == 'T': # 3-byte uint
                        reduced.append(field)
                        format_string += '3s'
                        format_names.append(field['name'])
                    else:
                        reduced.append(field)
                        format_string += field['type']
                        format_names.append(field['name'])

            self._format_string = format_string
            self._format_names = format_names
            self._bit_fields = bit_fields

        def deserialize(self, message):
            """Parses "Frame" message and sets corresponding class attributes.

            Returns True in case of success.
            """
            if not self._filter_func(message.id, message.data):
                return False

            data_array = struct.unpack_from(self._format_string, message.data)
            for num, name in enumerate(self._format_names):
                value = data_array[num]
                if (name != '_blank' and not name.startswith('_byte') and
                    self._fields_repr[name]['type'][-1:] not in ('s', 'c')):
                    if self._fields_repr[name]['type'][-1:] == 'T':
                        if sys.version_info < (3, 0):
                            value = sum([ord(x) << i * 8 for i, x in enumerate(value)])
                        else:
                            value = sum([x << i * 8 for i, x in enumerate(value)])
                    value *= self._fields_repr[name].get('resolution', 1)
                    value += self._fields_repr[name].get('offset', 0)

                setattr(self, name, value)

            for field in self._bit_fields:
                name = '_byte%s' % field['byte']
                byte = getattr(self, name)

                if field['type'] in ('t', 'ti'):
                    bit_count = 1
                elif field['type'][0] == 'u':
                    bit_count = int(field['type'][1:])
                else:
                    assert False

                value = byte >> (field['bit'] - 1) & ((1 << bit_count) - 1)
                value *= self._fields_repr[field['name']].get('resolution', 1)
                value += self._fields_repr[field['name']].get('offset', 0)

                setattr(self, field['name'], value)

            self.header = message.header  # For checking message time

            return True

        def dict(self):
            return {field: getattr(self, field) for field in list(self._fields_repr.keys())}

        def __repr__(self):
            return str(self.dict())

    # If there are defines in some fields, we make them visible as class properties
    for field in fields_repr:
        if 'defines' in fields_repr[field]:
            for name in fields_repr[field]['defines']:
                setattr(Message, name, fields_repr[field]['defines'][name])

    return Message


# Auxiliary functions
def counter_func(message, data, field, max_value):
    current = getattr(message, field)
    setattr(message, field, (current + 1) % (max_value + 1))

    return data

def xbr_checksum_func(message, data):
    checksum = sum(data) + (
            (message._frame_id & 0xFF) +
            ((message._frame_id >> 8) & 0xFF) +
            ((message._frame_id >> 16) & 0xFF) +
            ((message._frame_id >> 24) & 0xFF)
    )
    xbr_message_checksum = ((checksum >> 4) + checksum) & 0x0F
    data[-1] += xbr_message_checksum << 4

    return data
