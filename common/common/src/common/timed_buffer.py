#!/usr/bin/env python
# -*- coding: utf-8 -*-

from collections import namedtuple

MsgStamped = namedtuple("MsgStamped", ["time", "msg"])

class TimedBuffer(object):
    def __init__(self, timeout, logger=None):
       self._buf = [] # (time, message)
       self._timeout = timeout
       self._log = logger

    def add(self, time, msg):
        self._remove_outdated(time)
        if len(self._buf) > 0 and time <= self._buf[-1].time:
            #self._log('MSGBUFFER: old time (last/new) received: msg=%s, time=%.3f, %.3f' % \
            #(type(msg).__name__, self._buf[-1].time.to_sec(), time.to_sec()))
            return
        new_value = MsgStamped(time, msg)
        self._buf.append(new_value)

    def get_buf(self, time=0):
        #self._remove_outdated(time)
        ret_buf = list(self._buf)
        return ret_buf

    def _remove_outdated(self, time):
        while len(self._buf) > 0:
            # remove outdated values
            if self._buf[0].time + self._timeout > time:
                break
            del self._buf[0]        

    def clear(self):
        self._buf = []

    def get_last_before(self, time):
        if len(self._buf) == 0:
            return None

        buf = self.get_buf()
        item = None
        while True:
            if len(buf) == 0:
                break
            if buf[-1].time <= time:
                item = buf[-1]
                break
            del buf[-1]

        return item

    def get_first_after(self, time):
        if len(self._buf) == 0:
            return None

        buf = self.get_buf()
        item = None
        while True:
            if len(buf) == 0:
                break
            if buf[0].time >= time:
                item = buf[0]
                break
            del buf[0]

        return item

    def get_count(self):
        return len(self._buf)
