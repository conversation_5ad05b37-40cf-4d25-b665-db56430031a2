<?xml version="1.0"?>
<launch>
    <arg name="map_csize" default="10" />
    <arg name="height_map_csize" default="0.3" />
    <arg name="kernel_size" default="10" />
    <arg name="factor" default="8" />
    <arg name="path_to_map" default='' />

    <node name="create_height_map" pkg="common" type="height_map.py" output="screen" >
        <param name="map_csize" value="$(arg map_csize)" />
        <param name="height_map_csize" value="$(arg height_map_csize)" />
        <param name="kernel_size" value="$(arg kernel_size)" />
        <param name="factor" value="$(arg factor)" />
        <param name="path_to_map" value="$(arg path_to_map)" />
    </node>
</launch>
