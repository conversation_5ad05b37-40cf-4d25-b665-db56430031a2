#!/usr/bin/env python
import math
import unittest
import rostest

from common import geometry


class TestCommonGeometry(unittest.TestCase):

    def test_norm_angle_0_2PI(self):
        expected = math.pi / 2.0
        input = -1*(3.0 / 2.0) * math.pi
        result = geometry.norm_angle_0_2PI(input)
        self.assertEqual(result, expected)

PKG = 'common'
NAME = 'geometry_tests'
if __name__ == '__main__':
    rostest.unitrun(PKG, NAME, TestCommonGeometry)