#!/bin/bash

# Name the Docker image
IMAGE_NAME="iplace_app"
CONTAINER_NAME="iplace_container"

# Project root directory (assumes `docker` is a subfolder of the project root)
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)

# Allow X11 connections
xhost +local:docker

# Run the Docker container
docker run -it --rm \
    --init \
    --privileged \
    --name $CONTAINER_NAME \
    --network host \
    -e DISPLAY=$DISPLAY \
    -e QT_X11_NO_MITSHM=1 \
    -v /tmp/.X11-unix:/tmp/.X11-unix \
    -v "$PROJECT_ROOT:/project" \
    -v /etc/localtime:/etc/localtime:ro \
    -v /etc/timezone:/etc/timezone:ro \
    $IMAGE_NAME bash -c "cd /project/core && python3 main.py dev_mode"
    # $IMAGE_NAME bash -c "source /opt/ros/noetic/setup.bash && cd /project/core && python3 main.py dev_mode"

