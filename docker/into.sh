#!/bin/bash

# Name the Docker container
CONTAINER_NAME="iplace_container"

# Check if the container is already running
if docker ps --filter "name=$CONTAINER_NAME" --format "{{.Names}}" | grep -q "$CONTAINER_NAME"; then
    echo "Attaching to running container: $CONTAINER_NAME"
    docker exec -it $CONTAINER_NAME bash
else
    echo "Error: Container '$CONTAINER_NAME' is not running."
    echo "Please start the container with './run.sh' and try again."
    exit 1
fi
