Для того, чтобы редактировать переводы, нужно использовать Qt Linguist, для чего необходимо установить Qt (скачать с официального сайта установочные файлы и следовать приложенным инструкциям).

### Описание файла .pro
В файле проекта core/main.pro в первой строке после “=” выписаны через пробел названия файлов (относительно .pro-файла) с источниками строк для перевода.
Это должны быть исполяемые .py-файлы или файлы .ui, а также файлы с внешними источниками для переводов .tri.

Во второй строке описаны файлы переводов. Они создаются автоматически, ничего делать не нужно кроме как указать названия, которые будут однозначно указывать, какой перевод в них следует ожидать.
```
SOURCES = <python runnable>.py <form>.ui <external_sources>.tri
TRANSLATIONS = es_ES.ts ru_RU.ts
```

В файле, где происходит инициализация приложения (core/main.py), добавлен объект переводчика.
```
from PyQt5.QtCore import QTranslator
< other code >
# initialize translator
translator = QTranslator()
# load translation
translator.load('ru_RU.qm')
# add translator to application
QApplication.installTranslator(translator)
```
Переводчик загружается с помощью файла трансляции с расширением .qm (это бинарный упакованный перевод, сделанный из .ts файла, который мы указали в файле проекта).

### Редактирование исходных файлов
Предположим, что источники для переводов у нас содержатся в файлах вида и контроллера в виде модулей python.
Объекты класса QWidget и его дочерних содержат метод tr(str), который в рантайме подставляет перевод данной строки str (в соотвествии с загруженным переводчиком).

Соответственно чтобы строковая константа была переведена на другие языки, нужно указать Qt, где ее искать.
Есть два распространенных способа:
```
# если строка находится внутри класса-наследника QWidget
self.tr("String to be translated") 
# если переводится что-то иное, кусок контроллера или модели, в любом другом случае
QApplication.translate(<Название класса, модуля>, "String to be translated")
# название класса или модуля записано строковым литералом
# например:
QApplication.translate("GUI_modules", "Button Start")
```
Название модуля потом отобразится в файле переводов.
### Создание и редактирование файлов переводов

Переводчик от Qt будет автоматически создавать и обновлять файлы для перевода.

Сохраните все файлы, в которых были добавлены строки методом tr() или QApplication.translate(), после чего в рабочей директории выполните команду

> pylupdate5 <мой_файл_проекта>.pro

Если все сделано правильно, в исполняемых файлах нет ошибок, файл проекта составлен корректно, то у нас создадутся наши файлы переводов c расширением .ts.
Повторный вызов этой команды обновит файлы переводов, если вы например изменили исходный код.
у программы pylupdate5 есть полезный аргумент -noobsolete, который нужен для того чтобы обновленный файл не содержал удаленных строк или измененных строк. Без этого аргумента неактуальные строки будут отражены в программе-редакторе файлов переводов, но бледным цветом.
Например:

> pylupdate5 -noobsolete myProject.pro

Чтобы сделать перевод, нужно открыть программу Qt 5 Linguist и в ней открыть свежесозданный файл перевода с расширением .ts
При первом открытии файла Qt Linguist спросит о том, для какого языка и какой страны предполагается этот перевод.

![Alt text](pictures/translation-settings.png)

Чтобы легче было работать с локализацией, в названиях файлов использованы название локалей, например en_US.ts, ru_RU.ts и т.д.

![Alt text](pictures/qt-linguist-view.png)

Интерфейс Лингвиста простой - слева указаны модули и классы, откуда взяты переводы, посередине сверху список строк в выбранном модуле, справа сверху напоминалка из кода программы, откуда взялось данная фраза, чтобы можно было разобраться в контексте, если надо. Не всегда код справа соответствует фактическому переводу, так что сильно на него надеяться не стоит.
Ниже, по центру правой части есть поля для вставки переводов.

```Translation To <lang>``` - туда вписываем нужный перевод фразы

```Translator comments for <lang>``` - комментарий от переводчика. Заполнять необязательно, но можно, если, например, переводчику-фрилансеру непонятен контекст, так он может с вами общаться.
В поле ```Warnings``` появляются угрозы от Лингвиста, если переведенная строка сильно больше или меньше исходной, или в ней другая пунктуация.

Начальная строчка заканчивалась на точку, а ваша переведенная на восклицительный знак? Держите предупреждение.

```Phases and guesses``` нужны для облегчения работы переводчика. Если одно и то же слово встречается в программе много раз, то можно быстро заполнить для него перевод, нажимая горячие клавиши, указанные в таблице.


![Alt text](pictures/green_check.png)

Клавиши в тулбаре с изображенный зеленой галочкой означают “Пометь открытую фразу как переведенную” (alt+Return) и “Пометь открытую фразу как переведенную и открой следующую непереведенную”(ctrl+Return). Таким образом в таблице с фразами отобразится зеленая или желтая галочка, и можно легко отслеживать, что в настоящий момент еще не переведено или добавилось нового.

![Alt text](pictures/exclamation_mark.png)

Когда переводчик переведет все, что не отмечено галочкой - можно сохранить файл (Ctrl+S) и отправить его обратно разработчикам.
Разработчики кладут переведенный переводчиком файл туда, где его ожидает .pro файл проекта, и выполняет команду в консоли в директории проекта:

> lrelease <translation_file>.ts

(Либо в главном выпадающем меню самого Лингвиста выбирает ```Release <translation_file>.ts```) 

Программа lrelease перекодирует файл переводов из xml формата в бинарный файл с тем же названием и расширением .qm, и теперь наша программа может загружать перевод из этого перевода в рантайме.
Если хочется менять перевод программы в рантайме, то в коде создается новый объект класса qTranslator или и в него загружается нужный файл переводов, потом выполняется

```QApplication.installTranslator(new_translator)```

Добавление нового транслятора ничего не сделает с теми объектами, которые уже есть на экране. Чтобы их текст изменился, надо либо заново установить в них текст по какому-то событию, либо переимплементировать метод retranslateUi() в описании виджета, который вызовется автоматически при установке нового переводчика в приложение QApplication.

Проще всего записывать нужную локацию в файл конфига, считывать его при старте программы, устанавливать переводчик до инициализции виджетов и тогда на старте все будет сразу красиво и без лишней работы. При смене языка рекомендовать сразу перезагрузить программу, чтобы переинициализировать весь интерфейс.
### Перевод текста во внешних файлах

Если вам нужно перевести текст во внешних источниках, например в json файле, файле конфигурации и т.д., то вы не сможете в них вписать метки tr(str), по которым pylupdate5 понимает, что строковая константа должна быть добавлена в переводчик.
На такие случаи есть механизм добавления переводов из внешних источников. Выглядит немного костыльно, но в теории никто и не должен хранить локализуемые данные во внешних файлах, по крайней мере это не Qt-way.
Итак, чтобы добавить слова или фразы из внешнего файла, сначала нужно создать файл внешних переводов с расширением .tri
Наполнение .tri файла:

```
QT_TRANSLATE_NOOP("Module or Class", "String To Translate 1")
QT_TRANSLATE_NOOP("Module or Class 2", "String To Translate 2")
QT_TRANSLATE_NOOP("Module or Class 2", "String To Translate 3")
```

и так далее, пока не вставите все нужные строки.

Что здесь происходит - мы указываем Qt, что в модулях или классах у нас БУДУТ некие строки, и надо это учитывать в рантайме.

Обязательно добавьте этот файл в наш .pro файл проекта как источник!
```
SOURCES = ... <external_sources>.tri
TRANSLATIONS = ...
```
В самих модулях, где строки из внешнего файла будут отображены на экране, нужно вставить такую конструкцию, например если хотим изменить текст кнопки:
```
self.setText(QApplication.translate("Module or class", text_var))
```
где ```text_var``` - это переменная, в которую загружается перевод из внешнего файла, module or class должен быть такой же, как мы указали в .tri файле.

Сохраните все файлы, пересоберите файл перевода через pylupdate5, переведите полученные файлы трансляции в Qt Linguist, и переводы готовы.

 
