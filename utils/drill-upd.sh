cd ~/drill-software
git checkout develop
git pull
cd drill-mover
git checkout hudbay-dev
git status
git pull
cd ../common
git checkout rmo_drill
git status
git pull
cd ../lidar
git checkout drill
git status
git pull
cd ..
rsync -arv drill-mover/ vist@drill02:/srv/vist/src/drill-software/drill-mover/
rsync -arv common/ vist@drill02:/srv/vist/src/drill-software/common/
rsync -arv lidar/ vist@drill02:/srv/vist/src/drill-software/lidar/
rsync -arv .git/ vist@drill02:/srv/vist/src/drill-software/.git/
ssh -o PreferredAuthentications=password vist@drill02 -t 'bash /home/<USER>/drillctl.bash restart'
