# Use Ubuntu 20.04 as base image
FROM ubuntu:20.04

# Set environment variables for non-interactive installation and ROS setup
ENV DEBIAN_FRONTEND=noninteractive
ENV ROS_DISTRO=noetic

# Fix certificate and CA issue
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates && \
    update-ca-certificates

# Install curl and GPG tools for adding the ROS repository
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     curl gnupg2 lsb-release

# Add the ROS repository and GPG key
# RUN curl -fsSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc | gpg --dearmor -o /usr/share/keyrings/ros-archive-keyring.gpg && \
#     echo "deb [signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list

# Install system dependencies and ROS Noetic
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 python3-pip python3-dev \
    python-is-python3 \
    software-properties-common \
    git wget unzip \
    build-essential cmake \
    # ros-${ROS_DISTRO}-desktop-full \
    libqt5gui5 libqt5core5a libqt5widgets5 \
    libopencv-dev \
    pyqt5-dev-tools \
    python3-pyqt5.qtsvg \
    libxcb-xinerama0 libxcb-icccm4 libxcb-image0 libxcb-keysyms1 libxcb-render-util0 && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Install ROS Python dependencies
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     ros-${ROS_DISTRO}-rospy \
#     ros-${ROS_DISTRO}-std-msgs \
#     ros-${ROS_DISTRO}-geometry-msgs \
#     ros-${ROS_DISTRO}-sensor-msgs \
#     ros-${ROS_DISTRO}-rospy-message-converter && \
#     rm -rf /var/lib/apt/lists/*

# Install Python dependencies from requirements.txt
WORKDIR /project
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install the `socket_communicator` module
COPY . .
WORKDIR /project/common/socket_communicator
RUN python3 setup.py install

# Set environment for ROS
# RUN echo "source /opt/ros/${ROS_DISTRO}/setup.bash" >> /root/.bashrc

# Enable X11 forwarding for GUI
ENV DISPLAY=:0
ENV QT_X11_NO_MITSHM=1

# Default working directory for the project
WORKDIR /project

# Command to run the application (placeholder for run.sh override)
CMD ["bash"]
