# Zenoh Migration Guide

## Overview

This guide documents the migration from the old socket-based telemetry system to the new Zenoh-based architecture. The migration provides real-time, event-driven data updates instead of timer-based polling.

## Architecture Changes

### Old Architecture (Socket-based)
- **Core class** acts as central data hub with `telemetry` dictionaries
- **Communicator** uses socket-based communication to get telemetry data
- **Widgets** access data via `get_from_telemetry(core, field_name)` helper function
- **Timer-based updates** - widgets poll for data changes using QTimer
- **Vehicle selection** via `core.in_rc_vehid` or `core.watched_vehid`

### New Architecture (Zenoh-based)
- **ZenohWidget base class** handles subscription-based data updates
- **Zenoh client** provides real-time data streams via topic subscriptions
- **Event-driven updates** - widgets receive data via Qt signals when data changes
- **Configurable data sources** - widgets specify topic/field mappings in JSON config
- **Namespace-based vehicle selection** - widgets automatically switch data sources

## Universal Migration Steps

### Step 1: Change Base Class
```python
# OLD:
class MyWidget(QWidget):
    def __init__(self, core, *args, **kwargs):
        super().__init__(*args, **kwargs)

# NEW:
from base_widget import ZenohWidget
class MyWidget(ZenohWidget):
    def __init__(self, core, *args, **kwargs):
        super().__init__(core, *args, **kwargs)
```

### Step 2: Replace Timer-Based Data Access
```python
# OLD:
def update_data(self):
    value = get_from_telemetry(self.core, 'field_name')
    self.update_display(value)

# NEW:
def handle_field_name(self, value):
    """Handle updates for field_name data source"""
    self.update_display(value)
```

### Step 3: Configure Data Sources
```python
# OLD: Direct telemetry access
# NEW: Configure in prepare() method or JSON config
def prepare(self, data_sources=None, **kwargs):
    if data_sources is None:
        data_sources = {
            'field_name': {
                'topic': 'topic_name',
                'msg_type': 'drill_msgs/msg/MessageType',
                'field': 'field_name'
            }
        }
    super().prepare(data_sources=data_sources, **kwargs)
```

### Step 4: Remove QTimer, Use Signals
```python
# OLD:
self.timer = QTimer()
self.timer.timeout.connect(self.update_data)
self.timer.start(100)

# NEW:
# Connect to data update signals in __init__
self.data_updated.connect(self._on_data_updated)
```

### Step 5: Update start() Method
```python
# OLD:
def start(self):
    self.show()
    self.timer.start()

# NEW:
def start(self):
    super().start()  # Start zenoh subscriptions
    self.show()
```

## Map Widget Migration Example

The `core/gui_modules/map/map_widget.py` has been completely migrated as an example:

### Key Changes Made:
1. **Multiple Inheritance**: `class MapWidget(ZenohWidget, QWidget)`
2. **Data Storage**: Added `_current_values` dict to store incoming zenoh data
3. **Event Handling**: Added `_on_data_updated()` method to handle zenoh data updates
4. **Method Updates**: All `update_*()` methods now use `_current_values` instead of `core.telemetry`
5. **Configuration**: Added comprehensive data sources configuration in JSON

### Data Sources Configuration:
The map widget subscribes to multiple zenoh topics:
- `position` - for vehicle position and orientation
- `vehicle_outline`, `left_cat_outline`, `right_cat_outline` - for vehicle visualization
- `planned_route`, `planned_routes` - for route visualization
- `obstacles` - for obstacle visualization
- `tailing` - for tailing visualization
- `border` - for border visualization

## Benefits of Migration

1. **Real-time Updates**: Data arrives immediately when published, no polling delay
2. **Reduced CPU Usage**: No constant timer-based polling
3. **Better Scalability**: Zenoh handles multiple subscribers efficiently
4. **Data Freshness**: Built-in freshness tracking and timeout handling
5. **Namespace Support**: Automatic vehicle switching based on namespace
6. **Configurable**: Data sources can be configured in JSON without code changes

## Next Steps

1. **Test the migrated map widget** with real zenoh data
2. **Migrate other widgets** following the same pattern
3. **Update JSON configurations** for all widgets to include data sources
4. **Remove old telemetry dependencies** once all widgets are migrated
5. **Add parameter subscriptions** for dynamic configuration updates

## Notes

- The migration maintains backward compatibility where possible
- Old core methods are still available for widgets not yet migrated
- Data freshness and timeout handling is built into the base class
- Vehicle namespace switching is automatic based on core state
