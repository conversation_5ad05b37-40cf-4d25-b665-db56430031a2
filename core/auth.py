# python3.5+

"""
Модуль авторизации пользователей на HAL-сервере.

JWT - Json Web Token
"""

import typing as t
import requests
import logging

logger = logging.getLogger(__name__)


class UserRoles:
    """ Роли пользователей на сервере """
    ADMIN = "admin"
    NORMAL = "normal"


class ResponseTypes:
    """Типы ответов с сервера"""
    USER_EXISTS_ERROR = "UserExists"
    ADMIN_EXISTS_ERROR = "AdminExists"
    USER_NOT_EXISTS_ERROR = "UserNotExists"
    AUTHENTICATION_ERROR = "AuthenticationError"
    AUTHORIZATION_ERROR = "AuthorizationError"
    SERVER_ERROR = "ServerError"
    SUCCESS = "Success"
    TIMEOUT_ERROR = "TimeoutError"
    CONNECTION_ERROR = "ConnectionError"


class ResponseFailure:
    """Объект ответа на невыполненный запрос."""
    def __init__(self, type_, message):
        self.type = type_
        self.message = self._format_message(message)

    def _format_message(self, msg):
        if isinstance(msg, Exception):
            return "{}: {}".format(
                msg.__class__.__name__, "{}".format(msg)
            )
        return msg

    @property
    def value(self):
        return {"type": self.type, "message": self.message}

    def __bool__(self):
        return False


class ResponseSuccess:
    """Объект ответа на успешно выполненный запрос"""
    def __init__(self, value=None):
        self.type = ResponseTypes.SUCCESS
        self.value = value

    def __bool__(self):
        return True


class Auth(object):
    """
    Класс реализует методы авторизации пользователя на сервере,
    а также хранит информацию о текущей сессии -
    имя пользователя, токен доступа и токен обновления.
    """
    def __init__(self, hal_ip: str):
        """
        Args:
            hal_ip: текущей адресс HAL-сервера в формате http://127.0.0.1:5000
        """
        self._hal_ip = hal_ip

        self.cur_user = None
        self.cur_access_jwt = None
        self.cur_refresh_jwt = None

    def signup(self,
               username: str,
               password: str,
               role: str = UserRoles.NORMAL) -> t.Union[ResponseSuccess, ResponseFailure]:
        """
        Обрабатывает запрос на
        регистрацию пользователя.

        Пользователь с ролью администратора может быть создан единожды и
        для его регистрации не требуется авторизация.
        Остальные пользователи могут быть зарегистрированны только
        авторизованным администратором.

        Args:
            username: Уникальное имя пользователя.
            password: Пароль пользователя.
            role: Роль пользователя.
        Returns:
            В случае успешного запроса возвращает объект ответа ResponseSuccess,
            который будет содержать Id созданного пользователя .

            В случае ошибки при обработки запроса возвращает ResponseFailure,
            содержащий тип и описание ошибки для дальнейшей обработки.
        """
        headers = {'User-Agent':'DrillRMO'}
        if role == UserRoles.ADMIN:
            url = "/api/signup/admin"
        elif role == UserRoles.NORMAL:
            url = "/api/signup/user"
            headers['Authorization'] = f"Bearer {self.cur_access_jwt}"
        else:
            raise ValueError(f"Unknown user role: {role}")

        payload = {
            'username': username,
            'password': password
        }
        
        try:
            r = requests.post(
                self._hal_ip + url,
                json=payload,
                headers=headers,
                timeout=3
            )
            
            if r.status_code == 200:
                return ResponseSuccess(value=r.json()["id"])
            elif r.status_code == 400 and role == UserRoles.ADMIN:
                return ResponseFailure(
                    type_=ResponseTypes.ADMIN_EXISTS_ERROR,
                    message=r.text,
                )
            elif r.status_code == 400 and role == UserRoles.NORMAL:
                return ResponseFailure(
                    type_=ResponseTypes.USER_EXISTS_ERROR,
                    message=r.text,
                )
            else:
                return ResponseFailure(
                    type_=ResponseTypes.SERVER_ERROR,
                    message=r.text,
                )
        except requests.exceptions.ConnectTimeout:
            logger.debug(f"Connection timeout during signup request to {self._hal_ip + url}")
            return ResponseFailure(
                type_=ResponseTypes.TIMEOUT_ERROR, 
                message='Connection timeout during signup request'
            )
        except requests.exceptions.RequestException as e:
            logger.debug(f"Connection error during signup request: {e}")
            return ResponseFailure(
                type_=ResponseTypes.CONNECTION_ERROR,
                message=f'Connection error: {str(e)}'
            )

    def login(self, username: str, password: str) -> t.Union[ResponseSuccess, ResponseFailure]:
        """
        Обрабатывает запрос на аутентификации (вход в систему) пользователя.
        В случае успеха возвращает два токена:
        1. Access token (токен доступа), который
           должен быть использован для авторизации последующих запросов к системе.
        2. Refresh token (токен обновления) - используется
           для запроса на обновление токена доступа (после истечения срока действия первого).

        Args:
            username: Уникальное имя пользователя.
            password: Пароль пользователя.
        Returns:
            Returns:
            В случае успешного запроса возвращает объект ответа ResponseSuccess,
            который будет содержать словарь с токеном доступа и токеном обновшения.

            В случае ошибки при обработки запроса возвращает ResponseFailure,
            содержащий тип и описание ошибки для дальнейшей обработки.
        """
        url = "/api/login"
        headers = {'User-Agent':'DrillRMO'}
        payload = {
            "username": username,
            "password": password
        }
        try:
            r = requests.post(
                self._hal_ip + url,
                json=payload,
                headers=headers,
                timeout=3
            )
        except requests.exceptions.ConnectTimeout:
            return ResponseFailure(
                type_=ResponseTypes.TIMEOUT_ERROR, 
                message='Connection timeout during login'
            )
        except requests.exceptions.RequestException as e:
            logger.debug(f"Connection error during login: {e}")
            return ResponseFailure(
                type_=ResponseTypes.CONNECTION_ERROR,
                message=f'Connection error: {str(e)}'
            )

        if r.status_code == 200:
            data = r.json()
            self.cur_user = username
            self.cur_access_jwt = data['access_token']
            self.cur_refresh_jwt = data['refresh_token']

            return ResponseSuccess(
                value=data
            )
        elif r.status_code == 404:
            return ResponseFailure(
                type_=ResponseTypes.USER_NOT_EXISTS_ERROR,
                message=r.text,
            )
        elif r.status_code == 401:
            return ResponseFailure(
                type_=ResponseTypes.AUTHENTICATION_ERROR,
                message=r.text,
            )
        else:
            return ResponseFailure(
                type_=ResponseTypes.SERVER_ERROR,
                message=r.text,
            )

    def logout_access(self) -> t.Union[ResponseSuccess, ResponseFailure]:
        """
        Обрабатывает запрос инвалидации токена доступа для текущей сессии.

        Returns:
            В случае успешного запроса возвращает объект ответа ResponseSuccess.

            В случае ошибки при обработки запроса возвращает ResponseFailure,
            содержащий тип и описание ошибки для дальнейшей обработки.
        """
        url = "/api/logout/access"
        headers = {'User-Agent':'DrillRMO','Authorization': f"Bearer {self.cur_access_jwt}"}

        try:
            r = requests.post(
                self._hal_ip + url,
                headers=headers,
                timeout=3
            )
        except requests.exceptions.ConnectTimeout:
            return ResponseFailure(
                type_=ResponseTypes.TIMEOUT_ERROR,
                message="Connection timeout during access logout"
            )
        except requests.exceptions.RequestException as e:
            logger.debug(f"Connection error during access logout: {e}")
            return ResponseFailure(
                type_=ResponseTypes.CONNECTION_ERROR,
                message=f'Connection error: {str(e)}'
            )
            
        if r.status_code == 200:
            self.cur_access_jwt = None
            self.cur_refresh_jwt = None
            self.cur_user = None
            return ResponseSuccess()
        else:
            return ResponseFailure(
                type_=ResponseTypes.SERVER_ERROR,
                message=r.text
            )

    def logout_refresh(self) -> t.Union[ResponseSuccess, ResponseFailure]:
        """
        Обрабатывает запрос инвалидации токена обновления для текущей сессии.

        Returns:
            В случае успешного запроса возвращает объект ответа ResponseSuccess.

            В случае ошибки при обработки запроса возвращает ResponseFailure,
            содержащий тип и описание ошибки для дальнейшей обработки.
        """
        url = "/api/logout/refresh"
        headers = {'User-Agent':'DrillRMO', 'Authorization': f"Bearer {self.cur_refresh_jwt}"}

        try:
            r = requests.post(
                self._hal_ip + url,
                headers=headers,
                timeout=3
            )
        except requests.exceptions.ConnectTimeout:
            return ResponseFailure(
                type_=ResponseTypes.TIMEOUT_ERROR,
                message="Connection timeout during refresh logout"
            )
        except requests.exceptions.RequestException as e:
            logger.debug(f"Connection error during refresh logout: {e}")
            return ResponseFailure(
                type_=ResponseTypes.CONNECTION_ERROR,
                message=f'Connection error: {str(e)}'
            )

        if r.status_code == 200:
            self.cur_refresh_jwt = None
            return ResponseSuccess()
        else:
            return ResponseFailure(
                type_=ResponseTypes.SERVER_ERROR,
                message=r.text
            )

    def refresh_token(self) -> t.Union[ResponseSuccess, ResponseFailure]:
        """
        Отправляет запрос на обновление
        токена доступа. Возвращает обновленный токен и
        сохраняет его в ядре.
        Для выполнения запроса требует валидный рефреш-токен в
        хэдере запроса.

        Returns:
            В случае успешного запроса возвращает объект ответа ResponseSuccess,
            который будет содержать новый токен.

            В случае ошибки при обработки запроса возвращает ResponseFailure,
            содержащий тип и описание ошибки для дальнейшей обработки.
        """
        url = "/api/token/refresh"
        headers = {'User-Agent':'DrillRMO', 'Authorization': f"Bearer {self.cur_refresh_jwt}"}

        try:
            r = requests.post(
                self._hal_ip + url,
                headers=headers,
                timeout=3
            )
        except requests.exceptions.ConnectTimeout:
            return ResponseFailure(
                type_=ResponseTypes.TIMEOUT_ERROR,
                message="Connection timeout during token refresh"
            )
        except requests.exceptions.RequestException as e:
            logger.debug(f"Connection error during token refresh: {e}")
            return ResponseFailure(
                type_=ResponseTypes.CONNECTION_ERROR,
                message=f'Connection error: {str(e)}'
            )

        if r.status_code == 200:
            data = r.json()
            self.cur_access_jwt = data['access_token']

            return ResponseSuccess(
                value=data['access_token']
            )
        else:
            return ResponseFailure(
                type_=ResponseTypes.SERVER_ERROR,
                message=r.text,
            )
