from threading import Thread
import time
from playsound import playsound


class MakeSoundClass(object):
    def __init__(self, core, *args, **kwargs):
        self.core = core
        self.killed = False
        self.lash_plan_hash = None
        # self.move_perm_file = 'sounds/move_perm_beep.wav'
        self.played = False



    def check(self):
        while not self.killed:
            try:
                # isconnected = self.core.veh_active_states['4694']
                # mp = self.core.telemetry[self.vehid][self.field_to_read]
                vehid = '4694'
                if vehid is not None:
                    if self.from_telemetry:
                        selected_machine_telemetry = self.core.telemetry.get(vehid)
                        if selected_machine_telemetry is not None:
                            flag = selected_machine_telemetry.get(self.field_to_read)
                            # print('move', flag)
                        else:
                            flag = None
                    else:
                        flag = getattr(self.core, self.field_to_read, None)[vehid]
                        # print('veh_active_states', flag)
                    if flag is not None:
                        if self.inverse: flag = not flag
                        if not flag and not self.played:
                            playsound(self.sound_file)
                            self.played = True
                        if flag:
                            self.played = False
                        # print("FFFF ", self.field_to_read, flag)
            except KeyError as e:
                print(e)
                pass
            time.sleep(1)


    def prepare(self,  from_telemetry, field_to_read="", sound_file="", inverse = False):
        # self.vehid = '4694'
        self.sound_file = sound_file
        self.field_to_read = field_to_read
        self.from_telemetry = from_telemetry
        self.inverse = inverse
        pass



    def start(self):
        self.check_thread = Thread(target=self.check)
        self.check_thread.start()





