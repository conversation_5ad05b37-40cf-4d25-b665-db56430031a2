# Extracted class: DynamicActionButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt6.QtWidgets import QApplication, QMessageBox
from customClasses.smartButton import SmartButton

class DynamicActionButton(SmartButton):
    def __init__(self, **kwargs):
        """Initialize the DynamicActionButton.

        Args:
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(**kwargs)
        self.setText(QApplication.translate("DynamicActionButton", "Finish\nAction"))

        # Create message box for rod buildup confirmation
        self.buildup_msgbox = QMessageBox()
        self.buildup_msgbox.setIcon(QMessageBox.Icon.Warning)
        self.buildup_msgbox.setText(self.tr("WARNING: Before finishing rod buildup, make sure you have completed the process manually!\nIncorrect completion may damage the drilling rig."))
        self.buildup_msgbox.setInformativeText(self.tr("Are you sure you want to finish rod buildup?"))
        self.buildup_msgbox.setWindowTitle(self.tr("Finish Rod Buildup"))
        self.buildup_msgbox.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        self.buildup_msgbox.setDefaultButton(QMessageBox.StandardButton.No)

        # Create message box for rod stow confirmation
        self.stow_msgbox = QMessageBox()
        self.stow_msgbox.setIcon(QMessageBox.Icon.Warning)
        self.stow_msgbox.setText(self.tr("WARNING: Before finishing rod stow, make sure you have completed the process manually!\nIncorrect completion may damage the drilling rig."))
        self.stow_msgbox.setInformativeText(self.tr("Are you sure you want to finish rod stow?"))
        self.stow_msgbox.setWindowTitle(self.tr("Finish Rod Stow"))
        self.stow_msgbox.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        self.stow_msgbox.setDefaultButton(QMessageBox.StandardButton.No)

    def buttonTimerAction(self) -> None:
        """Update button state and text based on vehicle state.

        This method is called periodically by a timer to check the current state
        of the controlled or watched vehicle and update the button accordingly.
        """
        # Determine which vehicle to check (controlled or watched)
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid

        # Disable button if no vehicle is selected
        if vehid is None:
            self.setEnabled(False)
            self.setText(QApplication.translate("DynamicActionButton", "Finish\nAction"))
            return

        # Disable button if vehicle has no main state machine
        if "MainStateMachineNode" not in self.core.telemetry[vehid]:
            self.setEnabled(False)
            self.setText(QApplication.translate("DynamicActionButton", "Finish\nAction"))
            return

        # Get current and previous modes
        main_mode = self.get_main_mode()
        last_mode = self.get_last_mode()

        # Update button based on vehicle state
        if (main_mode in ("drilling", "remote") and
                "DrillerNode" in self.core.telemetry[vehid] and
                self.core.telemetry[vehid]["DrillerNode"] not in ("idle", "raise", "wait_after_drill")):
            # Drilling mode
            self.setText(QApplication.translate("DynamicActionButton", "Finish\nDrilling"))
            self.setEnabled(True)
        elif (main_mode == "remote" and last_mode == "shaft_stow" and
                "RodChangerNode" in self.core.telemetry[vehid] and
                self.core.telemetry[vehid]["RodChangerNode"] not in ("idle", "finish")):
            # Rod stow mode
            self.setText(QApplication.translate("DynamicActionButton", "Finish\nStow"))
            self.setEnabled(True)
        elif (main_mode == "remote" and last_mode == "shaft_buildup" and
                "RodChangerNode" in self.core.telemetry[vehid] and
                self.core.telemetry[vehid]["RodChangerNode"] not in ("idle", "finish")):
            # Rod buildup mode
            self.setText(QApplication.translate("DynamicActionButton", "Finish\nBuildup"))
            self.setEnabled(True)
        elif (main_mode in ("moving", "remote") and
                "PlannerNode" in self.core.telemetry[vehid] and
                self.core.telemetry[vehid]["PlannerNode"] in ("moving", "approach")):
            # Moving mode
            self.setText(QApplication.translate("DynamicActionButton", "Finish\nMoving"))
            self.setEnabled(True)
        else:
            # Default state - disabled
            self.setText(QApplication.translate("DynamicActionButton", "Finish\nAction"))
            self.setEnabled(False)

    def get_main_mode(self) -> str:
        """Get the main mode of the controlled or watched vehicle.

        Returns:
            str: The main mode of the vehicle, or None if not available
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid and "MainStateMachineNode" in self.core.telemetry[vehid]:
            return self.core.telemetry[vehid]["MainStateMachineNode"]
        return None

    def get_last_mode(self) -> str:
        """Get the last mode of the controlled or watched vehicle.

        Returns:
            str: The last mode of the vehicle, or None if not available
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid and "LastMainMode" in self.core.telemetry[vehid]:
            return self.core.telemetry[vehid]["LastMainMode"]
        return None

    def buttonClickAction(self) -> None:
        """Handle button click action based on vehicle state.

        This method determines the appropriate action to take based on the current
        state of the controlled or watched vehicle.
        """
        # Get current and previous modes
        main_mode = self.get_main_mode()
        last_mode = self.get_last_mode()

        # Execute appropriate action based on vehicle mode
        if main_mode == "drilling":
            self.finish_drilling()
        elif main_mode == "remote" and last_mode == "shaft_stow":
            # Show confirmation dialog for finishing rod stow
            ret = self.stow_msgbox.exec()
            if ret == QMessageBox.StandardButton.Yes:
                self.finish_stow()
        elif main_mode == "remote" and last_mode == "shaft_buildup":
            # Show confirmation dialog for finishing rod buildup
            ret = self.buildup_msgbox.exec()
            if ret == QMessageBox.StandardButton.Yes:
                self.finish_buildup()
        elif main_mode in ("moving", "remote"):
            self.finish_moving()

    def finish_drilling(self) -> None:
        """Send command to finish drilling operation.

        This method sends the finish_drilling command to the controlled or watched vehicle.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            cmd = {
                'finish_drilling': True
            }
            self.core.output_data[vehid].update(cmd)

    def finish_stow(self) -> None:
        """Send command to finish rod stow operation.

        This method sends the finish stow command to the controlled or watched vehicle.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            cmd = {
                'state': {"sm_name": "RodChangerNode", "state_name": "finish"}
            }
            self.core.output_data[vehid].update(cmd)

    def finish_buildup(self) -> None:
        """Send command to finish rod buildup operation.

        This method sends the finish buildup command to the controlled or watched vehicle.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            cmd = {
                'state': {"sm_name": "RodChangerNode", "state_name": "finish"}
            }
            self.core.output_data[vehid].update(cmd)

    def finish_moving(self) -> None:
        """Send command to finish moving operation.

        This method sends the finish_moving command to the controlled or watched vehicle.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            cmd = {
                'finish_moving': True
            }
            self.core.output_data[vehid].update(cmd)
