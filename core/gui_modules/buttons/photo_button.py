# Extracted class: PhotoButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QHBoxLayout
from PyQt6 import QtSvgWidgets
from customClasses.smartButton import SmartButton
from gui_common import RESOURCES_CONFIG, im_save

class PhotoButton(SmartButton):
    def __init__(self, core, **kwargs):
        """Initialize the PhotoButton.

        Args:
            core: The core object
            **kwargs: Additional keyword arguments passed to the parent class
        """
        self.button_text = kwargs.get('text', 'Save images')
        kwargs['text'] = ''
        kwargs['core'] = core
        super().__init__(**kwargs)
        self.setEnabled(True)
        self.i = 0  # Counter for photo operations

        # Load camera icon
        self.icon_path = os.path.join(RESOURCES_CONFIG["images_folder"], "camera_icon.svg")
        self.svg_widget = QtSvgWidgets.QSvgWidget(self.icon_path)

        # Set up button layout with icon
        icon_size = 35
        self.svg_widget.setFixedSize(icon_size, icon_size)
        layout = QHBoxLayout(self)
        layout.addWidget(self.svg_widget, 0, Qt.AlignmentFlag.AlignCenter)
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)

        # Configure button appearance
        button_size = 40
        self.setFixedSize(button_size, button_size)
        self.setStyleSheet("background-color: transparent;")
        self.setToolTip(self.tr(self.button_text))

    def setText(self, text: str) -> None:
        """Set the button text (used for tooltip).

        Args:
            text: The text to set
        """
        self.button_text = text
        self.setToolTip(self.tr(self.button_text))

    def buttonTimerAction(self) -> None:
        """Check if photo action is available and update button state.

        This method is called periodically by a timer to check if the photo action
        is available.
        """
        # Always enabled as this is a local operation
        self.setEnabled(True)

    def buttonClickAction(self) -> None:
        """Handle button click action.

        This method is called when the button is clicked and sends a command
        to save camera images.
        """
        logging.info("Send messages to save camera images")

        # Clear all existing messages in the queues
        for _, q in enumerate(im_save):
            while not q.empty():
                q.get()

        # Send save command to all image queues
        for _, q in enumerate(im_save):
            q.put('True')
