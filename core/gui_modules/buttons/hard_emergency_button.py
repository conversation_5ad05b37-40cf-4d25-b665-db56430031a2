# Extracted class: HardEmergencyButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt6 import QtCore
from PyQt6.QtCore import QTimer, QObject
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QWidget, QGridLayout, QPushButton

class HardEmergencyButton(QObject):
    """
    Emergency stop button widget.

    This button allows the operator to trigger an emergency stop or resume operation.
    """

    def __init__(self, core, *args, **kwargs):
        """Initialize the HardEmergencyButton.

        Args:
            core: The core object
            *args: Additional positional arguments passed to the parent class
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(*args, **kwargs)
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)

    def prepare(self, width=500, height=500, x=500, y=800, btn_width=100,
                btn_height=50, btn_green_fontsize="18px", btn_red_fontsize="18px", screen_num=0) -> None:
        """Prepare the button widget.

        Args:
            width: Width of the button frame
            height: Height of the button frame
            x: X position of the button
            y: Y position of the button
            btn_width: Button width
            btn_height: Button height
            btn_green_fontsize: Font size for the green (continue) button
            btn_red_fontsize: Font size for the red (emergency) button
            screen_num: Screen number (0 for sensor screen, other for main screen)
        """

        if screen_num == 0:
            self.frame = QWidget(self.core.sensor_screen)
        else:
            self.frame = QWidget(self.core.main_screen)

        self.frame.setGeometry(0, 0, width, height)
        grid = QGridLayout()
        self.frame.setLayout(grid)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        self.btn_red_fontsize = btn_red_fontsize
        self.btn_green_fontsize = btn_green_fontsize

        if screen_num == 0:
            self.frame.setGeometry(x, y, width, height)
        else:
            self.frame.setGeometry(1920 * (screen_num - 1) + x, y, width, height)

        btn = QPushButton()
        # btn.setText("Аварийная\nостановка")
        btn.setText(self.tr("Emergency\nStop"))
        stylesheet = "background: red;  font-size: %s" % (btn_red_fontsize)
        btn.setStyleSheet(stylesheet)
        btn.setObjectName("btn")

        btn.setFixedSize(btn_width, btn_height)
        btn.clicked[bool].connect(self.send)
        grid.addWidget(btn)
        self.btn = btn
        self.cmd = {}
        self.vehid = None

    def start(self) -> None:
        """Start the button widget.

        This method shows the button and starts the update timer.
        """
        self.btn.show()
        self.frame.show()
        self.timer.start(100)

    def update(self) -> None:
        """Update the button state based on vehicle state.

        This method is called periodically by the timer to update the button state
        based on the current state of the controlled, watched, or selected vehicle.
        """
        # Determine which vehicle to use (controlled, watched, or selected)
        if self.core.in_rc_vehid is not None:
            self.vehid = self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            self.vehid = self.core.watched_vehid
        elif self.core.selected_vehid is not None:
            self.vehid = self.core.selected_vehid
        else:
            # No vehicle selected, disable button
            self.btn.setEnabled(False)
            self.vehid = None
            return

        # Enable button since a vehicle is selected
        self.btn.setEnabled(True)

        # Update button appearance and command based on PWM state
        if 'pwm_enabled' not in self.core.telemetry[self.vehid] or self.core.telemetry[self.vehid]['pwm_enabled']:
            # PWM is enabled, show emergency stop button
            stylesheet = "background: red;  font-size: %s" % (self.btn_red_fontsize)
            self.btn.setStyleSheet(stylesheet)
            self.btn.setText(self.tr("Emergency\nStop"))

            self.cmd = {
                'emergency': True
            }
        else:
            # PWM is disabled, show continue working button
            stylesheet = "background: green;  font-size: %s" % (self.btn_green_fontsize)
            self.btn.setStyleSheet(stylesheet)
            self.btn.setText(self.tr("Continue\nworking"))

            self.cmd = {
                'emergency': False
            }

    def send(self) -> None:
        """Send the emergency command.

        This method is called when the button is clicked and sends the emergency
        command to the vehicle.
        """
        if self.vehid is not None:
            self.core.output_data[self.vehid].update(self.cmd)
