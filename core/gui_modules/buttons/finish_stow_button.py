# Extracted class: FinishStowButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt6 import QtCore
from PyQt6.QtCore import QTimer, QObject
from PyQt6.QtWidgets import QWidget, QGridLayout, QPushButton, QMessageBox

class FinishStowButton(QObject):
    """
    Button widget for finishing rod stow process.

    This button allows the operator to signal that the rod stow process is complete.
    """

    def __init__(self, core, *args, **kwargs):
        """Initialize the FinishStowButton.

        Args:
            core: The core object
            *args: Additional positional arguments passed to the parent class
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(*args, **kwargs)
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)

        # Create confirmation message box
        self.msgbox = QMessageBox()
        self.msgbox.setIcon(QMessageBox.Icon.Information)
        self.msgbox.setText(
            self.tr("Attention!\nMake sure the boring rod is not in the borehole,\nFork and Key are retracted away!"))
        self.msgbox.setWindowTitle(self.tr("Attention!"))
        self.msgbox.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)

    def prepare(self, width=500, height=500, x=500, y=800, color='blue', btn_width=100,
                btn_height=50, btn_fontsize="18px", screen_num=0) -> None:
        """Prepare the button widget.

        Args:
            width: Width of the button frame
            height: Height of the button frame
            x: X position of the button
            y: Y position of the button
            color: Button color
            btn_width: Button width
            btn_height: Button height
            btn_fontsize: Button font size
            screen_num: Screen number (0 for sensor screen, other for main screen)
        """

        if screen_num == 0:
            self.frame = QWidget(self.core.sensor_screen)
        else:
            self.frame = QWidget(self.core.main_screen)

        self.frame.setGeometry(0, 0, width, height)
        grid = QGridLayout()
        self.frame.setLayout(grid)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        if screen_num == 0:
            self.frame.setGeometry(x, y, width, height)
        else:
            self.frame.setGeometry(self.core.screen_width * (screen_num - 1) + x, y, width, height)

        btn = QPushButton()
        # btn.setText("Закончить\nразбор")
        btn.setText(self.tr("Finish\nStow"))
        stylesheet = "background: %s;  font-size: %s" % (color, btn_fontsize)
        btn.setStyleSheet(stylesheet)
        btn.setObjectName("btn")

        btn.setFixedSize(btn_width, btn_height)
        btn.clicked[bool].connect(self.send)
        grid.addWidget(btn)
        self.btn = btn

    def start(self) -> None:
        """Start the button widget.

        This method shows the button and starts the update timer.
        """
        self.btn.show()
        self.frame.show()
        self.timer.start(100)

    def update(self) -> None:
        """Update the button state based on vehicle state.

        This method is called periodically by the timer to update the button state
        based on the current state of the watched vehicle.
        """
        # Disable button if no vehicle is being watched or it's not in shaft_stow mode
        if (self.core.watched_vehid is None or
                "MainStateMachineNode" not in self.core.telemetry[self.core.watched_vehid] or
                self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] != "shaft_stow"):
            self.btn.setEnabled(False)
        else:
            self.btn.setEnabled(True)

    def send(self) -> None:
        """Send the finish stow command.

        This method is called when the button is clicked and sends the finish_stow
        command to the watched vehicle after confirmation.
        """
        # Show confirmation dialog
        ret = self.msgbox.exec()
        if ret == QMessageBox.StandardButton.Ok:
            # Send finish stow command
            cmd = {
                'finish_stow': True
            }
            self.core.output_data[self.core.watched_vehid].update(cmd)
