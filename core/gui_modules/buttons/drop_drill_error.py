# Extracted class: DropDrillError

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QApplication
from customClasses.smartButton import SmartButton

class DropDrillError(SmartButton):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setText(QApplication.translate("DropDrillError", self.text()))
        self.cmd = {}
        self.setBlinkColor(QColor("red"))

    def buttonTimerAction(self) -> None:
        """Check for errors in vehicle nodes and update button state accordingly.

        This method is called periodically by a timer to check if any vehicle nodes
        are in a failure state and need to be repaired.
        """
        # Disable button if no vehicle is being watched or if in remote control mode
        if self.core.watched_vehid is None or self.core.in_rc_vehid is not None:
            self.setEnabled(False)
            self.setBlinking(False)
            return

        nodes2repair = []  # List to collect nodes that need repair
        if "MainStateMachineNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] == 'failure':
            nodes2repair.append("MainStateMachineNode")

        if "PlannerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["PlannerNode"] == 'failure':
            nodes2repair.append("PlannerNode")

        if "TowerControllerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["TowerControllerNode"] == 'failure':
            nodes2repair.append("TowerControllerNode")

        if "DrillerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["DrillerNode"] == 'failure':
            nodes2repair.append("DrillerNode")

        if "ArmControllerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["ArmControllerNode"] == 'failure':
            nodes2repair.append("ArmControllerNode")

        if "DustFlapsControllerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["DustFlapsControllerNode"] == 'failure':
            nodes2repair.append("DustFlapsControllerNode")

        if "CarouselControllerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["CarouselControllerNode"] == 'failure':
            nodes2repair.append("CarouselControllerNode")

        if "LevelerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["LevelerNode"] == 'failure' \
                and "rear_jack_pulled" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["rear_jack_pulled"] \
                and "right_jack_pulled" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["right_jack_pulled"] \
                and "left_jack_pulled" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["left_jack_pulled"] \
                and "MainStateMachineNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] in (
        "idle", "moving", "grounding", "restore_string"):

            self.cmd = {
                'state': {"sm_name": "LevelerNode", "state_name": "pulled"}
            }


        elif "LevelerNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["LevelerNode"] == 'failure' \
                and "rear_jack_pulled" in self.core.telemetry[self.core.watched_vehid].keys() \
                and not self.core.telemetry[self.core.watched_vehid]["rear_jack_pulled"] \
                and "right_jack_pulled" in self.core.telemetry[self.core.watched_vehid].keys() \
                and not self.core.telemetry[self.core.watched_vehid]["right_jack_pulled"] \
                and "left_jack_pulled" in self.core.telemetry[self.core.watched_vehid].keys() \
                and not self.core.telemetry[self.core.watched_vehid]["left_jack_pulled"] \
                and "MainStateMachineNode" in self.core.telemetry[self.core.watched_vehid].keys() \
                and self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] in ("leveling", "tower_tilt",
                                                                                             "drilling",
                                                                                             "shaft_buildup",
                                                                                             "shaft_stow",
                                                                                             "wait_before_drill",
                                                                                             "wait_after_level") \
                and "platform_pitch" in self.core.telemetry[self.core.watched_vehid].keys() \
                and abs(self.core.telemetry[self.core.watched_vehid]["platform_pitch"]) < 0.3 \
                and "platform_roll" in self.core.telemetry[self.core.watched_vehid].keys() \
                and abs(self.core.telemetry[self.core.watched_vehid]["platform_roll"]) < 0.3:

            self.cmd = {
                'state': {"sm_name": "LevelerNode", "state_name": "holding"}
            }
        else:
            self.cmd = {}

        if len(nodes2repair):
            self.cmd.update({'repair': json.dumps(nodes2repair)})

        if len(self.cmd.items()):
            self.setEnabled(True)
            self.setBlinking(True)
        else:
            self.setEnabled(False)
            self.setBlinking(False)

    def buttonClickAction(self) -> None:
        """Send repair command to the vehicle when button is clicked.

        This method sends the repair command to the watched vehicle if there are
        any commands to send.
        """
        if self.cmd and len(self.cmd) > 0:
            self.core.output_data[self.core.watched_vehid].update(self.cmd)
