# Extracted class: RecalibAirButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt6.QtWidgets import QApplication
from customClasses.smartButton import SmartButton

class RecalibAirButton(SmartButton):
    """Button for recalibrating air pressure sensors.

    This button allows the operator to recalibrate the air pressure sensors
    on the drilling rig.
    """

    def __init__(self, **kwargs):
        """Initialize the RecalibAirButton.

        Args:
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(**kwargs)
        self.setText(QApplication.translate("RecalibAirButton", self.text()))
        self.confirmationText = QApplication.translate("RecalibAirButton", self.confirmationText)

    def buttonTimerAction(self) -> None:
        """Check if recalibration is available and update button state.

        This method is called periodically by a timer to check if the recalibration
        action is available for the currently watched vehicle.
        """
        # Disable button if no vehicle is being watched or driller node is in idle/failure state
        if (self.core.watched_vehid is None or
                "DrillerNode" not in self.core.telemetry[self.core.watched_vehid] or
                self.core.telemetry[self.core.watched_vehid]["DrillerNode"] in ["idle", "failure"]):
            self.setEnabled(False)
        else:
            self.setEnabled(True)

    def buttonClickAction(self) -> None:
        """Handle button click action.

        This method is called when the button is clicked and sends the recalibrate_air
        command to the watched vehicle after confirmation.
        """
        # Show confirmation dialog
        if self.askForConfirmation():
            # Send recalibrate air command
            cmd = {
                'recalibrate_air': True
            }
            self.core.output_data[self.core.watched_vehid].update(cmd)
