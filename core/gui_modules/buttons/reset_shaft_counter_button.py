# Extracted class: ResetShaftCounterButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QApplication, QMessageBox
from customClasses.smartButton import SmartButton
from customClasses.blinkBehaviour import BlinkingBehaviour

class ResetShaftCounterButton(SmartButton, BlinkingBehaviour):
    def __init__(self, **kwargs):
        """Initialize the ResetShaftCounterButton.

        Args:
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(**kwargs)
        self.base_text = QApplication.translate("ResetShaftCounterButton", "Reset\nRods")
        self.setText(self.base_text)

        # Create warning message box
        self.msgbox = QMessageBox()
        self.msgbox.setIcon(QMessageBox.Icon.Warning)
        self.msgbox.setText(self.tr("WARNING: Resetting the rod counter while the rod is in the ground can cause severe damage to the rod and the drilling rig!"))
        self.msgbox.setInformativeText(self.tr("Are you sure you want to reset the rod counter?"))
        self.msgbox.setWindowTitle(self.tr("Reset Rod Counter"))
        self.msgbox.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        self.msgbox.setDefaultButton(QMessageBox.StandardButton.No)

        # Set up blinking behavior
        self._blinkColor = QColor("red")
        self.setBlinking(False)

    def get_main_mode(self) -> str:
        """Get the main mode of the controlled or watched vehicle.

        Returns:
            str: The main mode of the vehicle, or None if not available
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid and "MainStateMachineNode" in self.core.telemetry[vehid]:
            return self.core.telemetry[vehid]["MainStateMachineNode"]
        return None

    def get_shaft_counter(self) -> int:
        """Get the shaft counter value of the controlled or watched vehicle.

        Returns:
            int: The shaft counter value, or 0 if not available
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid and "MainStateMachineNode" in self.core.telemetry[vehid]:
            return self.core.telemetry.get(vehid, {}).get("shaft_counter", 0)
        return 0

    def get_is_need_shaft_reset(self) -> bool:
        """Check if shaft reset is needed for the controlled or watched vehicle.

        Returns:
            bool: True if shaft reset is needed, False otherwise
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid and "is_need_shaft_reset" in self.core.telemetry[vehid]:
            return self.core.telemetry[vehid]["is_need_shaft_reset"]
        return False

    def buttonTimerAction(self) -> None:
        """Update button state and text based on vehicle state.

        This method is called periodically by a timer to check the current state
        of the controlled or watched vehicle and update the button accordingly.
        """
        # Get shaft counter value
        shaft_counter = int(self.get_shaft_counter())

        # Determine which vehicle to check (controlled or watched)
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is None:
            self.setEnabled(False)
            return

        # Get vehicle state information
        main_mode = self.get_main_mode()
        is_need_shaft_reset = self.get_is_need_shaft_reset()

        # Enable button only in remote mode with rods present
        if main_mode == "remote" and shaft_counter > 0:
            self.setEnabled(True)
        else:
            self.setEnabled(False)

        # Update visual state
        self.setBlinking(is_need_shaft_reset)
        self.setText(f"{self.base_text} [{shaft_counter}]")

    def buttonClickAction(self) -> None:
        """Handle button click action.

        This method is called when the button is clicked and handles the reset shaft
        counter action after showing a confirmation dialog.
        """
        # Determine which vehicle to use (controlled or watched)
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            # Show confirmation dialog
            ret = self.msgbox.exec()
            if ret == QMessageBox.StandardButton.Yes:
                # Send reset shaft counter command
                cmd = {
                    'reset_shaft_counter': True
                }
                self.core.output_data[vehid].update(cmd)
                logging.info(f"Reset rods command sent for vehicle {vehid}")
            else:
                logging.info(f"Reset rods command cancelled for vehicle {vehid}")
