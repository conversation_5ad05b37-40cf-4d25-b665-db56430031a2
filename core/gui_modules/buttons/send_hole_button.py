# Extracted class: SendHoleButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
from PyQt6 import QtCore
from PyQt6.QtCore import QTimer, QLocale, QObject
from PyQt6.QtGui import QColor, QDoubleValidator
from PyQt6.QtWidgets import (QWidget, QGridLayout, QLabel, QLineEdit,
                             QComboBox)
from customClasses.styledPushButton import StyledPushButton

class SendHoleButton(QObject):
    """
    Button widget for sending drill hole commands.

    This button allows the operator to send drilling commands with specified
    hole ID, depth, and angle parameters.
    """

    def __init__(self, core, *args, **kwargs):
        """Initialize the SendHoleButton.

        Args:
            core: The core object
            *args: Additional positional arguments passed to the parent class
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(*args, **kwargs)
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)

    def prepare(self, width=500, height=500, x=500, y=800, color=None, btn_width=100,
                btn_height=50, btn_fontsize=18, label_font_size="16px", font_color="#ffffff", screen_num=0,
                url="http://localhost", max_depth=33) -> None:
        """Prepare the button widget.

        Args:
            width: Width of the button frame
            height: Height of the button frame
            x: X position of the button
            y: Y position of the button
            color: Button color
            btn_width: Button width
            btn_height: Button height
            btn_fontsize: Button font size
            label_font_size: Font size for labels
            font_color: Font color for labels
            screen_num: Screen number (0 for sensor screen, other for main screen)
            url: URL endpoint for sending drill commands
            max_depth: Maximum allowed drilling depth
        """

        self.url = url
        self.max_depth = max_depth
        self.font_color = font_color
        self.label_font_size = label_font_size

        if screen_num == 0:
            self.frame = QWidget(self.core.sensor_screen)
        else:
            self.frame = QWidget(self.core.main_screen)

        self.frame.setGeometry(0, 0, width, height)
        grid = QGridLayout()
        self.frame.setLayout(grid)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        if screen_num == 0:
            self.frame.setGeometry(x, y, width, height)
        else:
            self.frame.setGeometry(self.core.screen_width * (screen_num - 1) + x, y, width, height)

        btn = StyledPushButton()
        btn.setText(self.tr("Drill\nhere"))
        if btn_fontsize is not None:
            btn.setFontSize(btn_fontsize)
        if color is not None:
            btn.setColor(QColor(color))
        btn.setObjectName("btn")

        btn.setFixedSize(btn_width, btn_height)
        btn.clicked.connect(self.send)
        grid.addWidget(btn, 0, 0)
        self.btn = btn

        self.depth_input = QLineEdit()
        self.depth_input.setMinimumWidth(60)
        validator = QDoubleValidator()
        loc = QLocale(QLocale.Language.English, QLocale.Script.LatinScript, QLocale.Country.UnitedStates)
        validator.setLocale(loc)
        self.depth_input.setValidator(validator)

        grid.addWidget(self.depth_input, 1, 1)

        self.depth_label = QLabel()
        self.depth_label.setText(self.tr("Depth"))
        self.depth_label.setStyleSheet("font-size: %s; color: %s" % (label_font_size, font_color))
        grid.addWidget(self.depth_label, 1, 0)

        self.angle_box = QComboBox()
        self.angle_box.addItems(["0", "5", "10", "15"])
        grid.addWidget(self.angle_box, 2, 1)

        self.angle_label = QLabel()
        # self.angle_label.setText("Наклон")
        self.angle_label.setText(self.tr("Tilt"))
        self.angle_label.setStyleSheet("font-size: %s; color: %s" % (label_font_size, font_color))
        grid.addWidget(self.angle_label, 2, 0)

        self.id_input = QLineEdit()
        grid.addWidget(self.id_input, 3, 1)

        self.id_label = QLabel()
        # self.id_label.setText("Номер")
        self.id_label.setText(self.tr("Hole ID"))
        self.id_label.setStyleSheet("font-size: %s; color: %s" % (label_font_size, font_color))
        grid.addWidget(self.id_label, 3, 0)

    def start(self) -> None:
        """Start the button widget.

        This method shows the button and starts the update timer.
        """
        self.btn.show()
        self.frame.show()
        self.timer.start(100)

    def update(self) -> None:
        """Update the button state based on vehicle state.

        This method is called periodically by the timer to update the button state
        based on the current state of the watched vehicle.
        """
        # Disable button if conditions are not met
        if (self.core.watched_vehid is None or
                "MainStateMachineNode" not in self.core.telemetry[self.core.watched_vehid] or
                self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] != "idle" or
                "LevelerNode" not in self.core.telemetry[self.core.watched_vehid] or
                self.core.telemetry[self.core.watched_vehid]["LevelerNode"] != "pulled" or
                self.core.auth.cur_user is None):
            self.btn.setEnabled(False)
        else:
            self.btn.setEnabled(True)

    def send(self) -> None:
        """Send the drill hole command.

        This method is called when the button is clicked and sends the drilling
        command with the specified parameters to the HAL API.
        """
        if self.core.watched_vehid is not None:
            # Get vehicle ID
            vehid = str(self.core.telemetry[self.core.watched_vehid]["vehid"])

            # Validate input fields
            if self.id_input.text() == "" or self.depth_input.text() == "" or "," in self.depth_input.text():
                return

            # Get drilling parameters
            holeid = str(self.id_input.text())
            depth = float(self.depth_input.text())
            angle = int(self.angle_box.currentText())

            # Prepare request payload
            PAYLOAD = {
                "vehid": vehid,
                "holeid": holeid,
                "elevation": angle,
                "depth": depth
            }

            # Prepare request headers
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.core.auth.cur_access_jwt}"
            }

            # Send request to HAL API
            try:
                response = requests.request(
                    "POST",
                    self.core.config['hal_ip'] + self.url,
                    headers=headers,
                    json=PAYLOAD
                )
                print(f"Drill command sent: {vehid}, {holeid}, depth={depth}, angle={angle}")
                print(f"Response: {response.text}")
            except Exception as e:
                print(f"Can't connect to HAL: {e}")
