# Extracted class: DropActionButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import hashlib
import time
import requests
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QApplication, QMessageBox, QInputDialog, QLineEdit
from customClasses.smartButton import SmartButton

class DropActionButton(SmartButton):
    def __init__(self, **kwargs):
        """Initialize the DropActionButton.

        Args:
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(**kwargs)
        self.setText(QApplication.translate("DropActionButton", self.text()))

        # Create confirmation message box
        self.msgbox = QMessageBox()
        self.msgbox.setIcon(QMessageBox.Icon.Information)
        self.msgbox.setText(self.tr("Confirm current task cancelling"))
        self.msgbox.setWindowTitle("Confirmation needed")
        self.msgbox.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)

        # Create wrong password message box
        self.msgbox_wrong_pass = QMessageBox()
        self.msgbox_wrong_pass.setIcon(QMessageBox.Icon.Critical)
        self.msgbox_wrong_pass.setText(self.tr("Permitted to qualified Engineers or Developers only"))
        self.msgbox_wrong_pass.setWindowTitle(self.tr("Wrong password"))
        self.msgbox_wrong_pass.setStandardButtons(QMessageBox.StandardButton.Ok)

    def buttonTimerAction(self) -> None:
        """Check if drop action is available and update button state.

        This method is called periodically by a timer to check if the drop action
        is available for the currently watched vehicle.
        """
        # Disable button if conditions are not met
        if (self.core.watched_vehid is None or
                self.core.auth.cur_user is None or
                "MainStateMachineNode" not in self.core.telemetry[self.core.watched_vehid] or
                self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] in [
                    'idle', 'remote', 'remote_wait', 'remote_prepare', 'end_remote', 'wait_before_level'
                ] or
                "PlannerNode" not in self.core.telemetry[self.core.watched_vehid] or
                self.core.telemetry[self.core.watched_vehid]['PlannerNode'] == 'computing'):
            self.setEnabled(False)
            self.resetColor()
        else:
            # Enable button and set color based on vehicle state
            self.setEnabled(True)
            if self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] in [
                'moving', 'restore_string', 'leveling', 'wait_after_level'
            ]:
                self.resetColor()
            else:
                self.setColor(QColor("red"))

    def send_to_hal(self, vehid: str) -> None:
        """Send cancel action request to HAL.

        Args:
            vehid: The vehicle ID to send the request for
        """
        PAYLOAD = {
            "vehid": vehid,
        }

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {self.core.auth.cur_access_jwt}"
        }

        try:
            response = requests.request(
                "POST",
                self.core.config['hal_ip'] + '/api/cancel-current-act',
                headers=headers,
                json=PAYLOAD
            )
            print(response.text.encode('utf8'))
            time.sleep(0.1)
        except Exception as e:
            print(f"Can't connect to HAL: {e}")

    def buttonClickAction(self) -> None:
        """Handle button click action.

        This method is called when the button is clicked and handles the drop action
        based on the current state of the watched vehicle.
        """
        # Check if vehicle is in a state where dropping action is allowed without password
        if self.core.telemetry[self.core.watched_vehid]["MainStateMachineNode"] in [
            'moving', 'restore_string', 'leveling', 'wait_after_level'
        ]:
            # Show confirmation dialog
            ret = self.msgbox.exec()
            if ret == QMessageBox.StandardButton.Ok:
                vehid = str(self.core.telemetry[self.core.watched_vehid]["vehid"])
                self.send_to_hal(vehid)
                cmd = {
                    'drop_action': True
                }
                self.core.output_data[self.core.watched_vehid].update(cmd)
        else:
            # For other states (like drilling), require password
            pwd, ok = QInputDialog.getText(
                self.core.sensor_screen,
                self.tr("Unusual Action"),
                self.tr("""
                Drilling in the process, can't cancel task right now.
                Enter password if still want to proceed
                """),
                QLineEdit.EchoMode.Password
            )
            if ok:
                # Verify password hash
                if hashlib.md5(pwd.encode()).hexdigest() == self.core.config['admin_pass_hash']:
                    vehid = str(self.core.telemetry[self.core.watched_vehid]["vehid"])
                    self.send_to_hal(vehid)
                else:
                    self.msgbox_wrong_pass.exec()
