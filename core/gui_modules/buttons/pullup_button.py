# Extracted class: PullupButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt6.QtWidgets import QApplication
from customClasses.smartButton import SmartButton

class PullupButton(SmartButton):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setText(QApplication.translate("PullupButton", self.text()))

    def buttonTimerAction(self) -> None:
        """Check if pullup action is available and update button state.

        This method is called periodically by a timer to check if the pullup action
        is available for the currently watched vehicle.
        """
        # Enable button only if a vehicle is being watched and it's in drilling mode
        if (self.core.watched_vehid is not None and
                "DrillerNode" in self.core.telemetry[self.core.watched_vehid] and
                self.core.telemetry[self.core.watched_vehid]["DrillerNode"] == 'drilling'):
            self.setEnabled(True)
        else:
            self.setEnabled(False)

    def buttonClickAction(self) -> None:
        """Send pullup command to the vehicle when button is clicked.

        This method sends the pullup command to the watched vehicle.
        """
        cmd = {
            'pullup': True
        }
        self.core.output_data[self.core.watched_vehid].update(cmd)
