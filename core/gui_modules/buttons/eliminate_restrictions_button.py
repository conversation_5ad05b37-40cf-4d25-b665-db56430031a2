# Extracted class: EliminateFirstLevelRestrictionsButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import hashlib
import math
import time
from datetime import datetime
from PyQt6.QtCore import QTime
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import (QApplication, QWidget, QMessageBox, QInputDialog, QLineEdit)
from customClasses.smartButton import SmartButton
from customClasses.darkPalette import ColorSet
from gui_common import get_from_telemetry, DEFAULT_RESTRICTIONS_MODE, FIRST_LEVEL_RESTRICTIONS_OFF, FULL_RESTRICTIONS_OFF, FIRST_LEVEL_REST_OFF_MSG, ALL_REST_OFF_MSG

class EliminateFirstLevelRestrictionsButton(SmartButton):
    """Button for eliminating first level restrictions.

    This button allows the operator to disable first level restrictions on the drilling rig.
    It can also show a secondary button for disabling all restrictions.
    """

    def __init__(self, **kwargs):
        """Initialize the EliminateFirstLevelRestrictionsButton.

        Args:
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(**kwargs)
        self.label_text = self.text()
        self.setText(QApplication.translate("EliminateRestrictions", self.label_text))
        self.widget = QWidget(self.core_screen)
        self._color = ColorSet.warningRedBlinkingColor.value.darker(100)
        self._blinkColor = QColor("black")
        self.normal_width = True
        self.start_restr_mode_ts = None
        self.basicFontSize = self.fontSize
        self.reducedFontSize = math.ceil(self.basicFontSize * 0.7)

        # Create the secondary button for disabling all restrictions
        self.full_rest_off_btn = EliminateFullRestrictionsButton(
            core=self.core,
            text='All\nRestrictions\noff',
            fontSize=self.reducedFontSize,
            needConfirm=False,
            needPassword=True
        )

    def start(self) -> None:
        """Start the button widget.

        This method positions and initializes the secondary button and starts the update timer.
        """
        # Position and size the secondary button
        self.full_rest_off_btn.move(int(self.x() + self.width() / 2 + 5), int(self.y()))
        self.full_rest_off_btn.setFixedSize(int(self.width() / 2), int(self.height()))

        # Initialize button states
        self.full_rest_off_btn.setEnabled(False)
        self.full_rest_off_btn.hide()
        self.full_rest_off_btn.start()
        self.setEnabled(False)

        # Start the update timer
        self.updateTimer.start()

    def setReducedWidth(self) -> None:
        """Set the button to reduced width (half of normal width)."""
        self.setFixedWidth(int(self.width() / 2))
        self.normal_width = False

    def setNormalWidth(self) -> None:
        """Set the button to normal width and restore the basic font size."""
        self.setFixedWidth(int(self.width() * 2))
        self.normal_width = True
        self.setFontSize(self.basicFontSize)

    def set_default_button_view(self) -> None:
        """Reset the button to its default appearance."""
        self.setBlinking(False)
        self.setText(QApplication.translate("EliminateRestrictions", self.label_text))
        self.setFontSize(self.basicFontSize)
        if not self.normal_width:
            self.setNormalWidth()
        self.full_rest_off_btn.hide()
        self.full_rest_off_btn.setEnabled(False)

    def buttonTimerAction(self) -> None:
        """Update the button state based on vehicle state and restrictions mode.

        This method is called periodically by a timer to update the button state
        based on the current state of the watched or controlled vehicle and the
        current restrictions mode.
        """
        # Get current restrictions mode
        current_restrictions_mode = get_from_telemetry(self.core, 'restrictions_mode', None)

        # Handle watched vehicle (autonomous mode)
        vehid = self.core.watched_vehid
        if vehid is not None:   # in autonomous mode
            self.setEnabled(False)
            self.setBlinking(False)
            current_restrictions_mode = get_from_telemetry(self.core, 'restrictions_mode', DEFAULT_RESTRICTIONS_MODE)

            # Reset restrictions mode if it's not default
            if current_restrictions_mode != DEFAULT_RESTRICTIONS_MODE:
                restrictions_mode = {
                    'restrictions_mode': DEFAULT_RESTRICTIONS_MODE
                }
                self.core.output_data[vehid].update(restrictions_mode)

        # Enable button only in remote control mode
        if self.core.in_rc_vehid is not None:
            self.setEnabled(True)
        else:   # not rc mode
            if not self.normal_width:
                self.setNormalWidth()
            self.setEnabled(False)

        # Update button appearance based on restrictions mode
        if current_restrictions_mode == DEFAULT_RESTRICTIONS_MODE:
            # Default mode - normal button
            self.set_default_button_view()
        elif current_restrictions_mode == FIRST_LEVEL_RESTRICTIONS_OFF:
            # First level restrictions off - show remaining time and secondary button
            self.remaining_time_output()
            if self.normal_width:
                self.setReducedWidth()
            self.full_rest_off_btn.show()
            self.full_rest_off_btn.setEnabled(True)
        elif current_restrictions_mode == FULL_RESTRICTIONS_OFF:
            # Full restrictions off - show remaining time and hide secondary button
            self.remaining_time_output()
            self.full_rest_off_btn.hide()
            if not self.normal_width:
                self.setNormalWidth()
        else:   # None
            # No restrictions mode - reset button
            self.set_default_button_view()
            self.setEnabled(False)

    def buttonClickAction(self) -> None:
        """Handle button click action.

        This method is called when the button is clicked and handles the restrictions
        mode changes based on the current state.
        """
        vehid = self.core.in_rc_vehid
        cur_restrictions_mode = get_from_telemetry(self.core, 'restrictions_mode')

        if cur_restrictions_mode == DEFAULT_RESTRICTIONS_MODE:
            # Default mode - ask for password to disable first level restrictions
            pwd, ok = QInputDialog.getText(
                self.widget,
                QApplication.translate("DangerousFeature", str('Dangerous feature!')),
                QApplication.translate("DangerousFeatureDescription", str('Description!')),
                QLineEdit.EchoMode.Password
            )
            if ok:
                # Verify password
                if hashlib.md5(pwd.encode()).hexdigest() == self.core.config['eliminate_restrictions_pass_hash']:
                    # Password correct - disable first level restrictions
                    self.setBlinking(True)
                    self.setReducedWidth()
                    self.start_restr_mode_ts = time.time()
                    restrictions_mode = {
                        'restrictions_mode': FIRST_LEVEL_RESTRICTIONS_OFF
                    }
                    self.core.output_data[vehid].update(restrictions_mode)

                    # Log the action
                    newMessage = {
                        'vehid': vehid,
                        'msg': FIRST_LEVEL_REST_OFF_MSG,
                        'time': datetime.now().timestamp(),
                        'source': 'RMO',
                        'code': 'FIRST_LEVEL_REST_OFF_MSG',
                        'level': 4
                    }
                    self.core.log_msgs[vehid].append(newMessage)

                    # Show the secondary button
                    self.full_rest_off_btn.show()
                    self.full_rest_off_btn.setEnabled(True)
                else:
                    # Password incorrect - show error
                    QMessageBox.warning(self, self.tr("Error"), self.tr("Wrong password"))

        elif cur_restrictions_mode == FIRST_LEVEL_RESTRICTIONS_OFF or cur_restrictions_mode == FULL_RESTRICTIONS_OFF:
            # Restrictions already disabled - restore default mode
            self.finish_restr_mode()

    def remaining_time_output(self) -> None:
        """Update the button text with the remaining time for restrictions mode.

        This method gets the remaining time for the current restrictions mode and
        updates the button text accordingly.
        """
        remaining_time = get_from_telemetry(self.core, 'restr_off_mode_remaining_time')

        # Check if remaining_time is None or not positive
        if remaining_time is not None and remaining_time > 0:
            # Format the remaining time
            time_to_display = QTime(0, 0, int(remaining_time))
            self.setText(
                QApplication.translate("RestoreRestrictions", str('Restore\nControl\nRestrictions')) +
                '\n' + time_to_display.toString('mm:ss')
            )
            self.setFontSize(self.reducedFontSize)
            self.setBlinking(True)
        else:
            # Time expired or not set - restore default mode
            self.finish_restr_mode()

    def finish_restr_mode(self) -> None:
        """Restore the default restrictions mode.

        This method sends a command to restore the default restrictions mode and
        resets the button appearance.
        """
        self.start_restr_mode_ts = None
        vehid = self.core.in_rc_vehid

        # Only send command if in remote control mode
        if vehid is not None:
            restrictions_mode = {
                'restrictions_mode': DEFAULT_RESTRICTIONS_MODE
            }
            self.core.output_data[vehid].update(restrictions_mode)

        # Reset button appearance
        self.set_default_button_view()


class EliminateFullRestrictionsButton(SmartButton):
    """Button for eliminating all restrictions.

    This button allows the operator to disable all restrictions on the drilling rig.
    It is shown only when first level restrictions are already disabled.
    """

    def __init__(self, **kwargs):
        """Initialize the EliminateFullRestrictionsButton.

        Args:
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(**kwargs)
        self.label_text = self.text()
        self.setText(QApplication.translate("EliminateFullRestrictions", self.label_text))
        self.widget = QWidget(self.core_screen)
        self._color = ColorSet.warningRedBlinkingColor.value.darker(100)
        self._blinkColor = QColor("black")
        self.start_restr_mode_ts = None
        self.setEnabled(False)
        self.hide()

    def start(self) -> None:
        """Start the button widget.

        This method initializes the button and starts the update timer.
        """
        self.hide()
        self.updateTimer.start()
        self.clicked.connect(self.buttonClickAction)

    def buttonClickAction(self) -> None:
        """Handle button click action.

        This method is called when the button is clicked and handles the full
        restrictions mode change after password confirmation.
        """
        vehid = self.core.in_rc_vehid

        # Check if in remote control mode
        if vehid is None:
            print('Cannot disable all restrictions when not in remote control mode')
            return

        # Check if first level restrictions are already disabled
        current_mode = get_from_telemetry(self.core, 'restrictions_mode', DEFAULT_RESTRICTIONS_MODE)
        if current_mode == FIRST_LEVEL_RESTRICTIONS_OFF:
            # Ask for password to disable all restrictions
            pwd, ok = QInputDialog.getText(
                self.widget,
                QApplication.translate("DangerousFeature", str('Dangerous feature!')),
                QApplication.translate("DangerousFeatureDescription", str('Description!')),
                QLineEdit.EchoMode.Password
            )
            if ok:
                # Verify password
                if hashlib.md5(pwd.encode()).hexdigest() == self.core.config['eliminate_restrictions_pass_hash']:
                    # Password correct - disable all restrictions
                    restrictions_mode = {
                        'restrictions_mode': FULL_RESTRICTIONS_OFF
                    }
                    self.hide()
                    self.setEnabled(False)
                    self.core.output_data[vehid].update(restrictions_mode)

                    # Log the action
                    newMessage = {
                        'vehid': vehid,
                        'msg': ALL_REST_OFF_MSG,
                        'time': datetime.now().timestamp(),
                        'source': 'RMO',
                        'code': 'ALL_REST_OFF_MSG',
                        'level': 4
                    }
                    self.core.log_msgs[vehid].append(newMessage)
                else:
                    # Password incorrect - show error
                    QMessageBox.warning(self, self.tr("Error"), self.tr("Wrong password"))
        else:
            # Wrong mode - log error
            print(f'WRONG MODE: Cannot disable all restrictions when in mode {current_mode}')
