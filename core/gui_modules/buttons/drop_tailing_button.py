# Extracted class: DropTailingButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt6.QtWidgets import QApplication
from customClasses.smartButton import SmartButton

class DropTailingButton(SmartButton):
    """Button for dropping tailings from the drilling rig.

    This button allows the operator to clear tailings from the drilling rig.
    """

    def __init__(self, **kwargs):
        """Initialize the DropTailingButton.

        Args:
            **kwargs: Additional keyword arguments passed to the parent class
        """
        super().__init__(**kwargs)
        self.setText(QApplication.translate("DropTailingButton", self.text()))
        self.confirmationText = QApplication.translate("DropTailingButton", self.confirmationText)

    def buttonTimerAction(self) -> None:
        """Check if drop tailing action is available and update button state.

        This method is called periodically by a timer to check if the drop tailing
        action is available for the currently watched vehicle.
        """
        # Enable button only if a vehicle is being watched
        if self.core.watched_vehid is None:
            self.setEnabled(False)
        else:
            self.setEnabled(True)

    def buttonClickAction(self) -> None:
        """Handle button click action.

        This method is called when the button is clicked and sends the clear_tailing
        command to the watched vehicle after confirmation.
        """
        # Show confirmation dialog
        if self.askForConfirmation():
            # Send clear tailing command
            cmd = {
                'clear_tailing': True
            }
            self.core.output_data[self.core.watched_vehid].update(cmd)
