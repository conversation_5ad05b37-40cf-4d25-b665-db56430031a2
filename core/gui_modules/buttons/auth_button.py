# Extracted class: AuthButton

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from functools import partial
from PyQt6.QtCore import Qt, QObject, pyqtSignal, QThread, QPoint, QSize, QMargins
from PyQt6.QtGui import QColor, QPalette
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QMessageBox, QApplication)
from customClasses.styledPushButton import StyledPushButton
from customClasses.darkPalette import darkPalette, ColorSet
from auth import ResponseSuccess, ResponseTypes, ResponseFailure

class PanelMode:
    """Enum-like class for authentication panel modes.

    Attributes:
        logined: User is logged in
        logouted: User is logged out
    """
    logined = 1
    logouted = 2

class AuthButton(QWidget):
    """
    This button gives operator ability to log in into system and
    stores JWT
    """

    def __init__(self, core, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.core = core
        self.core_screen = core.sensor_screen
        self.authorized = False

    def prepare(self, x, y, width, height, login_color, logout_color):
        self.loginColor = QColor(login_color)
        self.logoutColor = QColor(logout_color)
        self.initUI(x, y, width, height)

        self.setPanelComposing(PanelMode.logouted)

    def initUI(self, x, y, width, height):
        # create layouts and fake wrapper widget just for parenting *facepalm*
        self.parentWrapper = QWidget(parent=self.core_screen)
        self.parentWrapper.setPalette(darkPalette)

        mainLayout = QVBoxLayout()
        mainLayout.setContentsMargins(QMargins(4, 4, 4, 4))
        self.parentWrapper.setLayout(mainLayout)
        self.parentWrapper.setFixedSize(QSize(width, height))
        self.parentWrapper.move(QPoint(x, y))
        self.parentWrapper.setAutoFillBackground(True)
        # create wrapped object
        self.userLabel = QLabel()
        self.userLabel.setObjectName("userLabel")
        self.loginButton = StyledPushButton(text=self.tr("Login"))
        self.logoutButton = StyledPushButton(text=self.tr("Logout"))

        self.credentialWrapper = QWidget()
        self.loginForm = QLineEdit()
        self.passForm = QLineEdit()

        self.loginForm.setStyleSheet("color: black;")
        self.passForm.setStyleSheet("color: black;")

        # wrapped layouts
        loginLayout = QVBoxLayout()
        credentialsLayout = QFormLayout()
        buttonsLayout = QVBoxLayout()

        # STYLING

        self.loginButton.setColor(QColor("#57bd6b"))
        self.logoutButton.setColor(ColorSet.buttonBlueColor.value)
        for button in (self.logoutButton, self.loginButton):
            button.setMinimumHeight(35)
            button.setMaximumWidth(200)
            button.setMinimumWidth(150)
            # button.setAlignment(Qt.AlignmentFlag.AlignHCenter)

        # Set password field to hide characters
        self.passForm.setEchoMode(QLineEdit.EchoMode.Password)

        self.paintMainWrapper(self.loginColor)

        self.parentWrapper.setStyleSheet("""
        QLabel {
            font: 16px;
            color: white;
            padding: 1px;
            padding-right: 1px;
            alignment: center;
        }
        QLabel#userName {
            font: bold 28px;
            text-align: center;
            padding: 1px;
        }
        QLineEdit {
            background-color: white;
            border-radius: 5px;
            border-style: outset;
            padding: 1px;
        }
        StyledPushButton {
            color: white;

        }
        """)
        # Center-align the buttons and set appropriate spacing
        buttonsLayout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        credentialsLayout.setSpacing(10)
        loginLayout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # WIRING
        self.loginButton.clicked.connect(self.login)
        self.logoutButton.clicked.connect(self.logout)

        # COMPOSING
        mainLayout.addLayout(loginLayout)
        mainLayout.addWidget(self.credentialWrapper)
        mainLayout.addLayout(buttonsLayout)
        # mainLayout.addWidget(self.loginButton)

        loginLayout.addWidget(self.userLabel)
        buttonsLayout.addWidget(self.logoutButton)
        buttonsLayout.addWidget(self.loginButton)

        self.credentialWrapper.setLayout(credentialsLayout)

        credentialsLayout.addRow(QLabel(self.tr("login"), parent=self.parentWrapper), self.loginForm)
        credentialsLayout.addRow(QLabel(self.tr("password"), parent=self.parentWrapper), self.passForm)

    def paintMainWrapper(self, baseColor=None) -> None:
        """Set the background color of the parent wrapper.

        Args:
            baseColor: The background color to set
        """
        palette = self.palette()
        palette.setColor(QPalette.ColorGroup.Normal, QPalette.ColorRole.Window, baseColor)

        self.parentWrapper.setPalette(palette)

    def checkIfAuthorized(self):
        # if self.core.authToken is None:
        #     self.setAuthorization(False)
        pass

    def setPanelComposing(self, mode):
        if mode == PanelMode.logined:
            self.logoutButton.show()
            self.userLabel.show()
            self.loginButton.hide()
            self.credentialWrapper.hide()
            # self.parentWrapper.setFixedHeight(65)
            self.paintMainWrapper(self.logoutColor)
            pass
        else:
            self.logoutButton.hide()
            self.userLabel.hide()
            self.loginButton.show()
            self.credentialWrapper.show()
            self.paintMainWrapper(self.loginColor)
            # self.parentWrapper.setFixedHeight(130)
            pass

    class LoginWorker(QObject):
        finished = pyqtSignal(object)

        def __init__(self, core, login, passw, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.core = core
            self.login = login
            self.passw = passw

        def run(self):
            result = self.core.auth.login(self.login, self.passw)
            self.finished.emit(result)

    class LogoutWorker(QObject):
        finished = pyqtSignal(object)

        def __init__(self, core, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.core = core

        def run(self):
            result = self.core.auth.logout_access()
            self.finished.emit(result)

    def login(self) -> None:
        """Handle user login process with threading.

        This method validates user credentials and attempts to log in the user.
        """

        def checkLogin(response) -> None:
            """Process the authentication response.

            Args:
                response: The authentication response from core.auth
            """
            if isinstance(response, ResponseSuccess):
                self.setPanelComposing(PanelMode.logined)
                self.loginButton.setEnabled(True)
                self.userLabel.setText(self.core.auth.cur_user)
            else:
                self.loginButton.setEnabled(True)
                self.loginForm.setText("")
                self.passForm.setText("")
                errorText = QApplication.translate("AuthWidget", "Can't Authorize user\nerror: ")
                errorText += response.message
                mb = QMessageBox(text=errorText)
                mb.setFixedSize(800, 600)
                mb.setWindowTitle(QApplication.translate("AuthWidget", "Login Error"))
                # mb.move(self.parentWrapper.pos())
                mb.exec()

        # check if data at least exists in textfields
        login = self.loginForm.text()
        if not login:
            print('Empty login field')
            mb = QMessageBox(text=QApplication.translate("AuthWidget", "Login field should not be empty"))
            mb.setWindowTitle(QApplication.translate("AuthWidget", "Empty Login Field"))
            mb.move(self.parentWrapper.pos())
            mb.exec()
            return
        passw = self.passForm.text()
        if not passw:
            mb = QMessageBox(text=QApplication.translate("AuthWidget", "Password field should not be empty"))
            mb.setWindowTitle(QApplication.translate("AuthWidget", "Empty Password Field"))
            mb.move(self.parentWrapper.pos())
            mb.exec()
            print('Empty pass filed')
            return

        if not self.core.hal_connection_status:
            mb = QMessageBox(text=QApplication.translate("AuthWidget", "No connection with server..."))
            mb.setWindowTitle(QApplication.translate("AuthWidget", "Can't log in"))
            mb.move(self.parentWrapper.pos())
            mb.exec()
            return

        # create loging thread
        self.thread = QThread()
        # create logout worker, which call core.auth method
        self.loginWorker = self.LoginWorker(self.core, login, passw)
        self.loginWorker.moveToThread(self.thread)

        # connect all signals and wires
        self.thread.started.connect(partial(self.loginButton.setEnabled, False))
        self.thread.started.connect(partial(print, "Thread started"))
        self.thread.started.connect(self.loginWorker.run)
        self.loginWorker.finished.connect(self.thread.exit)
        self.loginWorker.finished.connect(checkLogin)
        self.thread.finished.connect(self.loginWorker.deleteLater)
        self.thread.finished.connect(self.thread.deleteLater)

        # let's roll
        self.thread.start()

    def logout(self) -> None:
        """Handle user logout process with threading.

        This method logs out the current user.
        """

        def checkLogout(response) -> None:
            """Process the logout response.

            Args:
                response: The logout response from core.auth
            """
            self.logoutButton.setEnabled(True)
            if isinstance(response, ResponseSuccess):
                self.setPanelComposing(PanelMode.logouted)
                self.loginForm.setText("")
                self.passForm.setText("")
            else:
                errorText = QApplication.translate("AuthWidget", "Can't logout user\nerror: ")
                errorText += response.message
                mb = QMessageBox(text=errorText)
                mb.setFixedSize(800, 600)
                mb.setWindowTitle(QApplication.translate("AuthWidget", "Logout Error"))
                mb.exec()

        if not self.core.hal_connection_status:
            mb = QMessageBox(text=QApplication.translate("AuthWidget", "No connection with server..."))
            mb.setWindowTitle(QApplication.translate("AuthWidget", "Can't log out"))
            mb.move(self.parentWrapper.pos())
            mb.exec()
            return

        # create logout thread and worker
        self.thread = QThread()
        self.logoutWorker = self.LogoutWorker(self.core)
        self.logoutWorker.moveToThread(self.thread)
        # connect wires
        # self.thread.started.connect(logoutDialog.exec)
        self.thread.started.connect(partial(self.logoutButton.setEnabled, False))
        self.thread.started.connect(self.logoutWorker.run)
        self.logoutWorker.finished.connect(self.thread.exit)
        self.logoutWorker.finished.connect(checkLogout)
        self.thread.finished.connect(self.logoutWorker.deleteLater)
        self.thread.finished.connect(self.thread.deleteLater)
        # self.thread.finished.connect(logoutDialog.reject)
        # logout process begins
        self.thread.start()

    def start(self):
        pass
