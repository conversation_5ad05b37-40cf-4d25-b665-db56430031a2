# Extracted class: CoreDataTranslator

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import time
from typing import Dict, Optional, Any, Set

from PyQt6.QtWidgets import QLabel, QVBoxLayout, QSizePolicy

from gui_common import Form

class CoreDataTranslator(object):
    """
    A widget for displaying the core state.

    This class creates a window that shows the current state of the core object
    and updates it in real-time.
    """

    def __init__(self, core: Any) -> None:
        """
        Initialize the CoreDataTranslator.

        Args:
            core: The core object containing the application state
        """
        self.core = core
        self.screen = Form('Core data')
        self.layout = QVBoxLayout()

        self.buttons: Dict[str, QLabel] = {}
        self.labels: Dict[str, QLabel] = {}
        self.killed = False
        self.update_thread: Optional[threading.Thread] = None

    def kill(self) -> None:
        """
        Stop the update thread and clean up resources.
        """
        self.killed = True
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1.0)

    def prepare(self) -> None:
        """
        Prepare the UI by creating labels for each core attribute.
        """
        # Attributes that should not be displayed
        not2show = (
            'core_lock',
            'main_screen',
            'sensor_screen',
        )

        # Create a label for each core attribute
        for k, v in self.core.__dict__.items():
            if k in not2show:
                continue

            lbl = QLabel()
            lbl.setWordWrap(True)
            lbl.setText(f"{k}:     {str(v)}z")

            lbl.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            self.labels[k] = lbl

            self.layout.addWidget(lbl)

        # Configure the screen
        self.screen.setLayout(self.layout)
        self.screen.setFixedHeight(1080)

    def start(self) -> None:
        """
        Show the screen and start the update thread.
        """
        self.screen.show()

        # Start the update thread
        self.update_thread = threading.Thread(target=self.update, daemon=True)
        self.update_thread.start()

    def update(self) -> None:
        """
        Update thread that refreshes the labels with current core attribute values.
        """
        try:
            while not self.killed:
                for k, v in self.core.__dict__.items():
                    lbl = self.labels.get(k, None)
                    if lbl is None:
                        continue
                    # Use f-strings for better readability and performance
                    self.labels[k].setText(f"{k}:    {str(v)}")
                # Sleep to avoid high CPU usage
                time.sleep(0.05)
        except Exception as e:
            print(f"Error in update thread: {e}")
