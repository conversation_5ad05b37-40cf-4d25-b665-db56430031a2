# Extracted class: CategoryContainer

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt6 import QtCore
from PyQt6.QtWidgets import QWidget, QGroupBox, QVBoxLayout, QApplication

class CategoryContainer(object):
    """
    A class for creating a visual container for grouping widgets with an optional label in a frame.

    This container provides a styled frame with optional title that can be used to group
    related UI elements together for better visual organization.
    """
    def __init__(self, core, *args, **kwargs) -> None:
        """
        Initialize the CategoryContainer.

        Args:
            core: The core object containing the application state
            *args: Additional positional arguments (not used)
            **kwargs: Additional keyword arguments (not used)
        """
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.frame = None
        self.group_box = None

    def prepare(self, width: int = 200, height: int = 300, x: int = 500, y: int = 500,
                border_color: str = "#444444", border_width: int = 2, border_radius: int = 10,
                screen: int = 1, is_label: bool = False, label_text: str = "",
                label_font_size: str = "20px", label_font_color: str = "#FFFFFF") -> None:
        """
        Prepare the container with the specified parameters.

        Args:
            width: Width of the container in pixels
            height: Height of the container in pixels
            x: X position of the container
            y: Y position of the container
            border_color: Color of the container border (CSS color format)
            border_width: Width of the border in pixels
            border_radius: Radius of the border corners in pixels
            screen: Screen number (1-based) to place the container on
            is_label: Whether to show a label at the top of the container
            label_text: Text for the label (only used if is_label is True)
            label_font_size: Font size for the label (CSS format, e.g., "20px")
            label_font_color: Color for the label text (CSS color format)
        """
        self.frame = QWidget(self.core_screen)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)

        font_size = int(label_font_size.replace('px', ''))
        label_font = self.frame.font()
        label_font.setPixelSize(font_size)

        self.group_box = QGroupBox(QApplication.translate("CategoryContainer", label_text) if is_label else "",
                                   self.frame)
        self.group_box.setGeometry(0, 0, width, height)
        self.group_box.setFont(label_font)

        title_margin = font_size // 2 if is_label else 0

        self.group_box.setStyleSheet(f"""
            QGroupBox {{
                background-color: transparent;
                border: {border_width}px solid {border_color};
                border-radius: {border_radius}px;
                margin-top: {title_margin}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 20px;
                color: {label_font_color};
                padding: 0 10px;
            }}
        """)

        layout = QVBoxLayout(self.group_box)
        layout.setContentsMargins(0, 0, 0, 0)
        self.frame.setGeometry(x + self.core.screen_width * (screen - 1), y, width, height)

    def start(self) -> None:
        """
        Show the container frame.
        """
        if self.frame:
            self.frame.show()

    def kill(self) -> None:
        """
        Mark the container as killed (for cleanup).
        """
        self.killed = True
