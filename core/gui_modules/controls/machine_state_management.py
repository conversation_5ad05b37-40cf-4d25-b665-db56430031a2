# Extracted class: MachineStateManagement

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import hashlib
import time

from PyQt6.QtCore import QObject, QTimer, Qt
from PyQt6.QtWidgets import (QWidget, QGridLayout, QPushButton, QLabel,
                             QComboBox, QInputDialog, QLineEdit, QDialog,
                             QApplication, QVBoxLayout)

class MachineStateManagement(QObject):
    """
    Widget for managing machine state through a UI with comboboxes.

    This widget displays the current state of various state machines and allows
    the operator to change states after password authentication.
    """

    def __init__(self, core):
        """
        Initialize the MachineStateManagement widget.

        Args:
            core: The core application object
        """
        super().__init__()
        self.combo = {}  # Dictionary to store comboboxes
        self.grid = QGridLayout()
        self.core = core
        self.killed = False
        self.core_screen = core.main_screen
        self.confirm = QWidget()
        self.state_combobox = True
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)

        # Will be initialized in prepare()
        self.widget_params = None
        self.confirm_params = None
        self.state_machine = None
        self.telemetry = None
        self.widget = None
        self.vehid = None
        self.edit_enabled = False
        self.state_labels = {}
        self.edit_btn = None
        self.node_choice = None
        self.goto_state = None

    def kill(self) -> None:
        """
        Mark the widget as killed to stop update thread.
        """
        self.killed = True

    def prepare(self, state_machine, widget_params, confirm_params) -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            state_machine: Dictionary defining the state machine structure
            widget_params: Dictionary of widget appearance parameters
            confirm_params: Dictionary of confirmation dialog parameters
        """
        self.widget_params = dict(widget_params)
        self.confirm_params = dict(confirm_params)
        self.state_machine = state_machine
        self.telemetry = {k: list(self.state_machine[k])[0] for k in self.state_machine}
        self.widget = QWidget(self.core_screen)
        self.widget.setFixedSize(self.widget_params["width"], self.widget_params["height"])
        self.widget.move(self.widget_params["x"], self.widget_params["y"])
        self.vehid = None
        self.edit_enabled = False
        self.state_labels = {}  # Словарь для хранения меток состояний

        for k in self.state_machine:
            m_name = list(self.state_machine[k])[0]
            sm_name = dict(self.state_machine[k][m_name])
            index = list(self.state_machine).index(k)

            # Создаем метку для названия состояния
            lbl = QLabel()
            lbl_text = QApplication.translate("MachineStateManagement", m_name)
            lbl.setText(lbl_text)
            lbl.setStyleSheet("color: %s; font-size: %s; text-transform: %s; font-weight: %s;" % (
            self.widget_params["color"], self.widget_params["font_size"], self.widget_params["text_transform"],
            self.widget_params["font_weight"]))

            # Создаем комбобокс
            self.combo[m_name] = QComboBox()
            self.combo[m_name].setFixedHeight(28)  # Фиксированная высота для комбобокса

            # Set the style for combobox using parameters from config
            self.combo[m_name].setStyleSheet(f"""
                QComboBox {{
                    color: {self.widget_params.get('color', 'white')};
                    background-color: #333333;
                    padding: 2px;
                    margin-bottom: 6px;
                    font-size: {self.widget_params.get('font_size', '10px')};
                    text-align: left;
                    border: 1px solid #555555;
                    text-transform: {self.widget_params.get('text_transform', 'none')};
                    font-weight: {self.widget_params.get('font_weight', 'normal')};
                }}
                QComboBox::drop-down {{
                    border: none;
                }}
                QComboBox::down-arrow {{
                    width: 10px;
                    height: 12px;
                }}
                QComboBox QAbstractItemView {{
                    background-color: #333333;
                    color: {self.widget_params.get('color', 'white')};
                    selection-background-color: #555555;
                    font-size: {self.widget_params.get('font_size', '10px')};
                    padding: 0px;
                }}
                QComboBox QAbstractItemView::item {{
                    min-height: 18px;
                    padding: 0px;
                }}
            """)

            # Переводим значения для текущей локали
            translated_values = []
            for item_value in sm_name.values():
                translated_values.append(QApplication.translate("MachineStateManagement", item_value))

            # Добавляем элементы в комбобокс
            self.combo[m_name].addItems(translated_values)

            # По умолчанию все элементы неактивны
            for i in range(self.combo[m_name].count()):
                self.combo[m_name].model().item(i).setEnabled(False)

            # Добавляем виджеты в сетку
            self.grid.addWidget(lbl, index, 0)
            self.grid.addWidget(self.combo[m_name], index, 1)

            # Включаем комбобокс и подключаем сигнал изменения текста
            self.combo[m_name].setEnabled(True)
            self.combo[m_name].currentTextChanged.connect(self.prep_confirm)

        self.edit_btn = QPushButton()
        self.edit_btn.setStyleSheet(f"background: red; color: {self.widget_params.get('color', 'white')}; font-size: 18px; text-transform: {self.widget_params.get('text_transform', 'none')};")
        # self.edit_btn.setText("Разблокировать")
        self.edit_btn.setText(self.tr('Unlock'))
        self.edit_btn.clicked.connect(self.process_edit_switch)

        self.grid.addWidget(self.edit_btn, self.grid.rowCount() + 1, 1)

        self.widget.setLayout(self.grid)

    def process_edit_switch(self) -> None:
        """
        Toggle the edit mode for state machine comboboxes.

        When enabled (after password authentication), allows changing states.
        When disabled, prevents state changes.
        """
        if self.edit_enabled:
            for k in self.combo.values():
                k.blockSignals(True)
                for i in range(k.count()):
                    k.model().item(i).setEnabled(False)
                k.blockSignals(False)
            self.edit_btn.setStyleSheet(f"background: red; color: {self.widget_params.get('color', 'white')}; font-size: 18px; text-transform: {self.widget_params.get('text_transform', 'none')};")
            # self.edit_btn.setText("Разблокировать")
            self.edit_btn.setText(self.tr("Unlock"))
            self.edit_enabled = False

        else:
            # pwd, ok = QInputDialog.getText(self.widget, self.tr('Dangerous feature'),
            #                                self.tr('You are activating a dangerous function.\nIf used incorrectly, there is a risk of damaging the machine.\nContinue only if you know what you are doing.\n\nEnter your password:'),
            #                                QLineEdit.EchoMode.Password)
            input_dialog = QInputDialog(self.widget)
            input_dialog.setWindowTitle(self.tr('Dangerous feature'))
            input_dialog.setLabelText(self.tr(
                'You are activating a dangerous function.\nIf used incorrectly, there is a risk of damaging the machine.\nContinue only if you know what you are doing.\n\nEnter your password:'))
            input_dialog.setTextEchoMode(QLineEdit.EchoMode.Password)
            input_dialog.setOkButtonText(self.tr('Ok'))
            input_dialog.setCancelButtonText(self.tr('Cancel'))

            result = input_dialog.exec()

            if result == QDialog.DialogCode.Accepted:
                pwd = input_dialog.textValue()
                ok = True
            else:
                pwd = ""
                ok = False

            if ok and hashlib.md5(pwd.encode()).hexdigest() == self.core.config['admin_pass_hash']:
                for k in self.combo.values():
                    k.blockSignals(True)
                    for i in range(k.count()):
                        k.model().item(i).setEnabled(True)
                    k.blockSignals(False)
                self.edit_btn.setStyleSheet(f"background: green; color: {self.widget_params.get('color', 'white')}; font-size: 18px; text-transform: {self.widget_params.get('text_transform', 'none')};")
                # self.edit_btn.setText("Заблокировать")
                self.edit_btn.setText(self.tr("Lock"))
                self.edit_enabled = True

    def prep_confirm(self, state) -> None:
        """
        Prepare and show the confirmation dialog for a state change.

        Args:
            state: The new state that was selected in the combobox
        """

        source = self.widget.sender()
        self.node_choice = None

        for k in self.combo:
            self.state_combobox = False
            self.combo[k].setEnabled(False)

        layout = QVBoxLayout()

        btn_ok = QPushButton()
        # btn_ok.setText("Подтвердить")
        btn_ok.setText(self.tr("Commit"))
        btn_ok.setStyleSheet("color: %s; background: %s; text-transform: %s;" % (
        self.confirm_params["color"], self.confirm_params["background_ok"], self.confirm_params["text_transform"]))
        btn_ok.resize(100, 100)
        btn_ok.move(100, 100)

        btn_no = QPushButton()
        # btn_no.setText("Отмена")
        btn_no.setText(self.tr("Cancel"))
        btn_no.setStyleSheet("color: %s; background: %s; text-transform: %s;" % (
        self.confirm_params["color"], self.confirm_params["background_no"], self.confirm_params["text_transform"]))
        btn_no.resize(100, 100)
        btn_no.move(100, 100)

        for key, val in self.combo.items():
            if val == source:
                # sm_name = [list(self.state_machine[k][key]) for k in self.state_machine if list(self.state_machine[k])[0] == key][0]
                self.node_choice = [k for k in self.state_machine if list(self.state_machine[k])[0] == key][0]
                break

        current_sm_pre = \
        [dict(self.state_machine[k][self.telemetry[k]]) for k in self.state_machine if k == self.node_choice][0]
        self.goto_state = \
        [k for k in current_sm_pre if QApplication.translate("MachineStateManagement", current_sm_pre[k]) == state][0]

        btn_ok.clicked.connect(self.btn_ok_clicked)
        btn_no.clicked.connect(self.btn_no_clicked)

        layout.addWidget(btn_ok)
        layout.addWidget(btn_no)

        self.confirm.setLayout(layout)
        self.confirm.setFixedSize(300, 90)
        self.confirm.move(6650, 350)
        self.confirm.setStyleSheet("background: %s;" % (self.confirm_params["background"]))
        self.confirm.setWindowFlags(Qt.WindowType.CustomizeWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.confirm.setWindowOpacity(1)
        self.confirm.show()

    def btn_ok_clicked(self) -> None:
        """
        Handle OK button click in the confirmation dialog.

        Sends the state change command to the vehicle and closes the dialog.
        """
        cmd = {
            'state': {"sm_name": self.node_choice, "state_name": self.goto_state}
        }
        self.core.output_data[self.core.selected_vehid].update(cmd)
        self.confirm.destroy()
        for k in self.combo:
            self.state_combobox = True
            self.combo[k].setEnabled(True)

    def btn_no_clicked(self) -> None:
        """
        Handle Cancel button click in the confirmation dialog.

        Closes the dialog without sending any commands.
        """
        self.confirm.destroy()
        for k in self.combo:
            self.state_combobox = True
            self.combo[k].setEnabled(True)

    def get_from_telemetry(self, key):
        """
        Get a value from telemetry for the current vehicle.

        Args:
            key: The telemetry key to retrieve

        Returns:
            The telemetry value as a string, or None if not available
        """
        if self.core.in_rc_vehid is not None:
            vehid = self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            vehid = self.core.watched_vehid
        else:
            return None

        try:
            return str(format(self.core.telemetry[vehid][key]))
        except Exception as e:
            # More specific exception handling with logging
            print(f"Error getting telemetry for {key}: {e}")
            return None

    def start(self) -> None:
        """
        Start the widget update timer.
        """
        self.timer.start(100)  # Update every 100ms

    def update(self) -> None:
        """
        Update the widget state based on telemetry data.

        This method is called periodically by the timer to update the comboboxes
        with the current state machine states from telemetry.
        """
        selected = self.core.watched_vehid or self.core.in_rc_vehid
        if selected and self.state_combobox:
            for k in self.combo:
                self.combo[k].setEnabled(True)
        else:
            for k in self.combo:
                self.combo[k].setEnabled(False)

        if not selected:
            return

        current_sm_pre = {i: dict(self.state_machine[i][self.telemetry[i]]) for i in self.state_machine}

        for k in self.telemetry:
            res = self.get_from_telemetry(k)
            if res is None:
                print("No data for ", k)
                continue

            # Получаем переведенный текст текущего состояния
            current_text = QApplication.translate("MachineStateManagement", current_sm_pre[k][res])

            # Блокируем сигналы перед изменением текста
            self.combo[self.telemetry[k]].blockSignals(True)

            # Устанавливаем текущий текст
            self.combo[self.telemetry[k]].setCurrentText(current_text)

            # Apply appropriate style based on state
            if res == "failure":
                self.combo[self.telemetry[k]].setStyleSheet(f"""
                    QComboBox {{
                        background: red;
                        color: {self.widget_params.get('color', 'white')};
                        padding: 2px;
                        margin-bottom: 6px;
                        font-size: {self.widget_params.get('font_size', '10px')};
                        text-align: left;
                        border: 1px solid #555555;
                        text-transform: {self.widget_params.get('text_transform', 'none')};
                        font-weight: {self.widget_params.get('font_weight', 'normal')};
                    }}
                    QComboBox::drop-down {{
                        border: none;
                    }}
                    QComboBox::down-arrow {{
                        width: 10px;
                        height: 12px;
                    }}
                    QComboBox QAbstractItemView {{
                        background-color: #333333;
                        color: {self.widget_params.get('color', 'white')};
                        selection-background-color: #555555;
                        font-size: {self.widget_params.get('font_size', '10px')};
                        padding: 0px;
                    }}
                    QComboBox QAbstractItemView::item {{
                        min-height: 18px;
                        padding: 0px;
                    }}
                """)
            else:
                self.combo[self.telemetry[k]].setStyleSheet(f"""
                    QComboBox {{
                        background: #333333;
                        color: {self.widget_params.get('color', 'white')};
                        padding: 2px;
                        margin-bottom: 6px;
                        font-size: {self.widget_params.get('font_size', '10px')};
                        text-align: left;
                        border: 1px solid #555555;
                        text-transform: {self.widget_params.get('text_transform', 'none')};
                        font-weight: {self.widget_params.get('font_weight', 'normal')};
                    }}
                    QComboBox::drop-down {{
                        border: none;
                    }}
                    QComboBox::down-arrow {{
                        width: 10px;
                        height: 12px;
                    }}
                    QComboBox QAbstractItemView {{
                        background-color: #333333;
                        color: {self.widget_params.get('color', 'white')};
                        selection-background-color: #555555;
                        font-size: {self.widget_params.get('font_size', '10px')};
                        padding: 0px;
                    }}
                    QComboBox QAbstractItemView::item {{
                        min-height: 18px;
                        padding: 0px;
                    }}
                """)

            # Разблокируем сигналы после изменения
            self.combo[self.telemetry[k]].blockSignals(False)
