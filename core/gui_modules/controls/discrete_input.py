# Extracted class: DiscreteInput

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Optional, List, Any

from PyQt6 import QtCore
from PyQt6.QtCore import QObject, QTimer, Qt
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QGridLayout, QLabel

from customClasses.styledPushButton import StyledPushButton

class DiscreteInput(QObject):
    """A discrete input control for adjusting parameters with plus/minus buttons.

    This widget provides a way to adjust a numeric parameter in discrete steps,
    showing the current value as a percentage and allowing the user to increase
    or decrease it using plus and minus buttons.
    """

    def __init__(self, core: Any, text: str, param_name: str, step: float,
                 allowed_modes: List[str]) -> None:
        """Initialize the DiscreteInput control.

        Args:
            core: The core application object
            text: The label text to display
            param_name: The parameter name to control
            step: The step size for increments/decrements
            allowed_modes: List of modes where this control is active
        """
        super().__init__()
        self.core = core
        self.killed = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)
        self.value = 0.0
        # Import QApplication here to avoid circular imports
        from PyQt6.QtWidgets import QApplication
        self.label_text = QApplication.translate("SmartButton", text)
        self.param_name = param_name
        self.step = step
        self.allowed_modes = allowed_modes

    def prepare(self, width: int = 500, height: int = 500, x: int = 500, y: int = 800,
                font_size: str = "30px", screen_num: int = 0) -> None:
        """Prepare the widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            font_size: Font size for the label
            screen_num: Screen number (0 for sensor screen, 1+ for main screen)
        """
        # Select the appropriate parent screen
        if screen_num == 0:
            self.frame = QWidget(self.core.sensor_screen)
        else:
            self.frame = QWidget(self.core.main_screen)

        # Initial geometry setup
        self.frame.setGeometry(0, 0, width, height)

        # Create layouts
        stack = QVBoxLayout()
        grid = QGridLayout()
        self.frame.setLayout(stack)

        # Set window properties
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        # Position the frame based on screen number
        if screen_num == 0:
            self.frame.setGeometry(x, y, width, height)
        else:
            self.frame.setGeometry(self.core.screen_width * (screen_num - 1) + x, y, width, height)

        # Create plus button
        self.btn_plus = StyledPushButton()
        self.btn_plus.setPadding([15, 15, 25, 15])  # Increased padding for better visibility
        self.btn_plus.setText("+")
        self.btn_plus.setFontSize(18)  # Set explicit font size for better visibility

        # Create minus button
        self.btn_minus = StyledPushButton()
        self.btn_minus.setPadding([15, 15, 25, 15])  # Increased padding for better visibility
        self.btn_minus.setText("-")
        self.btn_minus.setFontSize(18)  # Set explicit font size for better visibility

        # Create label
        self.label = QLabel()
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet(f"font-size: {font_size};")
        self.update_text()

        # Connect signals
        self.btn_plus.clicked.connect(self.plus)  # Using new-style signal connection
        self.btn_minus.clicked.connect(self.minus)  # Using new-style signal connection

        # Assemble layout
        stack.addWidget(self.label)
        stack.addLayout(grid)
        grid.addWidget(self.btn_plus, 0, 1)
        grid.addWidget(self.btn_minus, 0, 0)

    def update_text(self) -> None:
        """Update the label text with the current value."""
        # Format with a slightly larger font for better readability
        percentage = round(self.value * 100)
        self.label.setText(f"<span style='font-weight: bold;'>{self.label_text}: {percentage}%</span>")

    def get_vehid(self) -> Optional[str]:
        """Get the current vehicle ID.

        Returns:
            The vehicle ID or None if no vehicle is selected
        """
        if self.core.in_rc_vehid is not None:
            return self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            return self.core.watched_vehid
        return None

    def plus(self) -> None:
        """Increase the value by one step."""
        vehid = self.get_vehid()
        if vehid is None:
            return

        # Create command to increase value
        cmd = {
            self.param_name: self.value + self.step
        }
        self.core.output_data[vehid].update(cmd)

    def minus(self) -> None:
        """Decrease the value by one step."""
        vehid = self.get_vehid()
        if vehid is None:
            return

        # Create command to decrease value
        cmd = {
            self.param_name: self.value - self.step
        }
        self.core.output_data[vehid].update(cmd)

    def start(self) -> None:
        """Start the widget and timer."""
        # Show all UI elements
        self.btn_plus.show()
        self.btn_minus.show()
        self.label.show()
        self.frame.show()

        # Start the update timer
        self.timer.start(100)  # 100ms interval for updates (reduced CPU usage)

    def update(self) -> None:
        """Update the widget state based on telemetry data."""
        vehid = self.get_vehid()

        # Check if we have valid telemetry data and are in an allowed mode
        invalid_data = (
            vehid is None or
            'channels_states' not in self.core.telemetry[vehid] or
            self.param_name not in self.core.telemetry[vehid]['channels_states'] or
            "MainStateMachineNode" not in self.core.telemetry[vehid] or
            self.core.telemetry[vehid]["MainStateMachineNode"] not in self.allowed_modes
        )

        if invalid_data:
            # Reset to default state when data is invalid
            self.value = 0.0
            self.btn_plus.setEnabled(False)
            self.btn_minus.setEnabled(False)
        else:
            # Update value from telemetry
            self.value = self.core.telemetry[vehid]['channels_states'][self.param_name]

            # Enable/disable plus button based on upper limit
            self.btn_plus.setEnabled(self.value + self.step <= 1.0)

            # Enable/disable minus button based on lower limit
            self.btn_minus.setEnabled(self.value - self.step >= 0.0)

        # Update the displayed text
        self.update_text()
