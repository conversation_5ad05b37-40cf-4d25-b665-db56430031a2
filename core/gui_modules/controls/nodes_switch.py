# Extracted class: NodesSwitch

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import time
from typing import Optional

from PyQt6.QtWidgets import QPushButton

class NodesSwitch(object):
    """
    A button widget for switching between different node states.

    This widget provides a button that, when clicked, sends a command to change
    the state of a specified state machine node.
    """

    def __init__(self, core):
        """
        Initialize the NodesSwitch widget.

        Args:
            core: The core application object
        """
        super().__init__()
        self.core = core
        self.killed = False
        self.core_screen = core.sensor_screen
        self.btn = None
        self.sm_name = None
        self.state_name = None
        self.vehid = None

    def kill(self) -> None:
        """
        Mark the widget as killed to stop update thread.
        """
        self.killed = True

    def prepare(self, x: int, y: int, btn_title: str, width: int, height: int,
                sm_name: str, state_name: str) -> None:
        """
        Prepare the button widget with the specified parameters.

        Args:
            x: X position of the button
            y: Y position of the button
            btn_title: Text to display on the button
            width: Width of the button
            height: Height of the button
            sm_name: State machine name to target
            state_name: State name to set when clicked
        """
        self.sm_name = sm_name
        self.state_name = state_name
        self.btn = QPushButton(btn_title, self.core_screen)
        self.btn.setFixedSize(width, height)
        self.btn.move(x, y)
        self.btn.setDisabled(True)
        self.btn.clicked.connect(self.change_nodes)
        self.btn.show()

    def change_nodes(self) -> None:
        """
        Send command to change the state machine node state.

        This method is called when the button is clicked.
        """
        self.core.output_data = {self.vehid: {"set_state": {"sm_name": self.sm_name, "state_name": self.state_name}}}

    def start(self) -> None:
        """
        Start the widget update thread.
        """
        update_thread = threading.Thread(target=self.update, daemon=True)
        update_thread.start()

    def update(self) -> None:
        """
        Update thread that enables/disables the button based on vehicle selection.
        """
        try:
            while not self.killed:
                # Get the current vehicle ID (controlled or watched)
                self.vehid = self.core.watched_vehid or self.core.in_rc_vehid

                # Enable button only if a vehicle is selected
                if self.vehid and self.btn:
                    self.btn.setDisabled(False)
                elif self.btn:
                    self.btn.setDisabled(True)

                # Sleep to reduce CPU usage
                time.sleep(0.1)  # Reduced update frequency to 10Hz
        except Exception as e:
            print(f"Error in NodesSwitch update thread: {e}")
