# Extracted class: EmergencySetter

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Any, Dict, Optional

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QWidget, QPushButton, QGridLayout

class EmergencySetter(QWidget):
    """A widget that provides emergency movement permission controls.

    This widget displays two buttons that allow the operator to permit or deny
    vehicle movement, sending the appropriate commands to the core system.
    """

    def __init__(self, core: Any) -> None:
        """Initialize the EmergencySetter widget.

        Args:
            core: The core application object
        """
        super().__init__(core.sensor_screen)
        self.core = core
        self.mov_perm = True  # Movement permission state

        # Create buttons
        self.stop_btn = QPushButton()
        self.resume_btn = QPushButton()

    def prepare(self, x: int, y: int, font_size: str = "20px",
                btn_height: int = 30, btn_width: int = 30, btn_space: int = 20) -> None:
        """Prepare the widget with the specified parameters.

        Args:
            x: X position of the widget
            y: Y position of the widget
            font_size: Font size for the buttons
            btn_height: Height of the buttons
            btn_width: Width of the buttons
            btn_space: Spacing between buttons
        """
        # Position the widget
        self.move(x, y)

        # Create layout
        layout = QGridLayout()
        layout.setSpacing(btn_space)

        # Configure stop button
        self.stop_btn.setText(self.tr('DENY MOVEMENT'))
        self.stop_btn.setStyleSheet(f"background: red; font-size: {font_size}")
        self.stop_btn.setFixedSize(btn_width, btn_height)
        self.stop_btn.clicked.connect(self.handle_stop_click)  # Simplified connection

        # Configure resume button
        self.resume_btn.setText(self.tr('PERMIT MOVEMENT'))
        self.resume_btn.setStyleSheet(f"background: green; font-size: {font_size}")
        self.resume_btn.setFixedSize(btn_width, btn_height)
        self.resume_btn.clicked.connect(self.handle_resume_click)  # Simplified connection

        # Add widgets to layout
        layout.addWidget(self.resume_btn, 0, 0)
        layout.addWidget(self.stop_btn, 0, 1)

        # Set the layout
        self.setLayout(layout)

    def handle_stop_click(self) -> None:
        """Handle click on the stop button.

        Sets movement permission to False and sends the command to the core.
        """
        self.mov_perm = False
        print("Got MP FALSE")  # Debug message

        # Create and send command
        mov_perm = {
            'move_permission': self.mov_perm
        }
        if self.core.selected_vehid is not None:
            self.core.output_data[self.core.selected_vehid].update(mov_perm)

    def handle_resume_click(self) -> None:
        """Handle click on the resume button.

        Sets movement permission to True and sends the command to the core.
        """
        self.mov_perm = True
        print("Got MP TRUE")  # Debug message

        # Create and send command
        mov_perm = {
            'move_permission': self.mov_perm
        }
        if self.core.selected_vehid is not None:
            self.core.output_data[self.core.selected_vehid].update(mov_perm)

    def start(self) -> None:
        """Start the widget.

        This method is called when the module is started.
        Currently does nothing as the widget is already visible after preparation.
        """
        pass
