# -*- coding: utf-8 -*-
"""Vehicle selector widget with Zenoh integration.

This module replaces the legacy VehicleSelectorWrapper + customClasses.vehicleSelector
combo with a single widget that:

1. Preserves ALL visual behaviour and UX of the original selector.
2. Uses Zenoh for data flow: live fields are consumed via ZenohWidget
   subscriptions, button actions are sent through Zenoh topic publications
   (configurable).
3. Provides `prepare()` / `start()` API so it can be loaded by the
   generic module loader exactly like other gui_modules.
"""
from __future__ import annotations

import json
import os
import logging
import sys
from functools import partial
from typing import Any, Dict, List, Optional
from threading import Timer

from PyQt6.QtCore import QPoint, QMargins, QSize, Qt
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QScrollArea,
    QSizePolicy,
    QScroller,
    QScrollerProperties,
    QMessageBox,
    QDialog,
    QHBoxLayout,
    QLabel,
    QTextEdit,
    QPushButton,
)
from PyQt6.QtGui import QPixmap

from gui_common import CONFIG, RESOURCES_CONFIG
from customClasses.darkPalette import darkPalette as DARK_PALETTE
from customClasses.darkPalette import ColorSet
from .vehicle_card import (
    VehicleCard,
    CardStatus,
)
from base_widget import ZenohWidget  # sticky-namespace capable

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s'
    ))
    logger.addHandler(handler)

logger.debug("Vehicle selector module loaded")

# ---------------------------------------------------------------------------
# Helper class that subscribes to Zenoh and updates a VehicleCard UI instance
# ---------------------------------------------------------------------------
class _CardDataBridge(ZenohWidget):
    """Connects a *VehicleCard* with Zenoh data streams."""

    # Mapping from stream key → callback on card
    _CB_MAP = {
        "online": "setVehicleActivity",
        "permission": "setPermission",
        "robomode": "setRobot",
        "emergency_status": "setEmergency",
    }

    def __init__(
        self,
        core: Any,
        vehid: str,
        streams_cfg: Dict[str, Dict[str, str]],
        card: VehicleCard,
    ) -> None:
        # Use a default data_timeout of 1.0 seconds, can be overridden after initialization
        super().__init__(core, fixed_namespace=vehid, data_timeout=1.0)
        self.card = card
        self.vehid = vehid
        
        # State values for tracking and warnings
        self.main_state = None 
        self.planner_state = None

        # Build data_sources dict expected by ZenohWidget.prepare()
        data_sources = {
            key: {
                "topic": cfg["topic"],
                "msg_type": cfg["msg_type"],
                "field": cfg["field"],
                # Add transform if it exists in config
                **({"transform": cfg["transform"]} if "transform" in cfg else {})
            }
            for key, cfg in streams_cfg.items()
        }
        
        # Configure freshness monitoring - use first topic to determine online status
        self.monitor_topic = next(iter(streams_cfg.values()))["topic"]
        self.data_freshness_changed.connect(self._handle_freshness_change)
        self.prepare(data_sources=data_sources)

    # Generic handler dispatch using CB_MAP
    def _dispatch_update(self, source_name: str, value: Any):
        # Track states needed for dialogs and mode management
        if source_name == "main_state":
            if self.main_state != value:
                self._handle_main_state_change(value)
            self.main_state = value
        elif source_name == "planner_state":
            self.planner_state = value
            
        # Update card based on mapping
        cb_name = self._CB_MAP.get(source_name)
        if cb_name and hasattr(self.card, cb_name):
            getattr(self.card, cb_name)(value)
        else:
            # Skip updating task directly if it's main_state - we handle it separately
            if source_name != "main_state":
                super()._dispatch_update(source_name, value)
    
    def _handle_main_state_change(self, new_state: str):
        """Handle changes in main state and update card mode accordingly."""
        # Update the task display
        self.card.setTask(new_state)
        
        # State transition mapping
        if "remote" in new_state.lower():
            # Vehicle is in remote control mode
            self.card.setMode(CardStatus.controlled)
        else:
            # Vehicle is in autonomous mode
            current_mode = self.card.getMode()
            if current_mode == CardStatus.want_auto:
                # This is an expected transition from want_auto to auto
                self.card.setMode(CardStatus.auto)
            elif current_mode == CardStatus.controlled:
                # This is an unexpected transition from controlled to auto
                # The card handler should handle this case
                self.card.setMode(CardStatus.auto)
            elif current_mode == CardStatus.disconnected:
                # Initial state or reconnection
                self.card.setMode(CardStatus.auto)
            
    # Monitor freshness for online status
    def _handle_freshness_change(self, source_name: str, is_fresh: bool):
        if source_name == list(self.data_sources.keys())[0]:  # Use first source as indicator
            logger.debug(f"Vehicle {self.vehid}: Setting activity status to {is_fresh}")
            self.card.setVehicleActivity(is_fresh)
    
    # Check if vehicle should show warning on watch
    def should_show_moving_warning(self) -> bool:
        """Check if the vehicle is in a state that requires a warning dialog when watching."""
        return (self.main_state == "remote" and 
                self.planner_state in ("moving", "approach", "computing"))


# ---------------------------------------------------------------------------
# Main widget exposed to loader
# ---------------------------------------------------------------------------
class VehicleSelector(QWidget):
    """Scrollable list of *VehicleCard*s with Zenoh integration."""

    def __init__(
        self,
        core: Any,
        streams_cfg: Dict[str, Dict[str, str]] = None,
        vehicles: Optional[List[str]] = None,
        publish_cfg: Optional[Dict[str, Dict[str, Any]]] = None,
        *args,
        **kwargs,
    ) -> None:
        super().__init__(*args, **kwargs)
        self.core = core
        self.zenoh = getattr(core, "zenoh_client", None)
        
        # Load iplace_id from settings for RMO health
        try:
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../settings.json')
            with open(settings_path, 'r') as settings_file:
                settings = json.load(settings_file)
                self.iplace_id = settings.get("iplace_id", 0)
        except Exception as e:
            logger.error(f"Failed to load iplace_id from settings: {e}")
            self.iplace_id = 0
            
        # RMO health timer
        self.health_timer = None
        self.health_interval = 1.0  # default - will be overridden by config

        # Vehicles list ---------------------------------------------------
        if vehicles is None:
            vehicles = list(CONFIG.get("VEHID2ADDRESS", {}).keys())
            if not vehicles:
                raise ValueError("No vehicles provided and CONFIG.VEHID2ADDRESS empty")
        self.vehicles = vehicles

        # Stream subscription & publication configuration ------------------------------
        self.streams_cfg = streams_cfg or {
            "main_state": {  # Used for main state monitoring 
                "topic": "main_state", 
                "msg_type": "drill_msgs/msg/StateMachineStatus",
                "field": "current_state"
            },
            "permission": {
                "topic": "permission",
                "msg_type": "drill_msgs/msg/Permission",
                "field": "permission"
            },
            "robomode": {
                "topic": "robomode",
                "msg_type": "drill_msgs/msg/BoolStamped",
                "field": "value"
            },
            "emergency_status": {
                "topic": "emergency_status",
                "msg_type": "drill_msgs/msg/BoolStamped",
                "field": "value"
            },
            # Add planner state for warning dialog
            "planner_state": {
                "topic": "planner_state", 
                "msg_type": "drill_msgs/msg/StateMachineStatus",
                "field": "current_state"
            }
        }
        
        # If user provided streams_cfg, ensure it has planner_state
        if streams_cfg and "planner_state" not in streams_cfg:
            self.streams_cfg["planner_state"] = {
                "topic": "planner_state", 
                "msg_type": "drill_msgs/msg/StateMachineStatus",
                "field": "current_state"
            }
        
        # Publication topics configuration ------------------------------
        self.publish_cfg = publish_cfg or {
            # Permission configuration
            "permission": {
                "topic": "permission",
                "msg_type": "drill_msgs/msg/Permission",
                "fields": {
                    "permission": True,  # Will be set dynamically
                    "source": "RMO"
                }
            },
            # Robot mode configuration
            "robomode": {
                "topic": "robomode_sp",
                "msg_type": "drill_msgs/msg/BoolStamped",
                "fields": {
                    "value": True  # Will be set dynamically
                }
            },
            # Switch to auto mode
            "auto": {
                "topic": "set_state",
                "msg_type": "drill_msgs/msg/StateCommand",
                "fields": {
                    "node_name": "MainStateMachine",
                    "state": "END_REMOTE"
                }
            },
            # Switch to remote control mode
            "control": {
                "topic": "set_state",
                "msg_type": "drill_msgs/msg/StateCommand",
                "fields": {
                    "node_name": "MainStateMachine",
                    "state": "REMOTE_WAIT"
                }
            },
            # Disconnect from vehicle
            "disconnect": {
                "topic": "set_state",
                "msg_type": "drill_msgs/msg/StateCommand",
                "fields": {
                    "node_name": "MainStateMachine",
                    "state": "END_REMOTE"
                }
            },
            # Reset emergency state
            "reset_emergency": {
                "topic": "reset_emergency",
                "msg_type": "drill_msgs/msg/EmergencyReset",
                "fields": {}
            }
        }

        # Internal containers --------------------------------------------
        self.cards: Dict[str, VehicleCard] = {}
        self._bridges: Dict[str, _CardDataBridge] = {}
        self.watched_vehid = None  # Currently watched vehicle

        # Basic UI (will be finalised in prepare()) -----------------------
        self.setPalette(DARK_PALETTE)

    # ------------------------------------------------------------------
    # Public API expected by module loader
    # ------------------------------------------------------------------
    def prepare(self, width: int, height: int, x: int, y: int, 
              transition_timeout_sec: int = 10, data_timeout_sec: float = 1.0, 
              health_update_sec: float = 1.0, *_, **__):
        """Create UI elements and layout.
        
        Args:
            width: Widget width
            height: Widget height
            x: X position
            y: Y position
            transition_timeout_sec: Timeout for state transitions in seconds
            data_timeout_sec: Timeout for data freshness in seconds
            health_update_sec: Interval for RMO health status updates in seconds
        """
        # Set health update interval from config
        self.health_interval = health_update_sec
        # Attach to sensor screen like other widgets
        parent_screen = getattr(self.core, "sensor_screen", None)
        if parent_screen is None:
            raise RuntimeError("core.sensor_screen missing – cannot attach selector")
        self.setParent(parent_screen)
        self.setFixedSize(QSize(width, height))
        self.move(QPoint(x, y))
        self.setAutoFillBackground(True)

        # Main vertical layout -----------------------------------------
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(QMargins(1, 1, 1, 1))
        main_layout.setSpacing(0)

        # Scroll area with inertial scrolling --------------------------
        scroll_wrapper = QWidget()
        scroll_layout = QVBoxLayout(scroll_wrapper)
        scroll_layout.setContentsMargins(QMargins(0, 0, 0, 0))
        scroll_layout.setSpacing(0)
        scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(scroll_wrapper)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setWidgetResizable(True)

        # Enable touch/trackpad kinetic scrolling
        scroller_props = QScrollerProperties()
        scroller_props.setScrollMetric(QScrollerProperties.ScrollMetric.OvershootScrollDistanceFactor, 0.2)
        scroller_props.setScrollMetric(
            QScrollerProperties.ScrollMetric.VerticalOvershootPolicy,
            QScrollerProperties.OvershootPolicy.OvershootAlwaysOn,
        )
        scroller = QScroller.scroller(self.scroll_area)
        scroller.setScrollerProperties(scroller_props)
        scroller.grabGesture(self.scroll_area, QScroller.ScrollerGestureType.TouchGesture)

        # Build vehicle cards -------------------------------------------
        for vehid in self.vehicles:
            card = VehicleCard(name=vehid, transition_timeout_sec=transition_timeout_sec)
            card.setBlinkColor(card.palette().color(card.palette().ColorRole.Window).darker())
            card.setBlinkingFrequency(300)
            card.setSizePolicy(QSizePolicy.Policy.MinimumExpanding, QSizePolicy.Policy.Fixed)

            # Signals --------------------------------------------------
            card.shouldWatch.connect(partial(self._on_auto_request, vehid))
            card.shouldControl.connect(partial(self._on_control_request, vehid))
            card.shouldRelease.connect(partial(self._on_release_request, vehid))
            card.shouldConnect.connect(partial(self._on_connect_request, vehid))
            card.clickedEmergencyButton.connect(partial(self._on_emergency_request, vehid))
            card.togglePermission.connect(self._on_toggle_permission)
            card.toggleRobot.connect(self._on_toggle_robot)
            card.wasExpanded.connect(partial(self._on_card_expanded, vehid, True))
            card.wasCollapsed.connect(partial(self._on_card_expanded, vehid, False))
            card.transitionTimedOut.connect(self._on_transition_timeout)
            
            # Data bridge --------------------------------------------
            bridge = _CardDataBridge(self.core, vehid, self.streams_cfg, card)
            bridge.data_timeout = data_timeout_sec
            bridge.start()

            # Keep references ----------------------------------------
            self.cards[vehid] = card
            self._bridges[vehid] = bridge
            scroll_layout.addWidget(card)

        # Spacer to keep cards top-aligned
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        scroll_layout.addWidget(spacer)

        main_layout.addWidget(self.scroll_area)
        
        # Connect watched_vehid with core
        if hasattr(self.core, "watched_vehid"):
            self._update_watched_vehicle(self.core.watched_vehid)

    def start(self):
        self.show()
        self._start_health_timer()
        
    def _start_health_timer(self):
        """Start a timer to periodically publish RMO health status to all vehicles."""
        self._publish_health_to_all()
        self.health_timer = Timer(self.health_interval, self._start_health_timer)
        self.health_timer.daemon = True
        self.health_timer.start()
        
    def _publish_health_to_all(self):
        """Publish health status to all vehicles."""
        for vehid in self.vehicles:
            self._publish_health_to_vehicle(vehid)
            
    def _publish_health_to_vehicle(self, vehid: str):
        """Publish RMO health status to a specific vehicle.
        
        Args:
            vehid: The vehicle ID to publish health to
        """
        is_watched = (vehid == self.watched_vehid)
        
        try:
            # All fields are set dynamically here rather than in the config
            self._publish_command(
                "rmo_health", 
                vehid, 
                iplace_id=self.iplace_id,
                is_watched=is_watched,
                watched_vehid=self.watched_vehid if self.watched_vehid else ""
            )
        except Exception as e:
            logger.debug(f"Failed to publish health to {vehid}: {e}")

    def closeEvent(self, event):
        """Handle widget close event to clean up resources.
        
        Args:
            event: The close event
        """
        # Stop health timer if running
        if self.health_timer:
            self.health_timer.cancel()
            self.health_timer = None
        
        # Call parent implementation
        super().closeEvent(event)

    # ------------------------------------------------------------------
    # Card signal handlers (UI → backend)
    # ------------------------------------------------------------------
    def _publish_command(self, action: str, vehid: str, **additional_fields):
        """Publish a command to the configured topic with configured fields.
        
        Args:
            action: Action key in publish_cfg (e.g., "auto", "control")
            vehid: Vehicle ID
            **additional_fields: Additional fields that override standard ones
        """
        bridge = self._bridges.get(vehid)
        if not bridge:
            logger.warning(f"Cannot send {action} command for vehicle {vehid}, no bridge")
            return False
            
        # Get configuration for the action
        cfg = self.publish_cfg.get(action, {})
        if not cfg:
            logger.error(f"No configuration for action {action}")
            return False
            
        topic = cfg.get("topic", "")
        msg_type = cfg.get("msg_type", "")
        
        if not topic or not msg_type:
            logger.error(f"Invalid configuration for action {action}: missing topic or msg_type")
            return False

        # Collect fields from configuration
        fields = cfg.get("fields", {}).copy()
        
        # Add or override fields from additional_fields
        fields.update(additional_fields)
        
        # Publish command
        logger.debug(f"Publishing {action} command for {vehid} to {topic}: {fields}")
        try:
            result = bridge.publish(
                topic=topic,
                msg_type=msg_type,
                **fields
            )
            return result
        except Exception as e:
            logger.error(f"Error publishing {action} command: {e}")
            QMessageBox.critical(self, self.tr("Publication error"), self.tr(f"Error sending command: {e}"))
            return False

    def _update_watched_vehicle(self, vehid: str):
        """Update which vehicle is being watched.
        
        Args:
            vehid: The ID of the vehicle to watch, or None to watch none
        """
        # Clear existing watched vehicle
        if self.watched_vehid and self.watched_vehid in self.cards:
            self.cards[self.watched_vehid].setWatched(False)
            
        # Set new watched vehicle
        self.watched_vehid = vehid
        
        # Update core if needed
        if hasattr(self.core, "setWatchedVehid"):
            self.core.setWatchedVehid(vehid)
            
        # Update card display
        if vehid and vehid in self.cards:
            self.cards[vehid].setWatched(True)
            
        # Update health status immediately after watch status changes
        self._publish_health_to_all()

    def _on_connect_request(self, vehid: str):
        """Handle request to connect to a vehicle for observation.
        
        Args:
            vehid: Vehicle ID
        """
        logger.debug(f"Connect request for {vehid}")
        
        # If this vehicle is already watched, disconnect it
        if self.watched_vehid == vehid:
            self._on_release_request(vehid)
            return
            
        # If another vehicle is being watched, disconnect it first
        if self.watched_vehid and self.watched_vehid != vehid:
            self._on_release_request(self.watched_vehid)
            
        # Mark this vehicle as watched
        self._update_watched_vehicle(vehid)

    def _on_auto_request(self, vehid: str):
        """Handle request to switch vehicle to autonomous mode.
        
        Args:
            vehid: Vehicle ID
        """
        logger.debug(f"Auto mode request for {vehid}")
        
        # Check if the vehicle is already in auto mode
        card = self.cards.get(vehid)
        if not card:
            logger.warning(f"No card found for vehicle {vehid}")
            return
            
        # Connect to the vehicle if not already watched
        if not card.isWatched():
            self._on_connect_request(vehid)
        
        # Check current mode
        current_mode = card.getMode()
        
        if current_mode == CardStatus.auto:
            logger.debug(f"Vehicle {vehid} already in auto mode")
            return
            
        if current_mode == CardStatus.controlled:
            # If in remote control, transition through want_auto
            card.setMode(CardStatus.want_auto)
            
        # Send command to switch to auto mode
        if self._publish_command("auto", vehid):
            # State updates will be handled by data streams
            pass

    def _on_control_request(self, vehid: str):
        """Handle request to switch vehicle to remote control mode.
        
        Args:
            vehid: Vehicle ID
        """
        logger.debug(f"Control request for {vehid}")
        
        # Check if the vehicle is already in control mode
        card = self.cards.get(vehid)
        if not card:
            logger.warning(f"No card found for vehicle {vehid}")
            return
            
        # Connect to the vehicle if not already watched
        if not card.isWatched():
            self._on_connect_request(vehid)
            
        # Check current mode
        current_mode = card.getMode()
        
        if current_mode == CardStatus.controlled:
            logger.debug(f"Vehicle {vehid} already in control mode")
            return
            
        if current_mode == CardStatus.auto:
            # If in auto mode, transition through want_remote
            card.setMode(CardStatus.want_remote)
            
        # Check for planner state that might require warning
        bridge = self._bridges.get(vehid)
        if bridge and bridge.should_show_moving_warning():
            # Show warning dialog about switching during movement
            warn_dialog = self._create_switch_to_moving_dialog(vehid)
            if not warn_dialog.exec():
                logger.debug("User canceled control request")
                # Revert to auto mode if canceled
                if current_mode == CardStatus.want_remote:
                    card.setMode(CardStatus.auto)
                return
                
        # Send command to switch to control mode
        if self._publish_command("control", vehid):
            # State updates will be handled by data streams
            # Set controlled vehicle in core
            if hasattr(self.core, "setControlledVehid"):
                self.core.setControlledVehid(vehid)

    def _on_release_request(self, vehid: str):
        """Handle request to disconnect from a vehicle.
        
        Args:
            vehid: Vehicle ID
        """
        logger.debug(f"Disconnect request for {vehid}")
        
        card = self.cards.get(vehid)
        if not card:
            logger.warning(f"No card found for vehicle {vehid}")
            return
            
        # Check if in remote control mode, which requires confirmation
        if card.getMode() == CardStatus.controlled:
            # Show confirmation dialog
            confirm_dialog = self._create_disconnect_while_controlling_dialog(vehid)
            if not confirm_dialog.exec():
                logger.debug("User canceled disconnect request")
                return
                
        # Clear watched status
        if self.watched_vehid == vehid:
            self._update_watched_vehicle(None)
            
        # Reset control status in core
        if hasattr(self.core, "disconnectFromAll"):
            self.core.disconnectFromAll()
        else:
            if hasattr(self.core, "setWatchedVehid") and self.core.watched_vehid == vehid:
                self.core.setWatchedVehid(None)
            if hasattr(self.core, "setControlledVehid") and self.core.controlled_vehid == vehid:
                self.core.setControlledVehid(None)

    def _on_emergency_request(self, vehid: str):
        """Handle request to reset emergency state.
        
        Args:
            vehid: Vehicle ID
        """
        logger.warning(f"Emergency reset for {vehid}")
        
        # Create and show emergency dialog
        emergency_dialog = self._create_emergency_dialog(vehid)
        if emergency_dialog.exec():
            # Send emergency reset command
            self._publish_command("reset_emergency", vehid)

    def _on_toggle_permission(self, vehid: str, permit: bool):
        logger.debug(f"Publishing move_permission {permit} for {vehid}")
        
        # Send command to change move permission
        self._publish_command("permission", vehid, permission=permit)

    def _on_toggle_robot(self, vehid: str, robot: bool):
        logger.debug(f"Publishing robot mode {robot} for {vehid}")
        
        # Send command to change robot mode
        self._publish_command("robomode", vehid, value=robot)

    def _on_card_expanded(self, vehid: str, expanded: bool):
        # Ensure only one card expanded at a time
        if expanded:
            for v, card in self.cards.items():
                if v != vehid:
                    card.setExpanded(False)
                    
    # ------------------------------------------------------------------
    # Dialog creation methods
    # ------------------------------------------------------------------
    def _create_emergency_dialog(self, vehid: str) -> QDialog:
        """Create emergency dialog with reset option."""
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Emergency") + ", vehicle " + vehid)
        dialog.setMinimumWidth(640)
        dialog.setMinimumHeight(360)
        
        layout_h = QHBoxLayout()
        layout_v = QVBoxLayout()
        
        label_img = QLabel()
        icon_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "emergency.png")
        pixmap = QPixmap(icon_file_path)
        label_img.setPixmap(pixmap.scaledToWidth(150))
        
        text = QTextEdit()
        text.textCursor().insertHtml(self.tr("<b>Emergency was set for the {} vehicle.</b><br><br>".format(vehid)))
        text.textCursor().insertHtml(self.tr("Operator can activate the emergency mode with a button on the "
                                      "control panel or remotely with a radio remote control button. <br><br>"
                                      "Motor would be automatically stopped in this mode. <br><br>"
                                      "Please identify the problem that caused the emergency and eliminate it. "
                                      "Verify with the cameras that the operation is safe. <br><br>"
                                      "Afterwards, you can reset the emergency with the button below. <br><br>"
                                      "To hide this window, press the cancel button. "
                                      "You can return to this window later by pressing the emergency button "
                                      "in the vehicle selection window."))
        text.setReadOnly(True)
        
        button_reset = QPushButton(self.tr("Reset emergency"))
        button_cancel = QPushButton(self.tr("Hide this dialog"))
        
        button_reset.clicked.connect(dialog.accept)
        button_cancel.clicked.connect(dialog.reject)
        
        layout_h.addWidget(label_img)
        layout_v.addWidget(text)
        layout_v.addWidget(button_reset)
        layout_v.addWidget(button_cancel)
        layout_h.addLayout(layout_v)
        layout_h.setContentsMargins(15, 15, 15, 15)
        dialog.setLayout(layout_h)
        
        return dialog
    
    def _create_disconnect_while_controlling_dialog(self, vehid: str) -> QDialog:
        """Create dialog warning about disconnecting while in control mode."""
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Disconnect From Control") + ", vehicle " + vehid)
        dialog.setMinimumWidth(640)
        dialog.setMinimumHeight(360)
        
        layout_h = QHBoxLayout()
        layout_v = QVBoxLayout()
        
        label_img = QLabel()
        icon_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "warning.png")
        pixmap = QPixmap(icon_file_path)
        label_img.setPixmap(pixmap.scaledToWidth(150))
        
        text = QTextEdit()
        text.textCursor().insertHtml(self.tr("<b>Vehicle {} is currently in remote control mode.</b><br>".format(vehid)))
        text.textCursor().insertHtml(self.tr("Disconnecting from a vehicle in remote control mode will "
                                      "automatically switch it to autonomous mode. <br><br>"
                                      "Are you sure you want to disconnect from this vehicle? <br><br>"
                                      "Press <b>Disconnect</b> to confirm or <b>Cancel</b> to keep controlling."))
        text.setReadOnly(True)
        
        button_disconnect = QPushButton(self.tr("Disconnect"))
        button_cancel = QPushButton(self.tr("Cancel"))
        
        button_disconnect.clicked.connect(dialog.accept)
        button_cancel.clicked.connect(dialog.reject)
        
        layout_h.addWidget(label_img)
        layout_v.addWidget(text)
        layout_v.addWidget(button_disconnect)
        layout_v.addWidget(button_cancel)
        layout_h.addLayout(layout_v)
        layout_h.setContentsMargins(15, 15, 15, 15)
        dialog.setLayout(layout_h)
        
        return dialog
    
    def _create_switch_to_moving_dialog(self, vehid: str) -> QDialog:
        """Create dialog warning about switching to autonomous mode."""
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Switch to Autonomous Moving") + ", vehicle " + vehid)
        dialog.setMinimumWidth(640)
        dialog.setMinimumHeight(360)
        
        layout_h = QHBoxLayout()
        layout_v = QVBoxLayout()
        
        label_img = QLabel()
        icon_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "warning.png")
        pixmap = QPixmap(icon_file_path)
        label_img.setPixmap(pixmap.scaledToWidth(150))
        
        text = QTextEdit()
        text.textCursor().insertHtml(self.tr("<b>Drill with vehicle id {} could move after switch to autonomous mode.</b><br>".format(vehid)))
        text.textCursor().insertHtml(self.tr("Drill could move after switch to autonomous mode, because planner is in Moving, Approach or Computing mode. <br><br>"
                                      "If this is what you expect, check the goal point and just press <b>Continue</b> button. <br><br>"
                                      "If you finished moving manually and want robot to continue with drilling, "
                                      "press <b>Finish moving and continue autonomous</b>."))
        text.setReadOnly(True)
        
        button_continue = QPushButton(self.tr("Continue"))
        button_finish_and_continue = QPushButton(self.tr("Finish moving and continue autonomous"))
        button_cancel = QPushButton(self.tr("Cancel"))
        
        button_continue.clicked.connect(dialog.accept)
        button_cancel.clicked.connect(dialog.reject)
        button_finish_and_continue.clicked.connect(lambda: self._finish_moving_and_continue(vehid, dialog))
        
        layout_h.addWidget(label_img)
        layout_v.addWidget(text)
        layout_v.addWidget(button_continue)
        layout_v.addWidget(button_finish_and_continue)
        layout_v.addWidget(button_cancel)
        layout_h.addLayout(layout_v)
        layout_h.setContentsMargins(15, 15, 15, 15)
        dialog.setLayout(layout_h)
        
        return dialog
    
    def _finish_moving_and_continue(self, vehid: str, dialog: QDialog) -> None:
        """Send finish moving command and accept dialog."""
        logger.debug(f"Sending finish moving command to vehicle {vehid}")
        cmd = {'finish_moving': True}
        if hasattr(self.core, "in_rc_vehid") and self.core.in_rc_vehid:
            self.core.output_data[self.core.in_rc_vehid].update(cmd)
        elif hasattr(self.core, "watched_vehid") and self.core.watched_vehid:
            self.core.output_data[self.core.watched_vehid].update(cmd)
        elif vehid in self.core.output_data:
            self.core.output_data[vehid].update(cmd)
        dialog.accept()

    # Add a method to handle transition timeouts
    def _on_transition_timeout(self, vehid: str, transition_type: str):
        """ (Temporary) Handle transition timeout events.
        
        Args:
            vehid: The vehicle ID
            transition_type: The type of transition that timed out
        """
        logger.error(f"Transition {transition_type} timed out for vehicle {vehid}")
        
        # Show error message to user
        if "want_auto" in transition_type:
            error_msg = self.tr(f"Transition to autonomous mode timed out for vehicle {vehid}")
            action_text = self.tr("The vehicle will remain in remote control mode.")
        elif "want_remote" in transition_type:
            error_msg = self.tr(f"Transition to remote control mode timed out for vehicle {vehid}")
            action_text = self.tr("The vehicle will remain in autonomous mode.")
        else:
            error_msg = self.tr(f"Transition timed out for vehicle {vehid}")
            action_text = self.tr("The vehicle state is unchanged.")
            
        # Display error message
        QMessageBox.warning(
            self,
            self.tr("State Transition Error"),
            f"{error_msg}\n\n{action_text}",
            QMessageBox.StandardButton.Ok
        )
        
        # Signal this error to any other components that need to know
        # This could be connected to an external error display widget
        if hasattr(self.core, "signal_error") and callable(self.core.signal_error):
            self.core.signal_error("vehicle_transition", vehid, transition_type) 