# Extracted class: JoystickWidgetWrapper

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, Optional

from PyQt6.QtCore import <PERSON><PERSON>argins, QSize, QPoint
from PyQt6.QtWidgets import QWidget, QVBoxLayout
from PyQt6.QtGui import QPalette

from customClasses.darkPalette import darkPalette
from drillJoystickWidget.joystick import DrillJoystickWidget

class JoystickWidgetWrapper(QWidget):
    """
    Widget for displaying the drilling joystick position.

    This widget wraps the DrillJoystickWidget and handles the display of joystick data
    received from the core application.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """
        Initialize the JoystickWidgetWrapper.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.core = core
        self.core_screen = core.sensor_screen
        self.parentWrapper: Optional[QWidget] = None
        self.joyWid: Optional[DrillJoystickWidget] = None

        # Connect signal for joystick data updates
        self.core.newJoystickDataReady.connect(self.processDataForJoysticks)

    def prepare(self, width: int, height: int, x: int, y: int, modes: Dict[str, Any]) -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            modes: Dictionary of joystick modes
        """
        # Create wrapper widget for parenting
        self.parentWrapper = QWidget(parent=self.core_screen)
        self.parentWrapper.setPalette(darkPalette)

        # Create and configure layout
        mainLayout = QVBoxLayout()
        mainLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        mainLayout.setSpacing(0)  # Remove spacing between widgets
        self.parentWrapper.setLayout(mainLayout)

        # Set size and position
        self.parentWrapper.setFixedSize(QSize(width, height))
        self.parentWrapper.move(QPoint(x, y))
        self.parentWrapper.setAutoFillBackground(True)

        # Create the joystick widget
        self.joyWid = DrillJoystickWidget(parent=self.parentWrapper, core=self.core, modes=modes)
        self.joyWid.setPalette(darkPalette)

        # Set text color from palette
        color = darkPalette.color(QPalette.ColorRole.WindowText)
        self.parentWrapper.setStyleSheet(f"color: {color.name()}")

        # Connect signals
        self.joyWid.joystickNonlinearityToggled.connect(self.core.setJoystickNonlinearity)

        # Add widget to layout
        mainLayout.addWidget(self.joyWid)

    def processDataForJoysticks(self, data: Optional[Dict[str, Any]]) -> None:
        """
        Receive signal from core with joystick data and send it to joystick widget's slots.

        Args:
            data: Dictionary containing joystick data
        """
        if data is None or self.joyWid is None:
            return

        # Red and green buttons
        buttonState = data.get('button_pair')
        if buttonState is not None:
            self.joyWid.setButtons(buttonState)

        # Turning knob
        turningKnobState = data.get('pot_value')
        if turningKnobState is not None:
            self.joyWid.setKnobValue(turningKnobState * 100)

        # Tumbler
        tumblerState = data.get('switch')
        if tumblerState is not None:
            self.joyWid.setTumbler(tumblerState)

        # Joystick 1
        joy1State = data.get('joystick1')
        joy1ButtonState = data.get('joystick1_button')
        if (joy1State is not None) and (joy1ButtonState is not None):
            self.joyWid.setJoystick(1, joy1State * 100, joy1ButtonState == 1)

        # Joystick 2
        joy2State = data.get('joystick2')
        joy2ButtonState = data.get('joystick2_button')
        if (joy2State is not None) and (joy2ButtonState is not None):
            self.joyWid.setJoystick(2, joy2State * 100, joy2ButtonState == 1)

        # Joystick 3
        joy3State = data.get('joystick3')
        joy3ButtonState = data.get('joystick3_button')
        if (joy3State is not None) and (joy3ButtonState is not None):
            self.joyWid.setJoystick(3, joy3State * 100, joy3ButtonState == 1)

    def start(self) -> None:
        """
        Start the widget by showing the parent wrapper.
        """
        if self.parentWrapper:
            self.parentWrapper.show()
