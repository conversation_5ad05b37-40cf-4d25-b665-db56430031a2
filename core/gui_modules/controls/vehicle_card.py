from typing import Dict, List, Any, Union

from PyQt6 import QtGui, QtWidgets
from PyQt6.QtWidgets import (
    QWidget, QGroupBox, QSizePolicy,
    QVBoxLayout, QHBoxLayout, QPushButton, QApplication,
    QLabel, QScrollArea, QDialog, QTextEdit
)
from PyQt6.QtWidgets import QScroller, QScrollerProperties, QGridLayout
from PyQt6.QtCore import (
    Qt, QPoint,
    pyqtSignal, pyqtProperty, QSize, QMargins, QTimer
)
from PyQt6.QtGui import QPainter, QColor, QPixmap, QPalette, QBrush

import logging
from enum import Enum
import os
import json
from functools import partial

from customClasses.styledPushButton import StyledPushButton
from customClasses.styledCheckBox import StyledCheckBox
from customClasses.ledWithCaption import LedWithCaption
from customClasses.darkPalette import ColorSet
from customClasses.separator import Separator
from customClasses.blinkBehaviour import BlinkingBehaviour

dir_path = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(dir_path, '../../resources_settings.json')
with open(config_path, 'r') as config:
    RESOURCES_CONFIG = json.load(config)


class CardStatus(Enum):
    """Enum representing the status of a vehicle card.

    Attributes:
        disconnected: The vehicle is not being controlled
        auto: The vehicle is in autonomous mode
        controlled: The vehicle is being controlled remotely
        want_auto: The vehicle is transitioning to autonomous mode
        want_remote: The vehicle is transitioning to remote control mode
    """
    disconnected = 0
    auto = 1  # Renamed from watched to auto
    controlled = 2
    want_auto = 3  # Waiting to transition to auto mode
    want_remote = 4  # Waiting to transition to remote control mode


class VehicleCard(QtWidgets.QWidget, BlinkingBehaviour):
    """Widget representing a vehicle card in the vehicle selector.

    This widget displays vehicle information and provides controls for watching,
    controlling, and managing vehicle settings.
    """
    # Signal definitions with documentation
    shouldWatch = pyqtSignal(str)  # user clicks 'auto' button on card
    shouldControl = pyqtSignal(str)  # user clicks 'control' button on card with given vehid
    shouldRelease = pyqtSignal(str)  # user wants to disconnect, clear core's watch and rc vehid
    shouldConnect = pyqtSignal(str)  # user wants to connect to this vehicle (become watched)
    togglePermission = pyqtSignal(str, bool)  # user wants to change move permission state
    toggleRobot = pyqtSignal(str, bool)  # user wants to change robomode
    wasCollapsed = pyqtSignal(str)  # card was collapsed
    wasExpanded = pyqtSignal(str)  # card was expanded
    clickedEmergencyButton = pyqtSignal(str)  # emergency button was clicked
    transitionTimedOut = pyqtSignal(str, str)  # transition timed out (vehid, transition_type)
    
    def __init__(self, name: str, vehicleType: int = 0, transition_timeout_sec: int = 10, *args, **kwargs) -> None:
        """Initialize the VehicleCard.

        Args:
            name: The vehicle ID/name
            vehicleType: Type of vehicle (experimental feature)
            transition_timeout_sec: Timeout for state transitions in seconds
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)

        # Set up properties for connected vehicle
        self._vehid = name  # str vehicle name (id)
        self._status = False  # boolean True if card is online
        self._task = ""  # str with current task
        self._mode = CardStatus.disconnected  # vehicle mode - auto, controlled, disconnected
        self._robot = False  # vehicle robot status, True is robot
        self._movePermission = False  # vehicle permission boolean, True if permit
        self._vehicleType = vehicleType  # experimental: when different types of vehicles are present
        self._rcNeeded = False  # whether remote control is needed
        self._emergency = False  # emergency status
        self._oldPos = None  # for tracking mouse movement
        self._watched = False  # Flag to indicate if this is the watched vehicle
        self._transition_timer = None  # Timer for state transitions
        self._transition_timeout = transition_timeout_sec * 1000  # Convert seconds to milliseconds

        self.setAutoFillBackground(True)
        self._color = ColorSet.buttonRegularColor.value.darker()

        # card properties
        self._expanded = False
        self._initUI()

        # initialize card as disabled
        self.setVehicleActivity(False)

    # ----------------------------------------------------------------------------------
    # Private methods - for internal use only
    # ----------------------------------------------------------------------------------
    
    def _initUI(self):
        """Initialize the UI components of the card."""
        # create layouts and widgets
        mainLayout = QVBoxLayout()
        # main panel stores all the widgets and layout
        # except for mainLayout = its top-layer layout element

        # mainPanel = QGroupBox('Vehicle')
        mainPanel = QGroupBox()
        # mainPanel.setAutoFillBackground(True)
        # self.setAutoFillBackground(True)

        palette = self.palette()
        palette.setColor(QPalette.ColorRole.Window, Qt.GlobalColor.yellow)
        mainPanel.setPalette(palette)

        palette.setColor(QPalette.ColorRole.Window, Qt.GlobalColor.green)
        self.setPalette(palette)

        mainPanelLayout = QVBoxLayout()
        headerLayout = QHBoxLayout()
        ledLayout = QHBoxLayout()
        buttonsLayout = QGridLayout()
        togglesLayout = QVBoxLayout()

        self.vehidCaption = QLabel(text=self._vehid.upper())
        self.taskCaption = QLabel(text=self.tr("Task: No info"))
        tridotImage = TridotLabel()
        self.statusLED = LedWithCaption(text=self.tr("Offline"))
        self.errorsLED = LedWithCaption(text=self.tr("No info"))
        self.modeLED = LedWithCaption(text=self.tr("No info"))

        # expandable area is just a wrapper to store controls when card is expanded
        self.expandableArea = QWidget()
        self.expandableArea.setAutoFillBackground(True)
        self.expandableArea.setVisible(False)
        pal = self.palette()
        color = self.palette().color(QPalette.ColorRole.Text)
        color.setAlpha(5)
        pal.setColor(QPalette.ColorRole.Window, color)
        self.expandableArea.setPalette(pal)
        expandableAreaLayout = QVBoxLayout()
        self.autoButton = StyledPushButton(text=self.tr("Auto", "autonomous mode for the vehicle"),
                                          fontSize=18)
        # self.autoButton.setPalette(darkPalette)
        self.autoButton.setPadding([3, 10, 3, 10])
        self.autoButton.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        self.controlButton = StyledPushButton(text=self.tr("Control", "like control this vehicle remotely"),
                                              fontSize=18)
        self.emergencyButton = StyledPushButton(text=self.tr("Emergency", "reset emergency"),
                                                fontSize=18)
        self.connectButton = StyledPushButton(text=self.tr("Connect",
                                                            "connect to observe this vehicle"),
                                               fontSize=18)
        self.permissionCheckBox = StyledCheckBox(text=self.tr("Move Permission",
                                                              "does vehicle has permission to move?"),
                                                 fontSize=18, tracking=True)
        self.robotCheckBox = StyledCheckBox(
            text=self.tr("RoboMode", "Checkbox, if checked -> vehicle is robot, else manual"),
            fontSize=18, tracking=True)

        # wiring
        self.autoButton.clicked.connect(partial(self.shouldWatch.emit, self._vehid))
        self.controlButton.clicked.connect(partial(self.shouldControl.emit, self._vehid))
        self.connectButton.clicked.connect(self._toggleConnection)
        self.emergencyButton.clicked.connect(partial(self.clickedEmergencyButton.emit, self._vehid))
        self.permissionCheckBox.clicked.connect(self._permissionToggle)
        self.robotCheckBox.clicked.connect(self._robotToggle)

        # styling
        # line
        # captionBorderColor = self.palette().color(QPalette.ColorRole.Text)
        self.autoButton.setColor(ColorSet.buttonGreenColor.value)
        self.controlButton.setColor(ColorSet.buttonBlueColor.value)
        self.emergencyButton.setColor(ColorSet.buttonRedColor.value)
        self.connectButton.setColor(ColorSet.buttonRegularColor.value)
        self.permissionCheckBox.setColor(ColorSet.buttonRegularColor.value)
        self.robotCheckBox.setColor(ColorSet.buttonRegularColor.value)
        
        # Set up blinking configuration for buttons
        self.autoButton.setBlinkColor(ColorSet.buttonGreenColor.value.lighter(150))
        self.controlButton.setBlinkColor(ColorSet.buttonBlueColor.value.lighter(150))
        self.emergencyButton.setBlinkColor(ColorSet.buttonRedColor.value.lighter(150))
        self.autoButton.setBlinkingFrequency(500)
        self.controlButton.setBlinkingFrequency(500)
        self.emergencyButton.setBlinkingFrequency(500)
        
        # COMPOSING
        self.setLayout(mainLayout)
        mainLayout.setContentsMargins(QMargins(0, 0, 0, 2))

        for button in (self.autoButton, self.controlButton, self.connectButton, self.permissionCheckBox,
                       self.robotCheckBox):
            button.setMinimumHeight(65)
        self.emergencyButton.setMinimumHeight(50)
        # widgets in topmost place (always visible
        headerLayout.addWidget(self.vehidCaption, 2)
        headerLayout.addWidget(self.taskCaption, 3)
        headerLayout.addWidget(tridotImage)
        tridotImage.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)

        # LED statuses
        ledLayout.addWidget(self.statusLED)
        ledLayout.addWidget(self.errorsLED)
        ledLayout.addWidget(self.modeLED)

        # buttons to control vehicle
        buttonsLayout.addWidget(self.autoButton, 0, 0)
        buttonsLayout.addWidget(self.controlButton, 0, 1)
        buttonsLayout.addWidget(self.connectButton, 1, 0, 1, 0)

        # toggles to control some togglable parametes
        togglesLayout.addWidget(self.permissionCheckBox)
        togglesLayout.addWidget(self.robotCheckBox)

        mainLayout.addWidget(mainPanel)
        mainPanel.setLayout(mainPanelLayout)

        # mainLayout.addLayout(headerLayout)
        # mainLayout.addWidget(self.expandableArea)
        mainPanelLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        mainPanelLayout.addLayout(headerLayout)
        mainPanelLayout.addLayout(ledLayout)
        mainPanelLayout.addWidget(self.expandableArea)

        self.expandableArea.setLayout(expandableAreaLayout)
        expandableAreaLayout.setContentsMargins(QMargins(4, 4, 4, 4))
        # expandableAreaLayout.addLayout(ledLayout, 2)
        expandableAreaLayout.addWidget(self.emergencyButton)
        expandableAreaLayout.addLayout(buttonsLayout, 4)
        separatorColor = ColorSet.textColor.value
        separatorColor.setAlpha(50)
        expandableAreaLayout.addWidget(Separator(color=separatorColor))
        expandableAreaLayout.addLayout(togglesLayout, 10)

    def _paintCaption(self, color=None):
        """Paint the vehicle ID caption with appropriate styling.
        
        Args:
            color: Optional color override for the caption background
        """
        if not color:
            captionBackgroundColor = ColorSet.textColor.value
            captionBackgroundColor.setAlpha(20)
        else:
            captionBackgroundColor = color

        captionBorderColor = ColorSet.textColor.value
        captionBorderColor.setAlpha(50)
        if self._status:
            textColor = ColorSet.textColor.value
        else:
            textColor = ColorSet.dimmedTextColor.value
        basicStyleSheet = """
        border: 1px solid {};
        border-radius: 5px;
        padding: 10px;
        font-size: 22px;
        color: {};
        """.format(
            captionBorderColor.name(QColor.NameFormat.HexArgb),
            textColor.name()
        )
        backgroundStyleSheet = """
        background-color: {};
        """.format(
            captionBackgroundColor.name(QColor.NameFormat.HexArgb),
        )
        self.vehidCaption.setStyleSheet(basicStyleSheet + backgroundStyleSheet)

    def _updateMode(self):
        """Update visual representation of the card mode."""
        if not self._status:
            color = ColorSet.textColor.value
            color.setAlpha(20)
        elif self._mode == CardStatus.auto:
            color = ColorSet.buttonGreenColor.value
        elif self._mode == CardStatus.disconnected:
            color = ColorSet.buttonRegularColor.value
        elif self._mode == CardStatus.controlled:
            color = ColorSet.buttonOrangeColor.value
        elif self._mode == CardStatus.want_auto:
            color = ColorSet.buttonGreenColor.value
            color.setAlpha(150)  # Semi-transparent green for transition state
        elif self._mode == CardStatus.want_remote:
            color = ColorSet.buttonOrangeColor.value
            color.setAlpha(150)  # Semi-transparent orange for transition state
        else:
            color = ColorSet.buttonRegularColor.value

        self._paintCaption(color)
        
        # Stop blinking if not in transition state
        if self._mode not in (CardStatus.want_auto, CardStatus.want_remote):
            self.autoButton.setBlinking(False)
            self.controlButton.setBlinking(False)
            
    def _updateTasks(self):
        """Update the task caption with current task."""
        if self._task != "":
            # self.taskCaption.setText(self._task)
            tr_text = QApplication.translate("Task", self._task)
            self.taskCaption.setText(tr_text)
        else:
            self.taskCaption.setText(self.tr("No task",
                                             "Label in vehicle selector when no task given"))

    def _updateLEDs(self):
        """Update the LED indicators based on current state."""
        if self._status:
            self.statusLED.setColor(ColorSet.ledGreenColor.value)
            self.statusLED.setText(self.tr('Online', "LED indicator when vehicle is online"))

            if self._robot:
                self.modeLED.setColor(ColorSet.ledGreenColor.value)
                self.modeLED.setText(self.tr("Robot", "LED when vehicle is in Robo-mode"))
            else:
                self.modeLED.setColor(ColorSet.ledRedColor.value)
                self.modeLED.setText(self.tr("Manual", "LED when vehicle is not in Robo-mode"))
        else:
            # if vehicle offline then all LEDs should be disconnected
            self.statusLED.setColor(ColorSet.ledRedColor.value)
            self.statusLED.setText(self.tr('Offline', "LED indicator when vehicle is offline"))
            self.modeLED.setColor(ColorSet.ledRedColor.value)
            self.modeLED.setText(self.tr("No Data", "LED indicator when vehicle is offline"))
            self.errorsLED.setColor(ColorSet.buttonRedColor.value)
            self.errorsLED.setText(self.tr("No Data", "LED indicator when vehicle is offline"))

    def _updateButtons(self):
        """Update button states based on current vehicle state."""
        if not self._status:
            # if vehicle is not Online then all disabled
            self.autoButton.setEnabled(False)
            self.controlButton.setEnabled(False)
            self.connectButton.setEnabled(False)
            self.permissionCheckBox.setEnabled(False)
            self.robotCheckBox.setEnabled(False)
            self.emergencyButton.setVisible(False)
        else:
            # if vehicle is online, then follow conditions
            if self._mode == CardStatus.disconnected:
                self.autoButton.setEnabled(True)
                self.controlButton.setEnabled(True)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)
            elif self._mode == CardStatus.auto:
                self.autoButton.setEnabled(False)
                self.controlButton.setEnabled(True)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)
            elif self._mode == CardStatus.controlled:
                self.autoButton.setEnabled(True)
                self.controlButton.setEnabled(False)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)
            elif self._mode == CardStatus.want_auto:
                # self.autoButton.setEnabled(False)
                self.controlButton.setEnabled(False)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)
                # Make auto button blink
                self.autoButton.setBlinking(True)
            elif self._mode == CardStatus.want_remote:
                self.autoButton.setEnabled(False)
                # self.controlButton.setEnabled(False)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)
                # Make control button blink
                self.controlButton.setBlinking(True)
                
            # Update connect button separately
            self._updateConnectButton()
            
    def _permissionToggle(self, isPermit: Union[bool, Qt.CheckState]) -> None:
        """Called when permission check box is toggled.

        Args:
            isPermit: The new permission state (either a boolean or Qt.CheckState)
        """
        try:
            # Convert Qt.CheckState to boolean if needed
            if isinstance(isPermit, Qt.CheckState):
                is_permitted = (isPermit == Qt.CheckState.Checked)
            else:
                is_permitted = bool(isPermit)

            self.togglePermission.emit(self._vehid, is_permitted)
        except Exception as e:
            logging.error(f"Error in permission toggle: {e}")

    def _robotToggle(self, isRobot: Union[bool, Qt.CheckState]) -> None:
        """Called when robot mode check box is toggled.

        Args:
            isRobot: The new robot mode state (either a boolean or Qt.CheckState)
        """
        try:
            # Convert Qt.CheckState to boolean if needed
            if isinstance(isRobot, Qt.CheckState):
                is_robot_mode = (isRobot == Qt.CheckState.Checked)
            else:
                is_robot_mode = bool(isRobot)

            self.toggleRobot.emit(self._vehid, is_robot_mode)
        except Exception as e:
            logging.error(f"Error in robot toggle: {e}")

    def _toggleConnection(self):
        """Handle connect/disconnect button click.
        
        If not watched, emit signal to connect.
        If watched, emit signal to disconnect.
        """
        if self._watched:
            self.shouldRelease.emit(self._vehid)
        else:
            self.shouldConnect.emit(self._vehid)
            
    def _startTransitionTimer(self, transition_type):
        """Start a timer for state transition.
        
        Args:
            transition_type: The type of transition (want_auto or want_remote)
        """
        # Clear any existing timer
        if self._transition_timer:
            self._transition_timer.stop()
            
        # Create new timer
        self._transition_timer = QTimer(self)
        self._transition_timer.setSingleShot(True)
        self._transition_timer.timeout.connect(lambda: self._handleTransitionTimeout(transition_type))
        self._transition_timer.start(self._transition_timeout)
        
    def _handleTransitionTimeout(self, transition_type):
        """Handle timeout for state transitions.
        
        Args:
            transition_type: The type of transition that timed out
        """
        logging.warning(f"Transition to {transition_type} timed out for vehicle {self._vehid}")
        
        # Emit signal for external error handling
        self.transitionTimedOut.emit(self._vehid, str(transition_type))
        
        # Revert to the previous state
        if transition_type == CardStatus.want_auto:
            # If transitioning to auto timed out, revert to controlled mode
            if self._mode == CardStatus.want_auto:
                self.setMode(CardStatus.controlled)
        elif transition_type == CardStatus.want_remote:
            # If transitioning to remote timed out, revert to auto mode
            if self._mode == CardStatus.want_remote:
                self.setMode(CardStatus.auto)
                
    def _updateConnectButton(self):
        """Update the connect button text based on watched status."""
        if self._watched:
            self.connectButton.setText(self.tr("Disconnect"))
            self.connectButton.setColor(ColorSet.buttonRedColor.value)
            self.connectButton.setEnabled(True)
        else:
            self.connectButton.setText(self.tr("Connect"))
            self.connectButton.setColor(ColorSet.buttonRegularColor.value)
            self.connectButton.setEnabled(self._status)  # Enable only if vehicle is online

    # ----------------------------------------------------------------------------------
    # Event handlers - internal required by Qt
    # ----------------------------------------------------------------------------------
    
    def mousePressEvent(self, event: QtGui.QMouseEvent) -> None:
        """Handle mouse press events to track drag operations.
        
        Args:
            event: The mouse event
        """
        self._oldPos = event.pos()

    def mouseReleaseEvent(self, event: QtGui.QMouseEvent) -> None:
        """Handle mouse release events to toggle card expansion.

        Args:
            event: The mouse event
        """
        deltaPoint = self._oldPos - event.pos()

        if deltaPoint.manhattanLength() > 20:
            event.ignore()
        else:
            self.setExpanded(not self.isExpanded())
            # On macOS, ensure the event is accepted
            event.accept()

    # ----------------------------------------------------------------------------------
    # Public API - to be used by VehicleSelector
    # ----------------------------------------------------------------------------------
    
    def setTask(self, task: str) -> None:
        """Set the current task of the vehicle.
        
        Args:
            task: The current task name
        """
        self._task = task
        self.update()

    def setVehicleActivity(self, isActive: bool) -> None:
        """Set whether the vehicle is online/active.
        
        Args:
            isActive: True if the vehicle is online and active
        """
        self._status = isActive
        # update text color (dimmed if vehicle offline)
        if not isActive:
            textColor = ColorSet.textColor.value
        else:
            textColor = ColorSet.dimmedTextColor.value

        palette = self.palette()
        palette.setColor(QPalette.ColorRole.WindowText, textColor)
        self.setPalette(palette)

        self.update()

    def setRobot(self, isRobotMode: bool) -> None:
        """Set whether the vehicle is in robot mode.
        
        Args:
            isRobotMode: True if the vehicle is in robot (autonomous) mode
        """
        self._robot = isRobotMode
        self.robotCheckBox.setChecked(isRobotMode)
        if isRobotMode:
            self.modeLED.setColor(ColorSet.ledGreenColor.value)
            self.modeLED.setText(self.tr('Robot'))
        else:
            self.modeLED.setColor(ColorSet.ledRedColor.value)
            self.modeLED.setText(self.tr('Manual',
                                         "Caption for RED LED when vehicle is not permitted to move"))

        self.update()

    def setMode(self, mode: CardStatus) -> None:
        """Set the vehicle operation mode.
        
        Args:
            mode: The operation mode (auto, controlled, disconnected, want_auto, want_remote)
        """
        old_mode = self._mode
        self._mode = mode
        
        # Stop all blinking first
        self.autoButton.setBlinking(False)
        self.controlButton.setBlinking(False)
        self.setBlinking(False)
        
        # Handle transitions and blinking
        if mode == CardStatus.want_auto:
            self._startTransitionTimer(CardStatus.want_auto)
            # Ensure auto button has correct color before blinking
            self.autoButton.setColor(ColorSet.buttonGreenColor.value)
            # Start blinking
            if self._expanded:
                self.autoButton.setBlinking(True)
            else:
                self.setBlinking(True)
        elif mode == CardStatus.want_remote:
            self._startTransitionTimer(CardStatus.want_remote)
            # Ensure control button has correct color before blinking
            self.controlButton.setColor(ColorSet.buttonBlueColor.value)
            # Start blinking
            if self._expanded:
                self.controlButton.setBlinking(True)
            else:
                self.setBlinking(True)
        elif mode == CardStatus.auto and old_mode == CardStatus.want_auto:
            # Successful transition to auto
            if self._transition_timer:
                self._transition_timer.stop()
                self._transition_timer = None
        elif mode == CardStatus.controlled and old_mode == CardStatus.want_remote:
            # Successful transition to remote
            if self._transition_timer:
                self._transition_timer.stop()
                self._transition_timer = None
        elif mode == CardStatus.auto and old_mode == CardStatus.controlled:
            # Unexpected transition to auto - this should be handled in the selector
            logging.warning(f"Unexpected transition from controlled to auto for vehicle {self._vehid}")
        elif mode == CardStatus.controlled and old_mode == CardStatus.auto:
            # Unexpected transition to controlled - this should be handled in the selector
            logging.warning(f"Unexpected transition from auto to controlled for vehicle {self._vehid}")
            
        self.update()

    def setPermission(self, hasPermission: bool) -> None:
        """Set whether the vehicle has permission to move.
        
        Args:
            hasPermission: True if the vehicle is permitted to move
        """
        self._movePermission = hasPermission
        self.permissionCheckBox.setChecked(hasPermission)
        if hasPermission:
            self.errorsLED.setColor(ColorSet.ledGreenColor.value)
            self.errorsLED.setText(self.tr('Ok'))
        else:
            self.errorsLED.setColor(ColorSet.ledRedColor.value)
            self.errorsLED.setText(self.tr('Need Permission',
                                           "Caption for RED LED when vehicle is not permitted to move"))

        self.update()

    def setRCNeeded(self, isNeeded: bool) -> None:
        """Set whether remote control is needed for this vehicle.
        
        When true, causes appropriate blinking of the control button or card.
        
        Args:
            isNeeded: True when remote control is needed
        """
        self._rcNeeded = isNeeded
        
        # First stop any blinking that might be active
        if self._expanded:
            self.controlButton.setBlinking(False)
        else:
            self.setBlinking(False)
            
        # Then start blinking if needed
        if isNeeded:
            if self._expanded:
                # Ensure control button has correct color before blinking
                self.controlButton.setColor(ColorSet.buttonBlueColor.value)
                self.controlButton.setBlinking(True)
            else:
                self.setBlinking(True)

    def setEmergency(self, hasEmergency: bool) -> None:
        """Set whether the vehicle is in emergency state.
        
        When true, causes appropriate blinking of the emergency button or card.
        
        Args:
            hasEmergency: True when the vehicle is in emergency state
        """
        self._emergency = hasEmergency
        
        # First stop any existing blinking
        if self._expanded:
            self.emergencyButton.setBlinking(False)
        else:
            self.setBlinking(False)
            
        # Then set new blinking state
        if hasEmergency:
            self.emergencyButton.setVisible(True)
            if self._expanded:
                # Ensure emergency button has correct color
                self.emergencyButton.setColor(ColorSet.buttonRedColor.value)
                self.emergencyButton.setBlinking(True)
            else:
                self.setBlinking(True)
        else:
            self.emergencyButton.setBlinking(False)
            self.emergencyButton.setVisible(False)

    def isRCNeeded(self) -> bool:
        """Check if remote control is needed for this vehicle.
        
        Returns:
            True if remote control is needed
        """
        return self._rcNeeded
    
    def getVehicleId(self) -> str:
        """Get the vehicle ID/name.
        
        Returns:
            The vehicle ID
        """
        return self._vehid
    
    def isActive(self) -> bool:
        """Check if the vehicle is active/online.
        
        Returns:
            True if the vehicle is active/online
        """
        return self._status
        
    def getMode(self) -> CardStatus:
        """Get the current vehicle operation mode.
        
        Returns:
            The current mode (auto, controlled, disconnected, want_auto, want_remote)
        """
        return self._mode
        
    def hasEmergency(self) -> bool:
        """Check if the vehicle is in emergency state.
        
        Returns:
            True if the vehicle is in emergency state
        """
        return self._emergency

    def update(self) -> None:
        """Update the card's visual appearance based on current state.
        
        This method is called automatically when any state changes.
        """
        super().update()
        self._updateLEDs()
        self._updateButtons()
        self._updateTasks()
        self._updateMode()
        
        # Update background color based on watched status
        palette = self.palette()
        if self._watched:
            # Use a slightly different color to indicate watched status
            highlight_color = self._color.lighter(200)
            palette.setColor(QPalette.ColorRole.Window, highlight_color)
        else:
            palette.setColor(QPalette.ColorRole.Window, self._color)
        self.setPalette(palette)

    def setExpanded(self, isExpanded: bool) -> None:
        """Set whether the card is expanded.

        Args:
            isExpanded: Whether to expand the card
        """
        self._expanded = isExpanded
        if isExpanded:
            # expand this card
            self.expandableArea.show()
            try:
                self.wasExpanded.emit(self._vehid)
            except Exception as e:
                logging.error(f"Error emitting wasExpanded: {e}")

            # Stop whole card blinking
            self.setBlinking(False)
            
            # If in transition state, make appropriate button blink
            if self._mode == CardStatus.want_auto:
                self.autoButton.setColor(ColorSet.buttonGreenColor.value)
                self.autoButton.setBlinking(True)
            elif self._mode == CardStatus.want_remote:
                self.controlButton.setColor(ColorSet.buttonBlueColor.value)
                self.controlButton.setBlinking(True)
                
            # Handle RC needed
            if self.isRCNeeded():
                logging.debug('Try to expand but RC needed')
                self.controlButton.setColor(ColorSet.buttonBlueColor.value)
                self.controlButton.setBlinking(True)
                
            # Handle emergency
            if self.hasEmergency():
                self.emergencyButton.setColor(ColorSet.buttonRedColor.value)
                self.emergencyButton.setBlinking(True)
        else:
            self.expandableArea.hide()
            try:
                self.wasCollapsed.emit(self._vehid)
            except Exception as e:
                logging.error(f"Error emitting wasCollapsed: {e}")

            # Stop button blinking
            self.autoButton.setBlinking(False)
            self.controlButton.setBlinking(False)
            self.emergencyButton.setBlinking(False)
            
            # If in transition state, make the whole card blink
            if self._mode == CardStatus.want_auto or self._mode == CardStatus.want_remote:
                self.setBlinking(True)
            # Handle RC needed similarly
            elif self.isRCNeeded():
                logging.debug('Try to collapse but RC needed')
                self.setBlinking(True)
            # Handle emergency similarly
            elif self.hasEmergency():
                self.setBlinking(True)

    def isExpanded(self) -> bool:
        """Get whether the card is expanded.

        Returns:
            Whether the card is expanded
        """
        return self._expanded

    # Legacy property for compatibility
    expanded_ = pyqtProperty(bool, fset=setExpanded, fget=isExpanded)

    def setWatched(self, isWatched: bool) -> None:
        """Set whether this vehicle is being watched (observed).
        
        Args:
            isWatched: True if this is the currently watched vehicle
        """
        self._watched = isWatched
        self.update()
        
    def isWatched(self) -> bool:
        """Check if this vehicle is being watched.
        
        Returns:
            True if this is the currently watched vehicle
        """
        return self._watched


class TridotLabel(QWidget):
    """A custom widget that displays three vertical dots.

    This widget is used as a visual indicator in the vehicle card header.
    """

    def __init__(self, *args, **kwargs) -> None:
        """Initialize the TridotLabel.

        Args:
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)

    def paintEvent(self, a0: QtGui.QPaintEvent) -> None:
        """Paint the three dots.

        Args:
            a0: The paint event
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)

        # Create color (it's the text color in current color scheme)
        _color = self.palette().color(QPalette.ColorRole.Text)
        _brush = QBrush(_color, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        painter.setPen(Qt.PenStyle.NoPen)

        # Calculate x and y coordinates
        x = int(self.width()) / 2
        dotRadius = 3
        ys = [int(self.height()) / 4 * n for n in range(1, 4)]

        # Draw the dots
        for y in ys:
            painter.drawEllipse(QPoint(int(x), int(y)), dotRadius, dotRadius)

        return super().paintEvent(a0)

    def sizeHint(self) -> QSize:
        """Return the recommended size for the widget.

        Returns:
            The recommended size
        """
        return QSize(40, 40)


