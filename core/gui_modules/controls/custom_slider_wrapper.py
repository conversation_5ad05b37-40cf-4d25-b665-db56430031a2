# Extracted class: CustomSliderWrapper

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
from typing import Optional, List, Any

from PyQt6.QtCore import QObject, QTimer, QSize, QPoint, QMargins
from PyQt6.QtWidgets import QWidget, QVBoxLayout

from customClasses.customSliderWidget import CustomSlider
from customClasses.darkPalette import darkPalette

class CustomSliderWrapper(QObject):
    """ Slider for adjusting core parameters.
        Minimum height: 155 pixels!
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """Initialize the CustomSliderWrapper.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.core = core
        self.core_screen = core.sensor_screen
        self.timer = QTimer()
        self.timer.timeout.connect(self.get_from_veh)
        self.updated_once: List[str] = []  # List of vehicle IDs that have been updated

    def prepare(self, width: int, height: int, x: int, y: int, minValue: float, maxValue: float,
                units: str, caption: str, param_name: str, unit_scale: float = 1.0) -> None:
        """Prepare the slider widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            minValue: Minimum value for the slider
            maxValue: Maximum value for the slider
            units: Measurement units to display
            caption: Caption text for the slider
            param_name: Parameter name to control
            unit_scale: Scale factor for the value (default: 1.0)
        """
        self.param_name = param_name
        # Create layouts and wrapper widget for parenting
        self.parentWrapper = QWidget(parent=self.core_screen)
        mainLayout = QVBoxLayout()
        mainLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        self.parentWrapper.setLayout(mainLayout)
        self.parentWrapper.setPalette(darkPalette)
        self.parentWrapper.setFixedSize(QSize(width, height))
        self.parentWrapper.move(QPoint(x, y))
        self.parentWrapper.setAutoFillBackground(True)
        self.unit_scale = unit_scale

        # Create the slider widget
        from PyQt6.QtWidgets import QApplication
        self.slider = CustomSlider(
            caption=QApplication.translate("CustomSliderWrapper", caption),
            measureUnits=QApplication.translate("CustomSliderWrapper", units),
            minValue=minValue,
            maxValue=maxValue,
            initialValue=minValue,
        )

        # Connect signals
        self.slider.valueSubmited.connect(self.sendDataToCore)
        self.slider.valuePushed.connect(self.saveDataToFile)

        # Add to layout
        mainLayout.addWidget(self.slider)

    def get_from_veh(self) -> None:
        """Get parameter values from the vehicle and update the slider."""
        vehid = self.get_vehid()
        if vehid is not None:
            # Request parameter from vehicle
            if 'get_params' in self.core.output_data[vehid]:
                pl = json.loads(self.core.output_data[vehid]['get_params'])
                pl.append(self.param_name)
                self.core.output_data[vehid]['get_params'] = json.dumps(pl)
            else:
                self.core.output_data[vehid]['get_params'] = json.dumps([self.param_name])

            # Update slider with current value from telemetry
            if ('params' in self.core.telemetry[vehid] and
                self.param_name in self.core.telemetry[vehid]['params']):
                # Set the actual value display
                self.slider.setActualValue(self.core.telemetry[vehid]['params'][self.param_name] * self.unit_scale)

                # Initialize the slider value once
                if vehid not in self.updated_once:
                    self.updated_once.append(vehid)
                    self.slider.setValue(self.core.telemetry[vehid]['params'][self.param_name] * self.unit_scale)

    def start(self) -> None:
        """Start the widget and timer."""
        self.parentWrapper.show()
        self.timer.start(200)  # Update every 200ms

    def valueChanged(self, _: float) -> None:
        """Handle value change events (placeholder).

        Args:
            _: The new slider value (unused)
        """
        pass

    def saveDataToFile(self, _: float) -> None:
        """Save the parameter value to a file.

        Args:
            _: The value to save (unused directly, but needed for signal connection)
        """
        vehid = self.get_vehid()
        if vehid is not None:
            self.core.output_data[vehid]['dump_params'] = json.dumps([self.param_name])

    def get_vehid(self) -> Optional[str]:
        """Get the current vehicle ID.

        Returns:
            The vehicle ID or None if no vehicle is selected
        """
        if self.core.in_rc_vehid:
            return self.core.in_rc_vehid
        elif self.core.watched_vehid:
            return self.core.watched_vehid
        return None

    def sendDataToCore(self, value: float) -> None:
        """Send the parameter value to the core.

        Args:
            value: The value to send
        """
        # Convert from display value to actual value
        value /= self.unit_scale
        vehid = self.get_vehid()
        if vehid is not None:
            self.core.output_data[vehid]['set_params'] = json.dumps({self.param_name: value})
