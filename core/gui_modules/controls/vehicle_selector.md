# Виджет Выбора Машины

Настоящий документ представляет собой описание виджета выбора машины (vehicle, транспортное средство, ТС), состоящего из двух основных компонентов: `VehicleCard` (Карточка ТС) и `VehicleSelector` (Селектор ТС).

## Оглавление

1.  [Обзор Системы](#обзор-системы)
2.  [Компонент `VehicleCard` (Карточка ТС)](#компонент-vehiclecard-карточка-тс)
    *   [Сигналы (Выходы)](#сигналы-выходы-vehiclecard)
    *   [Публичный API (Входы)](#публичный-api-входы-vehiclecard)
    *   [Состояния](#состояния-vehiclecard)
    *   [Визуальные Элементы](#визуальные-элементы-vehiclecard)
3.  [Компонент `VehicleSelector` (Селектор ТС)](#компонент-vehicleselector-селектор-тс)
    *   [Инициализация и Конфигурация](#инициализация-и-конфигурация-vehicleselector)
    *   [Управление Карточками](#управление-карточками-vehicleselector)
    *   [Обработчики Сигналов](#обработчики-сигналов-vehicleselector)
    *   [Управление Состоянием](#управление-состоянием-vehicleselector)
    *   [Взаимодействие по Zenoh](#взаимодействие-по-zenoh-vehicleselector)
4.  [Компонент `_CardDataBridge` (Мост Данных Карточки)](#компонент-_carddatabridge-мост-данных-карточки)
    *   [Поток Данных](#поток-данных-_carddatabridge)
    *   [Переходы Состояний](#переходы-состояний-_carddatabridge)
5.  [Взаимодействие Компонентов Системы](#взаимодействие-компонентов-системы)
    *   [Сценарии Взаимодействия с Пользователем](#сценарии-взаимодействия-с-пользователем)
    *   [Процесс Подключения к ТС](#процесс-подключения-к-тс)
    *   [Переходы Между Режимами](#переходы-между-режимами)
    *   [Обработка Ошибок](#обработка-ошибок)

## Обзор Системы

Виджет предоставляет пользовательский интерфейс для мониторинга и управления основными режимами ТС. Она позволяет операторам:

*   Подключаться к ТС для наблюдения.
*   Переключаться между автономным режимом и режимом удаленного управления.
*   Управлять разрешениями на движение и режимом "робота" (robomode) для ТС.
*   Отслеживать статус и активность ТС.
*   Обрабатывать аварийные ситуации.
*   Передавать статус RMO (удаленного рабочего места оператора) на ТС.

Система состоит из трех основных компонентов:

1.  **`VehicleCard` (Карточка ТС)**: Индивидуальный виджет, представляющий одно ТС.
2.  **`VehicleSelector` (Селектор ТС)**: Контейнер и менеджер для Карточек ТС.
3.  **`_CardDataBridge` (Мост Данных Карточки)**: Связующее звено между потоками данных от ТС и пользовательским интерфейсом Карточки ТС.

## Компонент `VehicleCard` (Карточка ТС)

`VehicleCard` — это виджет, представляющий отдельное транспортное средство в интерфейсе. Он отображает информацию о ТС и предоставляет элементы управления для взаимодействия с ним.

### Сигналы (Выходы) `VehicleCard`

| Сигнал                 | Аргументы                            | Описание                                                                 |
| ---------------------- | ------------------------------------ | ------------------------------------------------------------------------ |
| `shouldWatch`          | `vehid: str`                         | Пользователь нажал кнопку "Авто" (запрос автономного режима).            |
| `shouldControl`        | `vehid: str`                         | Пользователь нажал кнопку "Контроль" (запрос удаленного управления).      |
| `shouldRelease`        | `vehid: str`                         | Пользователь хочет отключиться от ТС (сбросить наблюдение/управление).     |
| `shouldConnect`        | `vehid: str`                         | Пользователь хочет подключиться к этому ТС (сделать его "наблюдаемым").   |
| `togglePermission`     | `vehid: str, permit: bool`           | Пользователь изменил состояние разрешения на движение.                     |
| `toggleRobot`          | `vehid: str, robot: bool`            | Пользователь изменил режим "робота".                                      |
| `clicked`              | `vehid: str`                         | Пользователь кликнул на свободное место на карточке.                     |
| `wasCollapsed`         | `vehid: str`                         | Карточка была свернута.                                                  |
| `wasExpanded`          | `vehid: str`                         | Карточка была развернута.                                                |
| `clickedEmergencyButton` | `vehid: str`                         | Была нажата кнопка аварийной ситуации.                                   |
| `transitionTimedOut`   | `vehid: str, transition_type: str`   | Время ожидания перехода в другой режим истекло.                             |

### Публичный API (Входы) `VehicleCard`

| Метод                  | Параметры             | Описание                                                                                                |
| ---------------------- | --------------------- | ------------------------------------------------------------------------------------------------------- |
| `setVehicleActivity`   | `isActive: bool`      | Устанавливает статус ТС (онлайн/офлайн).                                                                |
| `setTask`              | `task: str`           | Устанавливает текущую задачу (main state), отображаемую в заголовке карточки.                                         |
| `setMode`              | `mode: CardStatus`    | Устанавливает режим работы (`disconnected`, `auto`, `controlled`, `want_auto`, `want_remote`).         |
| `setPermission`        | `hasPermission: bool` | Устанавливает статус разрешения на движение.                                                            |
| `setRobot`             | `isRobotMode: bool`   | Устанавливает режим "робота" (автономный/ручной).                                                         |
| `setEmergency`         | `hasEmergency: bool`  | Устанавливает аварийное состояние.                                                                      |
| `setRCNeeded`          | `isNeeded: bool`      | Устанавливает, требуется ли удаленное управление (для индикации миганием).                               |
| `setExpanded`          | `isExpanded: bool`    | Разворачивает/сворачивает карточку.                                                                     |
| `setWatched`           | `isWatched: bool`     | Устанавливает, является ли данное ТС "наблюдаемым" в данный момент.                                       |
| `isRCNeeded`           | -                     | Возвращает `True`, если требуется удаленное управление.                                                    |
| `getVehicleId`         | -                     | Возвращает идентификатор ТС.                                                                            |
| `isActive`             | -                     | Возвращает `True`, если ТС онлайн.                                                                       |
| `getMode`              | -                     | Возвращает текущий режим работы ТС.                                                                     |
| `hasEmergency`         | -                     | Возвращает `True`, если ТС в аварийном состоянии.                                                        |
| `isExpanded`           | -                     | Возвращает `True`, если карточка развернута.                                                             |
| `isWatched`            | -                     | Возвращает `True`, если это ТС является "наблюдаемым".                                                     |

### Состояния `VehicleCard`

#### Перечисление `CardStatus` (Режимы Карточки/ТС)
*   `disconnected` (0): ТС не подключено (не является ни "наблюдаемым", ни "управляемым").
*   `auto` (1): ТС находится в автономном режиме.
*   `controlled` (2): ТС находится под удаленным управлением.
*   `want_auto` (3): ТС находится в процессе перехода в автономный режим (ожидание подтверждения от ТС).
*   `want_remote` (4): ТС находится в процессе перехода в режим удаленного управления (ожидание подтверждения от ТС).

#### Статус Активности
*   `_status` (bool): `True`, если ТС онлайн; `False`, если офлайн.

#### Статус Наблюдения ("Watched")
*   `_watched` (bool): `True`, если данное ТС является текущим "наблюдаемым" ТС (т.е. оператор активно отслеживает именно это ТС).

#### Операционные Состояния
*   `_task` (str): Текущая выполняемая задача или состояние ТС.
*   `_robot` (bool): `True`, если ТС в режиме "робота"; `False`, если в ручном режиме.
*   `_movePermission` (bool): `True`, если ТС имеет разрешение на движение.
*   `_emergency` (bool): `True`, если ТС в аварийном состоянии.
*   `_rcNeeded` (bool): `True`, если ТС требует удаленного управления (например, для выполнения определенных задач).

#### Визуальные Состояния Карточки
*   `_expanded` (bool): `True`, если карточка развернута (показывает все элементы управления).
*   `_highlighted` (bool): `True`, если карточка дополнительно выделена (например, как "наблюдаемая").

### Визуальные Элементы `VehicleCard`

#### Постоянно Видимые Элементы
*   **Заголовок**: Идентификатор ТС и текущая задача/состояние.
*   **LED-индикаторы**:
    *   Индикатор статуса: Зеленый (Онлайн), Красный (Офлайн).
    *   Индикатор ошибок/разрешения: Зеленый (ОК/Разрешено), Красный (Ошибка/Запрещено).
    *   Индикатор режима: Зеленый (Робот), Красный (Ручной).

#### Элементы, Видимые при Развернутой Карточке
*   **Кнопки**:
    *   Кнопка "Авто": Переход в автономный режим.
    *   Кнопка "Контроль": Переход в режим удаленного управления.
    *   Кнопка "Подключить"/"Отключить": Подключение к ТС (делает его "наблюдаемым") или отключение.
    *   Кнопка "Авария": Сброс аварийного состояния (видима только при активной аварии).
*   **Флажки (Checkboxes)**:
    *   "Разрешение на движение": Включение/отключение разрешения на движение.
    *   "Режим робота": Включение/отключение режима "робота".

#### Цветовое Кодирование и Индикация
*   **Отключено (`disconnected`)**: Стандартный цвет фона.
*   **Автономный режим (`auto`)**: Зеленый цвет фона заголовка.
*   **Удаленное управление (`controlled`)**: Оранжевый/Синий (в зависимости от темы) цвет фона заголовка.
*   **Переход в `auto` (`want_auto`)**: Полупрозрачный зеленый фон заголовка, мигание.
*   **Переход в `controlled` (`want_remote`)**: Полупрозрачный оранжевый/синий фон заголовка, мигание.
*   **ТС Офлайн**: Затемненный текст на всех элементах.
*   **"Наблюдаемое" ТС (`_watched` = True)**: Дополнительное выделение карточки (например, более светлый фон).

#### Поведение Мигания
*   **Переходные состояния (`want_auto`, `want_remote`)**: Мигает соответствующая кнопка ("Авто" или "Контроль") если карточка развернута; мигает вся карточка, если свернута.
*   **Требуется удаленное управление (`_rcNeeded` = True)**: Мигает кнопка "Контроль" если карточка развернута; мигает вся карточка, если свернута.
*   **Аварийное состояние (`_emergency` = True)**: Мигает кнопка "Авария" если карточка развернута (и видима); мигает вся карточка, если свернута.

## Компонент `VehicleSelector` (Селектор ТС)

`VehicleSelector` — это прокручиваемый контейнер, который управляет несколькими экземплярами `VehicleCard`. Он обрабатывает взаимодействия пользователя с карточками и обменивается данными с ТС через Zenoh.

### Инициализация и Конфигурация `VehicleSelector`

#### Параметры Конструктора
*   `core`: Ссылка на ядро приложения (для доступа к общим ресурсам и Zenoh-клиенту).
*   `streams_cfg`: Конфигурация потоков данных от ТС (топики, типы сообщений, поля).
*   `vehicles`: Список идентификаторов ТС для отображения.
*   `publish_cfg`: Конфигурация для публикации команд на ТС.

#### Конфигурация Потоков Данных (Подписки)
Селектор подписывается на следующие потоки данных для каждого ТС:
*   `main_state`: Текущее состояние главного конечного автомата ТС.
*   `permission`: Статус разрешения на движение.
*   `robomode`: Статус режима "робота".
*   `emergency_status`: Аварийное состояние.
*   `planner_state`: Текущее состояние планировщика задач ТС (используется для предупреждений).

#### Конфигурация Публикаций (Команды)
Селектор может публиковать следующие команды на ТС:
*   `permission`: Установить разрешение на движение.
*   `robomode`: Включить/выключить режим "робота".
*   `auto`: Запросить переход в автономный режим.
*   `control`: Запросить переход в режим удаленного управления.
*   `disconnect`: Запросить отключение (обычно означает переход ТС в безопасный/автономный режим по умолчанию).
*   `reset_emergency`: Сбросить аварийное состояние.
*   `rmo_health`: Отправка статуса здоровья RMO (удаленного оператора) на ТС.

#### Основные параметры конфигурации
Селектор имеет несколько основных параметров, которые настраиваются в `prep_args` в конфигурационном файле:

*   `transition_timeout_sec`: Время ожидания (в секундах) для завершения перехода между режимами (например, из автономного в режим удаленного управления). По умолчанию: 3 секунды.
*   `data_timeout_sec`: Время ожидания (в секундах) для определения свежести данных, полученных от ТС. Используется для определения статуса онлайн/офлайн ТС. По умолчанию: 1 секунда.
*   `health_update_sec`: Интервал (в секундах) между отправками статуса RMO на ТС. По умолчанию: 1 секунда.

#### Конфигурация отправки статуса RMO
Селектор периодически публикует статус RMO для каждого ТС:
*   Данные включают:
    *   `iplace_id`: Идентификатор станции управления из settings.json.
    *   `is_watched`: Флаг, указывающий, наблюдается ли это конкретное ТС.
    *   `watched_vehid`: Идентификатор ТС, которое в данный момент наблюдается (если есть).

### Управление Карточками `VehicleSelector`

#### Создание и Настройка Карточек
В процессе инициализации Селектор:
1.  Создает экземпляр `VehicleCard` для каждого идентификатора ТС.
2.  Подключает сигналы от каждой `VehicleCard` к соответствующим обработчикам в `VehicleSelector`.
3.  Создает экземпляр `_CardDataBridge` для каждой карточки, связывая ее с потоками данных Zenoh.
4.  Размещает карточки в прокручиваемой области.

#### Управление "Наблюдаемым" ТС (`watched_vehid`)
Свойство `watched_vehid` отслеживает, какое ТС в данный момент активно наблюдается оператором.
*   Только одно ТС может быть "наблюдаемым" одновременно.
*   Метод `_update_watched_vehicle` обрабатывает переключение "наблюдаемого" ТС.
*   При выборе нового "наблюдаемого" ТС, предыдущее автоматически перестает быть таковым.
*   Выбор "наблюдаемого" ТС (`watched`) является необходимым условием для последующего перевода этого ТС в режим удаленного управления (`controlled`).

### Обработчики Сигналов `VehicleSelector`

#### Подключение к ТС
*   `_on_connect_request`: Обрабатывает запрос на подключение от карточки (делает ТС "наблюдаемым").
*   `_on_release_request`: Обрабатывает запрос на отключение от ТС (перестает быть "наблюдаемым", может также сбросить удаленное управление).
*   `_update_watched_vehicle`: Обновляет внутреннее состояние `watched_vehid` и соответствующие ссылки в ядре приложения.

#### Переходы Между Режимами
*   `_on_auto_request`: Обрабатывает запрос на перевод "наблюдаемого" ТС в автономный режим. Инициирует переход в `want_auto`, если ТС было в `controlled`.
*   `_on_control_request`: Обрабатывает запрос на перевод "наблюдаемого" ТС в режим удаленного управления. Инициирует переход в `want_remote`, если ТС было в `auto`.

#### Настройки ТС
*   `_on_toggle_permission`: Обрабатывает изменение разрешения на движение.
*   `_on_toggle_robot`: Обрабатывает изменение режима "робота".
*   `_on_emergency_request`: Обрабатывает запрос на сброс аварийного состояния.

#### Обработка Ошибок
*   `_on_transition_timeout`: Обрабатывает сигнал таймаута от `VehicleCard`, отображает сообщение об ошибке.

### Управление Состоянием `VehicleSelector`

#### Интеграция с Ядром Приложения
Селектор взаимодействует с ядром приложения для синхронизации глобального состояния:
*   `core.setWatchedVehid(vehid)`: Устанавливает текущее "наблюдаемое" ТС в ядре. `vehid` может быть `None`.
*   `core.setControlledVehid(vehid)`: Устанавливает текущее "управляемое" ТС в ядре. `vehid` может быть `None`.
*   `core.disconnectFromAll()`: Команда ядру на сброс всех активных подключений (и наблюдения, и управления).

#### Синхронизация Состояния Карточек
Селектор обеспечивает согласованность состояния между карточками:
*   Только одна карточка может быть развернута одновременно.
*   Только одно ТС может быть "наблюдаемым" (`watched`).
*   При переключении "наблюдаемого" ТС, предыдущее освобождается.
*   ТС должно быть "наблюдаемым" (`watched`), чтобы его можно было перевести в режим удаленного управления (`controlled`). Если оператор пытается перевести ТС в `controlled`, не сделав его `watched`, система сперва автоматически сделает его `watched`.

### Взаимодействие по Zenoh `VehicleSelector`

#### Публикация Команд
Метод `_publish_command` отправляет команды на ТС через Zenoh:
1.  Получает мост `_CardDataBridge` для целевого ТС.
2.  Извлекает конфигурацию команды (топик, тип сообщения, поля) из `publish_cfg`.
3.  Формирует полезную нагрузку сообщения, подставляя динамические значения.
4.  Публикует сообщение в настроенный топик Zenoh.

#### Публикация статуса RMO
Система периодически публикует информацию о статусе RMO:
1.  Инициализируется таймер, который запускает метод `_publish_health_to_all` с интервалом `health_update_sec`.
2.  Для каждого ТС вызывается метод `_publish_health_to_vehicle`.
3.  Формируется сообщение, содержащее:
    *   `iplace_id`: Идентификатор станции из settings.json.
    *   `is_watched`: `True`, если это конкретное ТС наблюдается в данный момент.
    *   `watched_vehid`: Идентификатор наблюдаемого ТС (пустая строка, если нет наблюдаемого ТС).
4.  Статус обновляется немедленно при изменении статуса наблюдения.
5.  При закрытии виджета таймер корректно останавливается.

#### Настройка сообщения статуса здоровья
Важные особенности конфигурации:
*   В конфигурационном файле (например `develop_modules.json`) определяется тип сообщения `drill_msgs/msg/RmoHealth`.
*   Секция `fields` намеренно оставлена пустой, так как все поля заполняются динамически в коде.
*   Изменение типа сообщения в конфигурации требует соответствующих изменений в системе:
    *   Новый тип сообщения должен поддерживать те же поля: `iplace_id`, `is_watched`, `watched_vehid`.
    *   Изменение может привести к ошибкам при публикации, если структура сообщения несовместима.
    *   Система обрабатывает ошибки публикации в блоке `try-except`, но функциональность мониторинга будет нарушена.

#### Отображение Диалоговых Окон
Селектор использует диалоговые окна для подтверждения критических действий или информирования пользователя:
*   `_create_emergency_dialog`: Для подтверждения сброса аварийного состояния.
*   `_create_disconnect_while_controlling_dialog`: Предупреждение при попытке отключиться от ТС, находящегося под удаленным управлением (подразумевает автоматический перевод ТС в автономный режим).
*   `_create_switch_to_moving_dialog`: Предупреждение, если ТС находится в движении или планирует движение при попытке переключить режим.

## Компонент `_CardDataBridge` (Мост Данных Карточки)

`_CardDataBridge` связывает экземпляр `VehicleCard` с потоками данных Zenoh, преобразуя обновления состояния ТС в обновления пользовательского интерфейса карточки.

### Поток Данных `_CardDataBridge`

#### Источники Данных
Мост подписывается на настроенные источники данных (топики Zenoh):
*   Подписки на топики для каждого источника данных.
*   Извлечение определенных полей из полученных сообщений.
*   Опциональное преобразование значений перед передачей в карточку.

#### Диспетчеризация Обновлений
При получении данных из Zenoh:
1.  Особая обработка для `main_state` (основное состояние ТС) и `planner_state` (состояние планировщика).
2.  Вызов соответствующих методов `VehicleCard` для обновления UI, используя карту `_CB_MAP`.
3.  Отслеживание "свежести" данных для определения статуса онлайн/офлайн ТС.

### Переходы Состояний `_CardDataBridge`

#### Обработка `main_state`
Метод `_handle_main_state_change` управляет обновлением режима `VehicleCard` на основе `main_state` от ТС:
*   Состояние ТС "remote" → режим карточки `CardStatus.controlled`.
*   Состояния ТС, соответствующие автономии ("idle", "approach", "moving", и т.д.) → режим карточки `CardStatus.auto`.
*   Состояние ТС "remote_wait" (ожидание перехода в удаленное управление) → режим карточки `CardStatus.want_remote`.
*   Обрабатывает ожидаемые и неожиданные переходы между режимами, обеспечивая корректное отображение в карточке.

#### Условия для Предупреждений
Метод `should_show_moving_warning` определяет, нужно ли выводить предупреждение:
*   Проверяет, находится ли ТС в режиме удаленного управления, но планировщик активен и ТС может двигаться.

## Взаимодействие Компонентов Системы

### Сценарии Взаимодействия с Пользователем

#### Подключение к ТС (Наблюдение)
1.  Пользователь нажимает кнопку "Подключить" на карточке ТС.
2.  `VehicleCard` генерирует сигнал `shouldConnect`.
3.  `VehicleSelector` обрабатывает сигнал через `_on_connect_request`.
4.  `VehicleSelector` обновляет `watched_vehid`, уведомляет ядро (`core.setWatchedVehid`) и обновляет состояние всех карточек.
5.  Целевая `VehicleCard` обновляется вызовом `setWatched(True)`.

#### Переключение в Автономный Режим
1.  Пользователь нажимает кнопку "Авто" на "наблюдаемой" карточке ТС.
2.  `VehicleCard` генерирует сигнал `shouldWatch`.
3.  `VehicleSelector` обрабатывает сигнал через `_on_auto_request`.
4.  Если ТС было в режиме удаленного управления, `VehicleCard` переводится в состояние `want_auto`.
5.  `VehicleSelector` публикует команду на переход в автономный режим.
6.  ТС отвечает изменением своего `main_state`.
7.  `_CardDataBridge` получает обновление и переводит `VehicleCard` в режим `auto` после успешного завершения перехода.

#### Переключение в Режим Удаленного Управления
1.  Пользователь нажимает кнопку "Контроль" на "наблюдаемой" карточке ТС.
2.  `VehicleCard` генерирует сигнал `shouldControl`.
3.  `VehicleSelector` обрабатывает сигнал через `_on_control_request`.
4.  Если ТС движется или планирует движение, отображается диалог предупреждения.
5.  При подтверждении, `VehicleCard` переводится в состояние `want_remote`.
6.  `VehicleSelector` публикует команду на переход в режим удаленного управления и уведомляет ядро (`core.setControlledVehid`).
7.  ТС отвечает изменением своего `main_state`.
8.  `_CardDataBridge` получает обновление и переводит `VehicleCard` в режим `controlled` после успешного завершения перехода.

#### Отключение от ТС
1.  Пользователь нажимает кнопку "Отключить" на "наблюдаемой" карточке ТС.
2.  `VehicleCard` генерирует сигнал `shouldRelease`.
3.  `VehicleSelector` обрабатывает сигнал через `_on_release_request`.
4.  Если ТС было под удаленным управлением, отображается диалог подтверждения.
5.  При подтверждении, `VehicleSelector` сбрасывает `watched_vehid` на `None` и обновляет ядро (`core.setWatchedVehid(None)`, `core.setControlledVehid(None)`).

### Процесс Подключения к ТС

#### Установка "Наблюдаемого" ТС (`watched_vehid`)
1.  `VehicleSelector` вызывает `_update_watched_vehicle(new_vehid)`.
2.  Если `watched_vehid` уже был установлен, для старой карточки вызывается `setWatched(False)`.
3.  Обновляется свойство `self.watched_vehid`.
4.  Вызывается `self.core.setWatchedVehid(new_vehid)` для обновления состояния в ядре.
5.  Для новой карточки (если `new_vehid` не `None`) вызывается `setWatched(True)`.

#### Установка "Управляемого" ТС (`controlled_vehid`)
1.  При успешном переходе в режим удаленного управления (`CardStatus.controlled`), `VehicleSelector` вызывает `self.core.setControlledVehid(vehid)`.
2.  Ядро приложения выполняет необходимую настройку для обеспечения удаленного управления (например, активация соответствующих каналов ввода/вывода).

### Переходы Между Режимами

#### Переходные Состояния
Система использует промежуточные состояния для управления асинхронными переходами режимов:
*   `want_auto`: Ожидание перехода из удаленного управления в автономный режим.
*   `want_remote`: Ожидание перехода из автономного режима в удаленное управление.

#### Таймер Перехода
Для каждого перехода устанавливается таймер (по умолчанию 10 секунд):
*   Таймер запускается при входе в состояние `want_auto` или `want_remote`.
*   Таймер отменяется при успешном завершении перехода (получении подтверждающего `main_state` от ТС).
*   Если ТС не подтверждает переход в течение таймаута, генерируется сигнал `transitionTimedOut`.

#### Визуальная Обратная Связь при Переходах
Во время переходных состояний пользовательский интерфейс предоставляет обратную связь:
*   Мигание соответствующей кнопки ("Авто" или "Контроль") если карточка развернута.
*   Мигание всей карточки, если она свернута.
*   Изменение цвета фона заголовка на полупрозрачный цвет целевого режима.

### Обработка Ошибок

#### Таймауты Переходов
Система использует настраиваемый параметр `transition_timeout_sec` для определения максимального времени ожидания перехода между режимами. Если переход в другой режим не удается в течение установленного времени:

1.  `VehicleCard` генерирует сигнал `transitionTimedOut`.
2.  `VehicleSelector` отображает диалоговое окно с сообщением об ошибке.
3.  `VehicleCard` автоматически возвращается в предыдущий стабильный режим (например, из `want_auto` обратно в `controlled`).
4.  `VehicleSelector` уведомляет ядро приложения, если это необходимо для глобальной обработки ошибок.

Значение `transition_timeout_sec` устанавливается в конфигурационном файле и влияет на все карточки ТС, управляемые данным экземпляром `VehicleSelector`.

#### Проблемы с Подключением
Статус онлайн/офлайн ТС отслеживается с использованием параметра `data_timeout_sec`:

*   Система считает данные "свежими", если они обновлялись не позднее, чем указано в параметре `data_timeout_sec` (по умолчанию 1 секунда).
*   Если данные не обновлялись дольше этого времени, система считает ТС офлайн.
*   Карточки ТС отображают статус "Офлайн" при потере связи.
*   Элементы управления на карточке деактивируются для ТС в офлайне.
*   Значение `data_timeout_sec` настраивается в конфигурации и применяется ко всем мостам данных `_CardDataBridge`.

#### Обработка Аварийных Ситуаций
Аварийное состояние ТС имеет специальную обработку:
1.  Карточка ТС отображает аварийное состояние соответствующей индикацией и миганием.
2.  Кнопка "Авария" становится видимой и активной.
3.  Нажатие кнопки "Авария" открывает диалоговое окно для подтверждения сброса.
4.  После подтверждения оператором, `VehicleSelector` отправляет команду сброса аварийного состояния на ТС. 