# Extracted class: Cameras

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import threading
import time
import os
import logging
import cv2
import numpy as np
from datetime import datetime

# PyQt6 imports
from PyQt6 import QtCore
from PyQt6.QtCore import QTimer, QPoint, QRect, Qt
from PyQt6.QtGui import QImage, QPixmap, QPainter, QPen, QTransform
from PyQt6.QtWidgets import QWidget, QLabel

# Application-specific imports
from gui_common import RESOURCES_CONFIG, get_from_telemetry, im_save
import multiprocessing as mp
import SharedArray as sa

# Define platform for easy reference
IS_MACOS = sys.platform == 'darwin'

class MacOSCameraReader(threading.Thread):
    """
    Thread-based camera reader for macOS compatibility.
    This avoids the multiprocessing pickling issues on macOS.
    """
    def __init__(self, cam_id, stream_address, shared_frame):
        super().__init__(daemon=True)
        self.cam_id = cam_id
        self.stream_address = stream_address
        self.shared_frame = shared_frame
        self.running = True

    def run(self):
        # Configure FFMPEG options based on stream type
        is_sim = 'sim' in self.stream_address
        if is_sim:
            ffmpeg_str = "flags;low_delay|fflags;nobuffer|strict;experimental|probesize;32|stimeout;500000|avioflags;direct|rtsp_transport;tcp|"
        else:
            ffmpeg_str = "flags;low_delay|fflags;nobuffer|strict;experimental|probesize;32|stimeout;500000|avioflags;direct|rtsp_transport;tcp|video_codec;hevc_cuvid"

        os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = ffmpeg_str

        try:
            video_cap = cv2.VideoCapture(self.stream_address)
            video_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            while self.running:
                ret, image_camera = video_cap.read()
                if not ret:
                    time.sleep(0.1)
                    continue

                # Update the shared frame data and timestamp
                with self.shared_frame["lock"]:
                    self.shared_frame["data"][:] = image_camera
                    self.shared_frame["timestamp"] = time.time()

                # Sleep to avoid CPU hogging
                time.sleep(0.01)
        except Exception as e:
            logging.error(f"Error in macOS camera reader thread: {e}")
        finally:
            if video_cap:
                video_cap.release()

class Cameras(object):
    """
    Class for camera initialization and management.

    This class handles camera streams for both Linux (using multiprocessing)
    and macOS (using threading) platforms.
    """
    MAX_IMG_TIME_DELAY = 0.5  # Maximum allowed delay for frame display (seconds)

    def __init__(self, core, modes, *args, **kwargs):
        """
        Initialize the Cameras object.

        Args:
            core: Core application object with access to screens and telemetry
            modes: Dictionary of camera display modes with position and transformation settings
            *args: Additional positional arguments (for future compatibility)
            **kwargs: Additional keyword arguments (for future compatibility)
        """
        self.core = core
        self.modes = modes

        self.cam_id = None
        self.killed = False
        self.timer = QTimer()

        self.width = None
        self.height = None
        self.rotation = None
        self.path_to_img = os.path.join(RESOURCES_CONFIG["images_folder"], "NoImage.png")
        self.prev_mode = None
        self.crop = None
        self.vflip = None
        self.hflip = None
        self.prev_watched_vehid = None
        self.hide = False
        self.default_pixmap = None

        # For Linux: multiprocessing
        self.proc = None
        self.frame = None

        # For macOS: threading
        self.camera_thread = None
        self.shared_frame = {
            "data": None,
            "timestamp": 0,
            "lock": threading.Lock()
        }

    def prepare(self, cam_id: str, default_mode: str = "front") -> None:
        """
        Prepare the camera widget with initial settings.

        Args:
            cam_id: Camera identifier (e.g., 'left', 'right', 'front')
            default_mode: Initial camera display mode
        """
        print("PrepCam")
        self.cam_id = cam_id

        # Create label for displaying camera feed
        if self.core.dev_mode:
            self.window = QWidget()
            self.label = QLabel(self.window)
            self.window.setWindowTitle(str(cam_id))
            self.window.show()
        else:
            self.label = QLabel(self.core.main_screen)

        # Load default image and set initial mode
        self.default_img = QImage(self.path_to_img)
        print("call set mode")
        self.set_mode("front")
        self.set_mode(default_mode)

        # Platform-specific initialization
        if IS_MACOS:
            # Initialize buffer for macOS threading approach
            self.shared_frame["data"] = np.zeros((960, 1280, 3), dtype=np.uint8)
        else:
            # Initialize shared memory for Linux multiprocessing approach
            try:
                sa.delete("shm://CAM" + str(cam_id))
            except Exception as e:
                pass
            sa.create("shm://CAM" + str(cam_id), (960, 1280, 3))
            self.frame = sa.attach("shm://CAM" + str(cam_id))

    def start(self) -> None:
        """
        Start the camera display and update timer.

        This method shows the camera label and starts the timer that
        periodically updates the camera feed (approximately 15 FPS).
        """
        self.label.show()
        self.timer.timeout.connect(self.show)
        self.timer.start(66)  # ~15 FPS

    def set_default_img(self) -> None:
        """
        Set the default image on the camera display.

        This method is called when no camera feed is available or when
        there are connection issues.
        """
        if not self.hide and hasattr(self, 'default_pixmap') and self.default_pixmap is not None:
            self.label.setPixmap(self.default_pixmap)

    def reader(self, stream_address: str) -> None:
        """
        Linux multiprocessing reader function that runs in a separate process.

        This method is executed in a separate process to read frames from the camera
        stream and store them in shared memory for the main process to access.

        Args:
            stream_address: RTSP URL of the camera stream
        """
        # Preconfig GPU for visible
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        vehid = str(vehid)
        if 'sim' in vehid:
            ffmpeg_str = "flags;low_delay|" \
                         "fflags;nobuffer|" \
                         "strict;experimental|" \
                         "probesize;32|" \
                         "stimeout;500000|" \
                         "avioflags;direct|" \
                         "rtsp_transport;tcp|"
            # "video_codec;hevc_cuvid"
        else:
            ffmpeg_str = "flags;low_delay|" \
                         "fflags;nobuffer|" \
                         "strict;experimental|" \
                         "probesize;32|" \
                         "stimeout;500000|" \
                         "avioflags;direct|" \
                         "rtsp_transport;tcp|" \
                         "video_codec;hevc_cuvid"
        os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = ffmpeg_str  # Make ffmpeg capture params as configured

        video_cap = cv2.VideoCapture(stream_address)  # init capture on main
        video_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Config buffresize of ffmpeg

        # Attach our proc to SA
        frame_out = sa.attach("shm://CAM" + str(self.cam_id))
        while True:
            ret, image_camera = video_cap.read()  # Try to grab image from camera
            if not ret:
                time.sleep(0.1)
            else:
                frame_out[:, :, :] = image_camera  # Put grabbed frame to SA
                frame_out[959][1279][0] = time.time()
                vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid

                try:
                    save = im_save[int(self.cam_id)].get_nowait()
                    logging.info("Save image from camera %s" % self.cam_id)
                except:
                    save = False
                    pass

                if save:
                    filename = "../images/" + datetime.now().strftime("%Y%m%d_%H%M%S_")
                    filename += self.cam_id + ".jpg"
                    # print (self.cam_id,type(self.cam_id))
                    cv2.imwrite(filename, image_camera)
                    logging.info("Image saved from camera %s to %s" % (self.cam_id, filename))

    def load_new_stream(self, vehid: str) -> None:
        """
        Update the video reading process with the camera address of the connected vehicle.

        If no vehicle is connected, a default waiting image is displayed.

        Args:
            vehid: Vehicle identifier to load camera stream from
        """
        if vehid is None:
            logging.info('No connected vehicle to stream')
            self.set_default_img()
            return

        cam_configs = self.core.cameras.get(vehid)
        if self.cam_id not in cam_configs:
            logging.warning('No camera params from connected vehicle')
            self.set_default_img()
            return

        # Build RTSP URL - common for both approaches
        stream_address = "rtsp://{}:{}@{}".format(
            cam_configs[self.cam_id]['login'],
            cam_configs[self.cam_id]['password'],
            cam_configs[self.cam_id]['IP']
        )

        # Platform-specific implementation
        if IS_MACOS:
            self._load_stream_macos(stream_address)
        else:
            self._load_stream_linux(stream_address)

    def _load_stream_macos(self, stream_address: str) -> None:
        """
        macOS-specific thread-based implementation for loading camera streams.

        This method creates and starts a thread to read frames from the camera stream
        and store them in a shared dictionary for the main thread to access.

        Args:
            stream_address: RTSP URL of the camera stream
        """
        # Stop existing thread if running
        if self.camera_thread and self.camera_thread.is_alive():
            self.camera_thread.running = False
            # Don't join - just let it die on its own
            self.camera_thread = None

        # Create and start new thread
        try:
            self.camera_thread = MacOSCameraReader(
                self.cam_id,
                stream_address,
                self.shared_frame
            )
            self.camera_thread.start()
        except Exception as e:
            logging.error(f"Failed to start macOS camera thread: {e}")
            self.set_default_img()

    def _load_stream_linux(self, stream_address: str) -> None:
        """
        Linux-specific multiprocessing implementation for loading camera streams.

        This method creates and starts a process to read frames from the camera stream
        and store them in shared memory for the main process to access.

        Args:
            stream_address: RTSP URL of the camera stream
        """
        # Safely terminate existing process if it exists
        proc_to_terminate = self.proc
        self.proc = None  # Set to None first to prevent race conditions

        if proc_to_terminate is not None:
            try:
                proc_to_terminate.terminate()
                proc_to_terminate.join(timeout=1.0)  # Add timeout to prevent hanging
            except Exception as e:
                logging.error(f"Error terminating process: {e}")

        # Create new process
        self.proc = mp.Process(target=self.reader, args=(stream_address,))  # Init process 1
        self.proc.daemon = True  # Make sure process exits when main program exits
        self.proc.start()

    def show(self) -> None:
        """
        Display the received frames.

        This method is called periodically by the timer to update the camera display.
        If the connected vehicle ID changes, it updates the video reading process
        according to the camera connection parameters of the new vehicle.
        """
        if self.prev_mode != self.core.cameras_mode:
            self.set_mode(self.core.cameras_mode)

        watched_vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid

        # Handle disconnection (common code for both platforms)
        if watched_vehid is None or self.hide:
            if IS_MACOS:
                # macOS: Stop thread
                if self.camera_thread and self.camera_thread.is_alive():
                    self.camera_thread.running = False
                    self.camera_thread = None
            else:
                # Linux: Stop process
                proc_to_terminate = self.proc
                self.proc = None
                if proc_to_terminate is not None:
                    try:
                        proc_to_terminate.terminate()
                        proc_to_terminate.join(timeout=1.0)
                    except Exception as e:
                        logging.error(f"Error terminating process in show: {e}")

            self.set_default_img()

        # Handle connection or vehicle change (common for both platforms)
        elif watched_vehid != self.prev_watched_vehid or (IS_MACOS and (not self.camera_thread or not self.camera_thread.is_alive())) or (not IS_MACOS and self.proc is None):
            with self.core.camera_lock:
                self.load_new_stream(watched_vehid)
                self.prev_watched_vehid = watched_vehid

        # Display the current frame or default image using platform-specific approach
        if IS_MACOS:
            self._show_macos()
        else:
            self._show_linux()

    def _show_macos(self) -> None:
        """
        macOS-specific frame display logic.

        This method retrieves the current frame from the shared dictionary
        and displays it on the label if it's valid and recent enough.
        """
        frame_is_valid = False
        current_frame = None

        # Safely get the current frame
        with self.shared_frame["lock"]:
            current_timestamp = self.shared_frame["timestamp"]
            if time.time() - current_timestamp <= self.MAX_IMG_TIME_DELAY:
                current_frame = self.shared_frame["data"].copy()
                frame_is_valid = True

        if frame_is_valid and not self.hide:
            h, w, _ = current_frame.shape
            img = QImage(np.uint8(current_frame), w, h, QImage.Format.Format_RGB888).rgbSwapped()
            pm = self.create_pixmap(img)
            self.label.setPixmap(pm)
        else:
            self.set_default_img()

    def _show_linux(self) -> None:
        """
        Linux-specific frame display logic.

        This method retrieves the current frame from shared memory
        and displays it on the label if it's valid and recent enough.
        """
        if self.proc is None or time.time() - self.frame[959][1279][0] > self.MAX_IMG_TIME_DELAY:
            self.set_default_img()
        else:
            if not self.hide:
                h, w, _ = self.frame.shape
                img = QImage(np.uint8(self.frame), w, h, QImage.Format.Format_RGB888).rgbSwapped()
                pm = self.create_pixmap(img)
                self.label.setPixmap(pm)

    def create_pixmap(self, img: QImage, deny_transform: bool = False) -> QPixmap:
        """
        Create a pixmap from the image with optional transformations and object detection overlays.

        Args:
            img: Source QImage to convert to pixmap
            deny_transform: If True, skip rotation and flip transformations

        Returns:
            QPixmap: The processed pixmap ready for display
        """
        pixmap = QPixmap.fromImage(img)
        detects_info = get_from_telemetry(self.core, "camera_detections_" + self.cam_id, default_value=None)
        if detects_info:
            for obj in detects_info.get("objects"):
                label = str(obj.get("object_class") + " " + "%.2f" % obj.get("score"))
                obj_x = int(obj.get("box")["x"])
                obj_y = int(obj.get("box")["y"])
                obj_w = int(obj.get("box")["width"])
                obj_h = int(obj.get("box")["height"])
                painter = QPainter(pixmap)
                painter.setPen(QPen(Qt.GlobalColor.red, 3, Qt.PenStyle.SolidLine))
                painter.drawRect(obj_x, obj_y, obj_w, obj_h)
                if deny_transform:
                    painter.drawText(obj_x, obj_y, label)
                # Properly end the painter
                painter.end()

        if not deny_transform:
            if self.rotation:
                transform = QTransform().rotate(self.rotation)
                pixmap = pixmap.transformed(transform, QtCore.Qt.TransformationMode.SmoothTransformation)
            hscale = 1 if not self.hflip else -1
            vscale = 1 if not self.vflip else -1

            if hscale == -1 or vscale == -1:
                pixmap = pixmap.transformed(QTransform().scale(hscale, vscale))

            # Draw detected objects rectangles
            if detects_info:
                for obj in detects_info.get("objects"):
                    label = str(obj.get("object_class") + " " + "%.2f" % obj.get("score"))
                    obj_x = int(obj.get("box")["x"])
                    obj_y = int(obj.get("box")["y"])
                    obj_w = int(obj.get("box")["width"])
                    obj_h = int(obj.get("box")["height"])

                    pp = [QPoint(obj_x, obj_y), QPoint(obj_x + obj_w, obj_y + obj_h)]
                    for i in range(len(pp)):
                        pp[i] = QTransform().rotate(self.rotation).map(pp[i])
                        pp[i] = QTransform().scale(hscale, vscale).map(pp[i])
                        if pp[i].x() < 0:
                            pp[i] += QPoint(pixmap.width(), 0)
                        if pp[i].y() < 0:
                            pp[i] += QPoint(0, pixmap.height())
                        if pp[i].x() > pixmap.width():
                            pp[i] -= QPoint(pixmap.width(), 0)
                        if pp[i].y() > pixmap.height():
                            pp[i] -= QPoint(0, pixmap.height())

                    p = QPoint(min(pp[0].x(), pp[1].x()), max(pp[0].y(), pp[1].y()) + 15)
                    painter = QPainter(pixmap)
                    painter.setPen(QPen(Qt.GlobalColor.red, 3, Qt.PenStyle.SolidLine))
                    painter.drawText(p, label)
                    # Properly end the painter
                    painter.end()

            if self.crop[0] or self.crop[1] or self.crop[2] or self.crop[3]:
                crop_left_x = int(img.size().width() * self.crop[3])
                crop_top_y = int(img.size().height() * self.crop[0])

                cut_from_right = int(img.size().width() * self.crop[1])
                cut_from_bottom = int(img.size().height() * self.crop[2])

                crop_width = int(img.size().width() - crop_left_x - cut_from_right)
                crop_height = int(img.size().height() - crop_top_y - cut_from_bottom)

                pixmap = pixmap.copy(QRect(crop_left_x, crop_top_y, crop_width, crop_height))

        scaled_pixmap = pixmap.scaled(self.label.size(), Qt.AspectRatioMode.IgnoreAspectRatio)
        return scaled_pixmap

    def set_mode(self, mode_id: str) -> None:
        """
        Set video display parameters based on the specified mode.

        This method configures the camera display position, size, rotation,
        and other transformation parameters based on the specified mode.

        Args:
            mode_id: Identifier of the display mode to set
        """
        print("setting mode ", mode_id)
        self.prev_mode = mode_id
        mode_dict = self.modes.get(mode_id)
        if not mode_dict:
            self.hide = True
            self.label.hide()
            return

        self.hide = False
        self.label.show()
        x = mode_dict.get("x", 0)
        y = mode_dict.get("y", 0)
        self.width = mode_dict.get("width", 500)
        self.height = mode_dict.get("height", 500)
        self.rotation = mode_dict.get("rotation", 30)
        self.hflip = mode_dict.get("hflip", False)
        self.vflip = mode_dict.get("vflip", False)
        screen_num = mode_dict.get("screen", 0)
        self.crop = mode_dict.get("crop", [0, 0, 0, 0])

        self.label.setFixedSize(int(self.width), int(self.height))

        if self.core.dev_mode:
            self.window.setFixedSize(int(self.width), int(self.height))
        else:
            self.label.move(int(self.core.screen_width * (screen_num - 1) + x), int(y))

        self.default_pixmap = self.create_pixmap(self.default_img, deny_transform=True)
        print("mode set", mode_id)

    def kill(self) -> None:
        """
        Stop and clean up camera resources.

        This method stops the timer, terminates any running threads or processes,
        and releases resources used by the camera.
        """
        self.killed = True
        self.timer.stop()
        if IS_MACOS:
            if hasattr(self, 'camera_thread') and self.camera_thread and self.camera_thread.is_alive():
                self.camera_thread.running = False
        else:
            if hasattr(self, 'proc') and self.proc is not None:
                self.proc.terminate()
                self.proc.join()
                self.proc = None
