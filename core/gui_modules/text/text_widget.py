# Extracted class: TextWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from typing import <PERSON><PERSON>

from PyQt6 import QtCore
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QLabel, QApplication

class TextWidget(QLabel):
    """
    A simple text widget that displays static text on the screen.

    This widget can show text with customizable font size, color, and alignment.
    It can also display the current version of the application.
    """

    def __init__(self, core, *args, **kwargs):
        """
        Initialize the TextWidget.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__()
        self.core = core

    def prepare(self, text: str = "", font_size: str = "12px", x: int = 0, y: int = 0,
                font_color: str = "#AAAAAA", left: bool = False, screen: int = 1) -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            text: Text to display
            font_size: Font size in CSS format (e.g., "12px")
            x: X position of the widget
            y: Y position of the widget
            font_color: Text color in CSS format (e.g., "#AAAAAA")
            left: Whether to align text to the left (False for center)
            screen: Screen number (1+ for main screen, 0 for sensor screen)
        """
        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setStyleSheet("font-size: %s; color: %s " % (font_size, font_color))
        self.setWordWrap(True)

        if text == 'RMO ver.':
            res = os.popen("git describe --always").readline()
            text = text + ' ' + res

        if not left:
            self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        else:
            self.setAlignment(Qt.AlignmentFlag.AlignLeft)

        if screen >= 1:
            self.setParent(self.core.main_screen)
            self.move((screen - 1) * self.core.screen_width + x, y)
        else:
            self.setParent(self.core.sensor_screen)
            self.move(x, y)
        # self.setText(text)
        # trying to translate from another file...
        self.setText(QApplication.translate("TextWidget", text))

    def start(self) -> None:
        """
        Show the widget.
        """
        self.show()
