# Extracted class: MessagesWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import csv
import os
import threading
import time
from collections import deque
from typing import Dict, Any, List, Optional, Deque, Tuple, Union

from PyQt6 import QtCore
from PyQt6.QtCore import (Qt, QTimer, QSize, QPoint, QRect, QPropertyAnimation,
                          QEasingCurve, QDateTime, QMargins, pyqtProperty, pyqtSignal)
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSizePolicy, QMainWindow,
                             QScrollArea, QFrame, QTextEdit, QPushButton, QGridLayout, QSpacerItem,
                             QDialog, QDialogButtonBox, QFormLayout, QMessageBox, QApplication)
from PyQt6.QtGui import (QPalette, QColor, QFont, QPixmap, QP<PERSON>ter, QBrush, QPen, QPolygon,
                         QIcon)

from customClasses.styledCheckBox import StyledCheckBox
from gui_common import CONFIG, RESOURCES_CONFIG, RosLogLevels

class MessagesWidget(object):
    """
    Displays all current log messages from vehicles.

    This widget shows log messages from all connected vehicles,
    with filtering and formatting based on message type.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """
        Initialize the MessagesWidget.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        self.core = core
        self.killed = False
        self.last_msg: Dict[str, Any] = {}
        self.filter_str: str = ""

        # Will be initialized in prepare()
        self.maximized_frame: Optional[MessagesWidgetMaximized] = None

    def prepare(self, width: int = 500, height: int = 500, x: int = 500, y: int = 800,
                icons_radius: int = 30, msg_colors: Optional[Dict[str, str]] = None,
                screen_num: int = 0, filter_str: str = "") -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            icons_radius: Radius of the icon buttons
            msg_colors: Dictionary mapping message types to colors
            screen_num: Screen number (0 for sensor screen, 1+ for main screen)
            filter_str: String to filter messages by
        """
        if not msg_colors:
            msg_colors = {
                "16": "#f28907",  # control restriction
                "8": "#e01705",  # error
                "4": "#f0d400",  # warning
                "2": "#50baeb",  # info
            }
        # self.minimized_frame = MessagesWidgetMinimized(self.core, self)
        self.maximized_frame = MessagesWidgetMaximized(self.core, self)
        self.maximized_frame.prepare(width=width,
                                     height=height,
                                     x=x, y=y,
                                     icons_radius=icons_radius,
                                     msgs_colors=msg_colors,
                                     # minimized_window=self.minimized_frame,
                                     screen_num=screen_num)

        self.filter_str = filter_str

        # self.minimized_frame.prepare(width=width,
        #                              height=height,
        #                              x=x, y=y,
        #                              icons_radius=icons_radius,
        #                              msgs_colors=msg_colors,
        #                              maximized_window=self.maximized_frame,
        #                              screen_num=screen_num)

    def start(self) -> None:
        """
        Show the maximized frame and start the update thread.
        """
        if self.maximized_frame:
            # self.minimized_frame.hide()
            self.maximized_frame.show()
            update_thread = threading.Thread(target=self.update_, daemon=True)
            update_thread.start()

    def update_(self) -> None:
        """
        Update thread that refreshes the message display.

        This method runs in a separate thread and periodically updates
        the message display with new messages.
        """
        while not self.killed:
            self.maximized_frame.update_signal.emit()
            # self.minimized_frame.update_signal.emit()
            time.sleep(0.1)

            vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
            if vehid is not None and vehid in self.core.telemetry.keys():
                self.maximized_frame.testBtn.setEnabled(True)
            else:
                self.maximized_frame.testBtn.setEnabled(False)

    def get_sorted_msgs(self) -> List[Dict[str, Any]]:
        """
        Get a sorted list of messages from all vehicles.

        Returns:
            A list of message dictionaries sorted by time
        """
        msgs = []
        for vehid, data in self.core.log_msgs.items():
            for msg in data:
                if (
                        msg['level'] == RosLogLevels.DEBUG or
                        (msg['level'] == RosLogLevels.INFO and not self.core.veh_active_states[vehid]) or
                        any(k in msg['msg'] for k in self.filter_str)
                ):
                    continue
                else:
                    msg['vehid'] = vehid
                    msgs.append(msg)

            self.core.log_msgs[vehid] = []  # сбрасываем переданные сообщения

        msgs.sort(key=lambda d: d["time"])
        if msgs:
            self.last_msg = msgs[-1]
        return msgs


class MessagesWidgetMaximized(QWidget):
    """
    Развернутое окно сообщений
    """

    update_signal = QtCore.pyqtSignal()

    def __init__(self, core, controller, *args, **kwargs):
        super().__init__(core.main_screen)
        self.core = core
        self.controller = controller
        self.killed = False
        self.update_signal.connect(self.handle_msgs)
        # create message queue
        self.messages = deque(maxlen=100)
        # create text block for message storing and displaying
        # self.msg_box = QTextBrowser(self)
        self.msg_box = QTextEdit()
        # self.msg_box.setReadOnly(True)
        self.msg_box.setAcceptRichText(True)
        self.msg_box.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                   QSizePolicy.Policy.MinimumExpanding)
        self.msg_box.setStyleSheet("background-color: rgba(100, 100, 100, 100);")
        # self.msg_box.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.msg_box.setAcceptRichText(True)
        self.msg_box.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        # self.msg_box.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self._debugMode = False

    def prepare(self, width=500, height=500, x=500, y=800,
                icons_radius=30, machine_id_font_size=20,
                msgs_font_size=15, msgs_colors=None,
                minimized_window=None, screen_num=0):
        self.machine_id_font_size = machine_id_font_size
        self.msgs_font_size = msgs_font_size
        # self.minimized_window = minimized_window
        self.msgs_colors = msgs_colors
        self.lastUpdate = 0

        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.move(self.core.screen_width * (screen_num - 1) + x, y)
        self.setFixedSize(width, height)

        # create main layout:
        mainLayout = QVBoxLayout()
        mainLayout.setContentsMargins(QMargins(1, 1, 1, 1))
        self.setLayout(mainLayout)

        # toolbar-like widget (wrapper)
        top_icons_widget = QWidget(self)
        top_icons_widget.setAutoFillBackground(True)
        top_icons_widget.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                       QSizePolicy.Policy.Minimum)
        top_icons_widget.setFixedHeight(45)

        # create caption for whole widget
        captionLabel = QLabel(text=self.tr("Message Log"))
        captionLabel.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                   QSizePolicy.Policy.MinimumExpanding)
        # create button for clearing the log
        clear_icon_path = os.path.join(RESOURCES_CONFIG["images_folder"],
                                       "cross.png")
        clear_btn = QPushButton()
        clear_btn.clicked.connect(self.handle_clear_btn)
        clear_btn.setIcon(QIcon(clear_icon_path))

        # test button - just for debug
        self.testBtn = QPushButton(text="Report a problem")
        self.testBtn.setSizePolicy(QSizePolicy.Policy.Minimum,
                                   QSizePolicy.Policy.MinimumExpanding)
        self.testBtn.clicked.connect(self.testButton)
        self.testBtn.hide()

        # debug checkbox
        debugModeCheckbox = StyledCheckBox(text=self.tr('Debug Mode'))
        debugModeCheckbox.setSizePolicy(QSizePolicy.Policy.Minimum,
                                        QSizePolicy.Policy.MinimumExpanding)
        debugModeCheckbox.setFontSize(15)

        debugModeCheckbox.toggled.connect(self.setDebugMode)

        minimize_icon_path = os.path.join(RESOURCES_CONFIG["images_folder"],
                                          "arrow_up.png")
        # minimize_btn = QPushButton('', top_icons_widget)
        # minimize_btn.clicked.connect(self.handle_minimize_btn)
        # minimize_btn.setIcon(QIcon(minimize_icon_path))

        btns = [clear_btn,
                # minimize_btn
                ]

        for btn in btns:
            btn.setIconSize(QtCore.QSize(icons_radius, icons_radius))
            btn.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
            btn.setStyleSheet("background: rgba(255, 255, 255, 0)")

        top_icons_widget_layout = QHBoxLayout()
        top_icons_widget_layout.setContentsMargins(QMargins(20, 2, 2, 2))
        top_icons_widget.setLayout(top_icons_widget_layout)
        # top_icons_widget_layout.addWidget(minimize_btn)
        top_icons_widget_layout.addWidget(captionLabel)
        top_icons_widget_layout.addStretch()
        top_icons_widget_layout.addWidget(self.testBtn)
        top_icons_widget_layout.addWidget(debugModeCheckbox)
        top_icons_widget_layout.addWidget(clear_btn)

        mainLayout.addWidget(top_icons_widget)
        mainLayout.addWidget(self.msg_box)

    def testButton(self):
        # newMessage = {
        # 'vehid': "Drillaz 0001",
        # 'msg': "Ukulele ukulele trali vali tili ti",
        # 'time': time.time(),
        # 'msgCode': "100010101",
        # 'type': 2
        # }
        # self.messages.append(newMessage)
        # self.addNewMessage(newMessage)

        event_name = "rmo_event_" + QDateTime.currentDateTime().toString("yyyy-MM-dd_HH-mm-ss_t")

        self.msgbox = QMessageBox()
        self.msgbox.setIcon(QMessageBox.Information)
        self.msgbox.setText(self.tr("Save recent onboard bags for further analysis?"))
        self.msgbox.setWindowTitle(self.tr("Save event?"))
        self.msgbox.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)

        ret = self.msgbox.exec()
        if ret == QMessageBox.Ok:
            cmd = {
                'bags_save': {'name': event_name, 'description': "", 'minutes_to_save': -1, 'with_lidars': False}
            }
            self.send_cmd(cmd)

            class InputDialog(QDialog):
                def __init__(self, parent=None):
                    super().__init__(parent)

                    self.setWindowTitle(event_name + self.tr(" description"))
                    self.setMaximumWidth(640)
                    self.setMaximumHeight(240)

                    self.description = QTextEdit(self)
                    self.description.setText("Event: ")
                    buttonBox = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel, self)

                    layout = QFormLayout(self)
                    layout.addRow(self.tr("Event description"), self.description)
                    layout.addWidget(buttonBox)

                    buttonBox.accepted.connect(self.accept)
                    buttonBox.rejected.connect(self.reject)

                def getInputs(self):
                    return self.description.toPlainText()

            dialog = InputDialog()
            if dialog.exec():
                print(dialog.getInputs())

                cmd = {
                    'bags_save': {'name': event_name, 'description': dialog.getInputs(), 'with_lidars': False}
                }
                self.send_cmd(cmd)

    def send_cmd(self, cmd):
        vehid = None
        if self.core.in_rc_vehid is not None:
            vehid = self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            vehid = self.core.watched_vehid
        if vehid is not None:
            self.core.output_data[vehid].update(cmd)
        else:
            print("No connected vehicle while trying to send cmd")

    def addNewMessage(self, message):
        newText = ""
        msg_type_colour = None
        msg_str = None
        message_type = message.get('type', 0)
        message_time = message.get('time')
        message_vehid = message.get('vehid', 'Undefined Vehicle')
        message_code = message.get('code', 'Undefined Code')
        message_text = message.get('msg', ' ')

        if message_type == 8:
            msg_type_colour = QApplication.translate("MessageTypeColour", 'Red')
        elif message_type == 4:
            msg_type_colour = QApplication.translate("MessageTypeColour", 'Yellow')
        elif message_type == 16:
            msg_type_colour = QApplication.translate("MessageTypeColour", 'Orange')

        # create message line
        msg_color = self.msgs_colors.get(str(message_type), "#000000")
        # add line formatting
        line = '<p style = "color: {};">'.format(msg_color)
        time_str = '--:--:--'
        if message_time is not None:
            time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(message_time))

        if msg_type_colour:
            msg_str = [time_str, str(message_vehid), message_text, msg_type_colour, 'debug']

        if self.isDebug():
            # remember user <BR> tag instead of "\n" then editing
            # styled text in pyQt
            newText = line + ' '.join([
                self.tr('From', 'for messaging: _from_ vehicle '),
                message_vehid + ':',
                message_text,
                self.tr('At', 'for messaging: received _at_ time'),
                time_str
            ])
            if message_code != '':
                newText += "<BR> Code:" + message_code

            lastPos = self.msg_box.verticalScrollBar().value()
            maxPos = self.msg_box.verticalScrollBar().maximum()
            self.msg_box.append(newText)
            if lastPos != maxPos:
                self.msg_box.verticalScrollBar().setValue(lastPos)

        if not self.isDebug() and message.get('code', '') != '':
            # create message line
            msg_color = self.msgs_colors.get(str(message_type), "#000000")
            # add line formatting
            line = '<p style = "color: {};">'.format(msg_color)
            time_str = '--:--:--'
            if message_time is not None:
                time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(message_time))
            # remember user <BR> tag instead of "\n" then editing
            # styled text in pyQt
            newText = line + ' '.join([
                self.tr('From', 'for messaging: _from_ vehicle '),
                message_vehid + ':',
                self.tr(message_code),
                self.tr('At', 'for messaging: received _at_ time'),
                time_str
            ])

            lastPos = self.msg_box.verticalScrollBar().value()
            maxPos = self.msg_box.verticalScrollBar().maximum()
            self.msg_box.append(newText)
            if lastPos != maxPos:
                self.msg_box.verticalScrollBar().setValue(lastPos)

            if msg_type_colour:
                msg_str = [time_str, str(message_vehid), self.tr(message_code) + "\n" + message_text, msg_type_colour,
                           'main']

        if msg_str:
            log_file = open(self.core.log_file_name, mode='a')
            writer = csv.writer(log_file)
            if self.core.first_row:
                writer.writerow(['start_alert', 'veh_id', 'msg_alert', 'alert_category', 'alert_filter'])
                self.core.first_row = None
            writer.writerow(msg_str)
            log_file.close()

    def update_text(self):
        self.msg_box.clear()
        for message in self.messages:
            self.addNewMessage(message)

    def handle_clear_btn(self):
        # delete all messages from list
        self.messages.clear()
        # clear the text field
        self.msg_box.clear()

    def handle_minimize_btn(self):
        self.minimized_window.show()
        self.hide()
        self.minimized_window.move(self.pos().x(), self.pos().y())

    def handle_msgs(self):
        msgs = self.controller.get_sorted_msgs()
        for msg_data in msgs:
            vehid = msg_data.get("vehid", "")
            msg_type = msg_data.get("level", 1)
            if msg_type != 2 or vehid == self.core.watched_vehid or vehid == self.core.in_rc_vehid:
                self.add_msg(msg_data)

    def add_msg(self, data):
        vehid = data.get("vehid", "")
        msg = data.get("msg", "")
        msg_time = data.get("time", "")
        msg_type = data.get("level", 1)
        msg_code = data.get("code", None)

        if not msg:
            return
        '''
        if (str(vehid) + msg) in self.msgs_hashes:
            return
        '''

        newMessage = {
            'vehid': vehid,
            'msg': msg,
            'time': msg_time,
            'code': msg_code,
            'type': msg_type
        }

        self.messages.append(newMessage)
        self.addNewMessage(newMessage)

    def setDebugMode(self, newDebugMode):
        """ debug mode property setter """
        # Convert Qt.CheckState to boolean if needed
        if isinstance(newDebugMode, Qt.CheckState):
            newDebugMode = (newDebugMode == Qt.CheckState.Checked)
        self._debugMode = newDebugMode
        # need to redraw all messages from scratch
        self.update_text()
        if newDebugMode:
            self.testBtn.show()
        else:
            self.testBtn.hide()

    def isDebug(self):
        """ debug mode property getter """
        return self._debugMode

    # debug mode allow control service messages visibility
    debugMode = pyqtProperty(bool, fset=setDebugMode, fget=isDebug)


class MessagesWidgetMinimized(QMainWindow):
    update_signal = QtCore.pyqtSignal()

    def __init__(self, core, controller, *args, **kwargs):
        super().__init__(core.main_screen)
        self.core = core
        self.controller = controller
        self.killed = False
        self.update_signal.connect(self.update_msg)

    def prepare(self, width=500, height=500, x=500, y=800, icons_radius=30, machine_id_font_size=20, msgs_font_size=15,
                msgs_colors=None, maximized_window=None, screen_num=0):
        self.machine_id_font_size = machine_id_font_size
        self.msgs_font_size = msgs_font_size
        self.maximized_window = maximized_window
        self.msgs_colors = msgs_colors
        self.last_msg_w = None

        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        self.move(self.core.screen_width * (screen_num - 1) + x, y)
        self.setFixedWidth(width)

        max_f_w0 = QWidget(self)
        v_layout_max_frame = QHBoxLayout()
        max_f_w0.setLayout(v_layout_max_frame)
        max_f_w0.setSizePolicy(QSizePolicy.Policy.Maximum,
                               QSizePolicy.Policy.Maximum);

        self.setCentralWidget(max_f_w0)

        self.move(x, y)

        top_icons_widget = QWidget(self)

        maximize_icon_path = os.path.join(RESOURCES_CONFIG["images_folder"],
                                          "arrow_down.png")
        maximize_btn = QPushButton('', top_icons_widget)
        maximize_btn.clicked.connect(self.handle_maximize_btn)
        maximize_btn.setIcon(QIcon(maximize_icon_path))

        btns = [maximize_btn]

        for btn in btns:
            btn.setIconSize(QtCore.QSize(icons_radius, icons_radius))
            btn.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
            btn.setStyleSheet("background-color: rgba(255, 255, 255, 128)")

        top_icons_widget_layout = QHBoxLayout()
        top_icons_widget.setLayout(top_icons_widget_layout)
        top_icons_widget_layout.addStretch(1)
        top_icons_widget_layout.addWidget(maximize_btn)

        msgs_container = QWidget()
        msgs_container_layout = QVBoxLayout()
        msgs_container.setLayout(msgs_container_layout)
        msgs_container.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        msgs_container.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        v_layout_max_frame.addWidget(msgs_container, 9)
        v_layout_max_frame.addWidget(top_icons_widget, 1)
        v_layout_max_frame.setAlignment(top_icons_widget, Qt.AlignmentFlag.AlignTop)
        msgs_container_layout.setAlignment(top_icons_widget, Qt.AlignmentFlag.AlignTop)

        self.v_layout_max_frame = v_layout_max_frame
        self.msgs_container_layout = msgs_container_layout
        self.msgs_container = msgs_container
        self.max_f_w0 = max_f_w0

    def handle_maximize_btn(self):
        self.maximized_window.show()
        self.hide()
        self.maximized_window.move(self.pos().x(), self.pos().y())

    def update_msg(self):
        self.add_msg(self.controller.last_msg)

    def add_msg(self, data):
        machine_id = data.get("machine_id", "")
        msg = data.get("msg", "")
        msg_type = data.get("level", "info")

        if not msg:
            return

        msg_color = self.msgs_colors.get(str(msg_type), "#000000")

        if self.last_msg_w:
            self.last_msg_w.deleteLater()

        msg_widget = QWidget(self)
        msg_widget_layout = QHBoxLayout()
        msg_widget.setLayout(msg_widget_layout)

        machine_id_l = QLabel("От: " + str(machine_id), msg_widget)
        machine_id_l.setStyleSheet("font-size : %s; color : black;" % (self.msgs_font_size))
        msg_l = QLabel(msg, msg_widget)
        msg_l.setStyleSheet("font-size : %s; color : %s;" % (self.machine_id_font_size, msg_color))
        msg_l.setWordWrap(True)
        msg_widget_layout.addWidget(machine_id_l)
        msg_widget_layout.addWidget(msg_l)

        self.msgs_container_layout.insertWidget(0, msg_widget)
        self.last_msg_w = msg_widget
