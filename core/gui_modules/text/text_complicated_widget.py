# Extracted class: TextComplicatedWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import time
import threading
import logging
from typing import Optional, Any

from PyQt6 import QtCore
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel

from gui_common import get_from_telemetry

logger = logging.getLogger(__name__)

class ApiWorker(QtCore.QObject):
    """Background worker for making API calls without blocking the UI"""
    result_ready = pyqtSignal(str, str)  # url, result
    error_occurred = pyqtSignal(str, str)  # url, error message
    connection_status_changed = pyqtSignal(str, bool)  # service, status
    
    def __init__(self):
        super().__init__()
        self._running = True
        self._retry_count = {}  # Track retry attempts per URL
        self._retry_delay = 10  # Initial retry delay in seconds (increased from 5)
        self._max_retry_delay = 120  # Maximum retry delay (increased from 30)
        self._request_queue = []
        self._thread = threading.Thread(target=self._worker_thread, daemon=True)
        self._thread.start()
    
    def stop(self):
        """Stop the worker thread"""
        self._running = False
        if self._thread.is_alive():
            self._thread.join(timeout=1.0)
    
    def queue_request(self, url, service_name):
        """Add a request to the queue"""
        self._request_queue.append((url, service_name))
        
    def _worker_thread(self):
        """Background thread that processes API requests"""
        while self._running:
            if not self._request_queue:
                time.sleep(0.1)
                continue
                
            url, service_name = self._request_queue.pop(0)
            try:
                response = requests.get(url, timeout=2.0)
                if response.ok:
                    self.result_ready.emit(url, response.text)
                    self.connection_status_changed.emit(service_name, True)
                    # Reset retry count on success
                    if url in self._retry_count:
                        del self._retry_count[url]
                else:
                    self.error_occurred.emit(url, f"HTTP {response.status_code}")
                    # Add back to queue for retry
                    self._handle_retry(url, service_name)
            except requests.RequestException as e:
                logger.debug(f"Request to {url} failed: {str(e)}")
                self.error_occurred.emit(url, str(e))
                self.connection_status_changed.emit(service_name, False)
                # Add back to queue for retry
                self._handle_retry(url, service_name)
            
            # Small delay between requests
            time.sleep(0.1)
    
    def _handle_retry(self, url, service_name):
        """Handle retry logic with exponential backoff"""
        # Initialize or increment retry count
        self._retry_count[url] = self._retry_count.get(url, 0) + 1
        
        # Calculate delay with exponential backoff
        retry_delay = min(self._retry_delay * (2 ** (self._retry_count[url] - 1)), self._max_retry_delay)
        
        # Log retry attempt - only log every 5th attempt to reduce noise
        if self._retry_count[url] % 5 == 1 or self._retry_count[url] <= 2:
            logger.debug(f"Will retry {url} in {retry_delay} seconds (attempt {self._retry_count[url]})")
        
        # Schedule the retry after delay
        def delayed_retry():
            if self._running:  # Check if we're still running
                self._request_queue.append((url, service_name))
        
        timer = threading.Timer(retry_delay, delayed_retry)
        timer.daemon = True
        timer.start()


class TextComplicatedWidget(QWidget):
    """
    A widget that displays dynamic text with data from various sources.

    This widget can show text that updates automatically with data from
    telemetry, HAL version, or other sources.
    """
    # Create a single shared worker for all instances
    _api_worker = None

    def __init__(self, core: Any, text: str, *args, **kwargs) -> None:
        """
        Initialize the TextComplicatedWidget.

        Args:
            core: The core application object
            text: The base text to display
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__()
        self.core = core
        self.core_screen = core.main_screen
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)
        self.version: Optional[str] = None
        self.label_text: str = text
        self.hal_version = "connecting..."
        self.build_id = "connecting..."
        self.disp_version = "connecting..."
        
        # Initialize the shared API worker if not already created
        if TextComplicatedWidget._api_worker is None:
            TextComplicatedWidget._api_worker = ApiWorker()
            # Set initial status
            self.core.hal_connection_status = False

        # Connect to worker signals
        TextComplicatedWidget._api_worker.result_ready.connect(self._handle_api_result)
        TextComplicatedWidget._api_worker.error_occurred.connect(self._handle_api_error)
        TextComplicatedWidget._api_worker.connection_status_changed.connect(self._handle_connection_status)

        # Will be initialized in prepare()
        self.frame: Optional[QWidget] = None
        self.label: Optional[QLabel] = None
        self.field_to_read: str = ""

    def prepare(self, text: str = "", font_size: str = "12px", x: int = 0, y: int = 0,
                width: int = 0, height: int = 0, font_color: str = "#AAAAAA",
                left: bool = False, screen: int = 1, from_telemetry: bool = False,
                field_to_read: str = "") -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            text: Additional text to display (unused if label_text is set)
            font_size: Font size in CSS format (e.g., "12px")
            x: X position of the widget
            y: Y position of the widget
            width: Width of the widget
            height: Height of the widget
            font_color: Text color in CSS format (e.g., "#AAAAAA")
            left: Whether to align text to the left (unused, always left-aligned)
            screen: Screen number (1+ for main screen, 0 for sensor screen)
            from_telemetry: Whether to read data from telemetry
            field_to_read: Name of the field to read from telemetry
        """

        if screen == 0:
            self.frame = QWidget(self.core.sensor_screen)
        else:
            self.frame = QWidget(self.core.main_screen)

        self.frame.setGeometry(0, 0, width, height)
        stack = QVBoxLayout()
        self.frame.setLayout(stack)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        if screen == 0:
            self.frame.setGeometry(x, y, width, height)
        else:
            self.frame.setGeometry(self.core.screen_width * (screen - 1) + x, y, width, height)

        if screen >= 1:
            self.frame = QWidget(self.core.main_screen)
            self.frame.setGeometry(self.core.screen_width * (screen - 1) + x, y, width, height)
        else:
            self.frame.setGeometry(x, y, width, height)

        self.field_to_read = field_to_read
        self.label = QLabel()
        self.label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.label.setStyleSheet("font-size: %s; color: %s " % (font_size, font_color))
        self.label.setText(self.label_text)
        stack.addWidget(self.label)

    def start(self) -> None:
        """
        Start the widget update timer and show the widget.
        """
        if self.label and self.frame:
            # Queue initial API requests if this is a version widget
            if self.label_text == "HAL ver.":
                self._queue_hal_version_request()
            elif self.label_text == "HAL build ID":
                self._queue_build_id_request()
            elif self.label_text == "Disp. ver.":
                self._queue_disp_version_request()
            
            self.timer.start(1000)  # 1 second interval for updates
            self.label.show()
            self.frame.show()

    def _queue_hal_version_request(self):
        """Queue a request to get the HAL version"""
        url = self.core.config['hal_ip'] + '/api/get_hal_version/'
        TextComplicatedWidget._api_worker.queue_request(url, "hal")
    
    def _queue_build_id_request(self):
        """Queue a request to get the HAL build ID"""
        url = self.core.config['hal_ip'] + '/api/get_hal_build_ID/'
        TextComplicatedWidget._api_worker.queue_request(url, "hal")
    
    def _queue_disp_version_request(self):
        """Queue a request to get the display version"""
        url = self.core.config['disp_ip'] + '/.jenkinsenv'
        TextComplicatedWidget._api_worker.queue_request(url, "disp")
    
    def _handle_api_result(self, url, result):
        """Handle successful API results"""
        if '/api/get_hal_version/' in url:
            self.hal_version = result.replace('"', '')
        elif '/api/get_hal_build_ID/' in url:
            self.build_id = result.replace('"', '')
        elif '/.jenkinsenv' in url:
            text = result
            git_desc_pos = text.find('GIT_DESCRIBE=')
            if git_desc_pos != -1:
                ver_start_pos = git_desc_pos + len('GIT_DESCRIBE=')
                ver_end_pos = text[ver_start_pos:].find('/n')
                self.disp_version = text[ver_start_pos:ver_end_pos] if ver_end_pos > 0 else text[ver_start_pos:]
            else:
                self.disp_version = self.tr("no version")
    
    def _handle_api_error(self, url, error):
        """Handle API errors"""
        # Track error occurrences by URL
        if not hasattr(self, '_error_count'):
            self._error_count = {}
        
        self._error_count[url] = self._error_count.get(url, 0) + 1
        
        # Only log errors occasionally to reduce spam
        should_log = (self._error_count[url] <= 2) or (self._error_count[url] % 10 == 0)
        
        if should_log:
            if '/api/get_hal_version/' in url or '/api/get_hal_build_ID/' in url:
                logger.debug(f"HAL API error: {error}")
            elif '/.jenkinsenv' in url:
                logger.debug(f"DISP API error: {error}")
    
    def _handle_connection_status(self, service, status):
        """Handle connection status changes"""
        if service == "hal":
            self.core.hal_connection_status = status
            # Queue next request after a delay
            if self.label_text == "HAL ver.":
                self.timer.start(1000)  # restart timer for the next update
    
    def update_text(self) -> None:
        """
        Update the label text with the current version.
        """
        if self.label and self.version:
            self.label.setText(self.label_text + ' ' + self.version)

    def update_hal_ver_text(self) -> None:
        """
        Update the label text with the HAL version.
        """
        # Update the label text based on the cached result
        if self.core.hal_connection_status:
            self.label.setText(self.label_text + ' ' + self.hal_version)
            # Queue a new request if connected
            self._queue_hal_version_request()
        else:
            self.label.setText(self.label_text + self.tr(' connecting...'))
            # If not connected, we rely on the retry mechanism
            # Don't queue additional requests here

    def update_build_id_text(self) -> None:
        """
        Update the label text with the HAL build ID.
        """
        # Update the label text based on the cached result
        if self.core.hal_connection_status:
            self.label.setText(self.label_text + ' ' + self.build_id)
            # Queue a new request if connected
            self._queue_build_id_request()
        else:
            self.label.setText(self.label_text + self.tr(' connecting...'))
            # If not connected, we rely on the retry mechanism
            # Don't queue additional requests here

    def update_disp_ver_text(self) -> None:
        """
        Update the label text with the display version.
        """
        if self.disp_version == "connecting...":
            self.label.setText(self.label_text + self.tr(' connecting...'))
        else:
            self.label.setText(self.label_text + ' ' + self.disp_version)
            # Queue a new request only if we have a successful response before
            self._queue_disp_version_request()

    def update(self) -> None:
        """
        Update the widget text based on the configured data source.

        This method is called periodically by the timer to update the text
        with the latest data from the appropriate source.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if self.label_text == "HAL ver.":
            self.update_hal_ver_text()
        elif self.label_text == "HAL build ID":
            self.update_build_id_text()
        elif self.label_text == "Disp. ver.":
            self.update_disp_ver_text()
        elif vehid is not None and vehid in self.core.telemetry.keys():
            self.version = get_from_telemetry(self.core, self.field_to_read)
            self.update_text()
        else:
            self.label.setText(self.label_text)
            
    @classmethod
    def cleanup(cls):
        """Clean up the API worker thread when the application exits"""
        if cls._api_worker is not None:
            cls._api_worker.stop()
            cls._api_worker = None
