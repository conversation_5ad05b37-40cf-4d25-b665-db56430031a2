# Extracted class: ConditionalTextWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Optional, Any

from PyQt6 import QtCore
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import <PERSON><PERSON>abel, QWidget

class ConditionalTextWidget(QLabel):
    """
    A text widget that shows or hides itself based on a condition.

    This widget displays text that is only visible when a specific condition
    is met, based on a field in telemetry or core state.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """
        Initialize the ConditionalTextWidget.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__()
        self.core = core
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_visibility)

        # Will be initialized in prepare()
        self.from_telemetry: bool = False
        self.field_to_read: str = ""

    def prepare(self, text: str = "", font_size: str = "12px", x: int = 0, y: int = 0,
                font_color: str = "#AAAAAA", left: bool = False, screen: int = 1,
                field_to_read: str = "", from_telemetry: bool = False) -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            text: Text to display
            font_size: Font size in CSS format (e.g., "12px")
            x: X position of the widget
            y: Y position of the widget
            font_color: Text color in CSS format (e.g., "#AAAAAA")
            left: Whether to align text to the left (False for center)
            screen: Screen number (1+ for main screen, 0 for sensor screen)
            field_to_read: Name of the field to check for visibility
            from_telemetry: Whether to read from telemetry (True) or core (False)
        """
        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setStyleSheet("font-size: %s; color: %s " % (font_size, font_color))
        self.setWordWrap(True)
        self.from_telemetry = from_telemetry
        self.field_to_read = field_to_read

        if not left:
            self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        else:
            self.setAlignment(Qt.AlignmentFlag.AlignLeft)

        if screen >= 1:
            self.setParent(self.core.main_screen)
            self.move((screen - 1) * self.core.screen_width + x, y)
        else:
            self.setParent(self.core.sensor_screen)
            self.move(x, y)
        self.setText(text)

    def start(self) -> None:
        """
        Show the widget and start the update timer.
        """
        self.show()
        self.timer.start(100)  # 100ms interval for updates (reduced CPU usage)

    def update_visibility(self) -> None:
        """
        Update the visibility of the widget based on the condition.

        This method is called periodically by the timer to check if the
        widget should be visible or hidden based on the field value.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            flag = None
            if self.from_telemetry:
                selected_machine_telemetry = self.core.telemetry.get(vehid)
                if selected_machine_telemetry is not None:
                    flag = selected_machine_telemetry.get(self.field_to_read)
                else:
                    self.widget.color = self.disabled_color
                    self.widget.update()
                    return
            else:
                flag = getattr(self.core, self.field_to_read, None)
            if flag is None or not flag:
                self.setVisible(False)
            else:
                self.setVisible(True)
        else:
            self.setVisible(False)
