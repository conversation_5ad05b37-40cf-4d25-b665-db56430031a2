# Extracted class: MovingDataTranslator

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
from typing import Dict, Any, List, Optional

from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QWidget, QGridLayout, QLabel, QApplication

from gui_common import get_from_telemetry

class MovingDataTranslator(object):
    """
    A widget that displays and updates telemetry data with smoothing.

    This widget shows multiple telemetry values with labels, applying
    smoothing to the values for better readability.
    """

    def __init__(self, core: Any) -> None:
        """
        Initialize the MovingDataTranslator.

        Args:
            core: The core application object
        """
        super().__init__()
        self.core = core
        self.killed = False
        self.labels: Dict[str, QLabel] = {}
        self.grid = QGridLayout()
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)
        self.old_vals: Dict[str, float] = {}
        self.mode_visible: List[str] = []
        self.no_fraction: bool = False

        # Will be initialized in prepare()
        self.telemetry: Optional[Dict[str, Dict[str, Any]]] = None
        self.widget: Optional[QWidget] = None
        self.lbl_value_params: Optional[Dict[str, str]] = None

    def kill(self) -> None:
        """
        Mark the widget as killed to stop update thread.
        """
        self.killed = True

    def prepare(self, telemetry: Dict[str, Dict[str, Any]], lbl_name_params: Dict[str, str],
                lbl_value_params: Dict[str, str], x: int, y: int, width: int, height: int,
                screen_num: int, mode_visible: Optional[List[str]] = None,
                value_on_new_line: bool = False, no_fraction: bool = False) -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            telemetry: Dictionary of telemetry fields to display
            lbl_name_params: Style parameters for the name labels
            lbl_value_params: Style parameters for the value labels
            x: X position of the widget
            y: Y position of the widget
            width: Width of the widget
            height: Height of the widget
            screen_num: Screen number (0 for sensor screen, 1+ for main screen)
            mode_visible: List of camera modes where this widget is visible
            value_on_new_line: Whether to show values on a new line
            no_fraction: Whether to show values as integers (no decimal part)
        """
        self.telemetry = telemetry
        self.mode_visible = mode_visible if mode_visible is not None else []
        self.no_fraction = no_fraction
        self.lbl_value_params = lbl_value_params
        if screen_num == 0:
            self.widget = QWidget(self.core.sensor_screen)
            self.widget.setGeometry(x, y, width, height)
        else:
            self.widget = QWidget(self.core.main_screen)
            self.widget.setGeometry(x + self.core.screen_width * (screen_num - 1), y, width, height)

        for k in self.telemetry.keys():
            lbl_name = QLabel()
            lbl_value = QLabel()
            lbl_name.setText(QApplication.translate("MovingDataTranslator", self.telemetry[k]['name']) + ':')
            lbl_name.setStyleSheet("color: %s; font-size: %s; text-transform: %s; font-weight: %s;" % (
            lbl_name_params["color"], lbl_name_params["font_size"], lbl_name_params["text_transform"],
            lbl_name_params["font_weight"]))
            lbl_value.setStyleSheet("color: %s; font-size: %s; text-transform: %s; font-weight: %s;" % (
            lbl_value_params["color"], lbl_value_params["font_size"], lbl_value_params["text_transform"],
            lbl_value_params["font_weight"]))

            if not value_on_new_line:
                index = list(self.telemetry).index(k)
                self.grid.addWidget(lbl_name, index, 0)
                self.grid.addWidget(lbl_value, index, 1)
            else:
                index = list(self.telemetry).index(k) * 2
                self.grid.addWidget(lbl_name, index, 0)
                self.grid.addWidget(lbl_value, index + 1, 0)

            self.labels[k] = lbl_value
            self.old_vals[k] = 0.0

        self.widget.setLayout(self.grid)

    def start(self) -> None:
        """
        Start the widget update timer.
        """
        self.timer.start(100)  # 100ms interval for updates (reduced CPU usage)

    def update(self) -> None:
        """
        Update the widget values based on telemetry data.

        This method is called periodically by the timer to update the displayed
        values with the latest telemetry data, applying smoothing.
        """
        if self.core.cameras_mode in self.mode_visible or self.mode_visible[0] == 'ALL':
            self.widget.setHidden(False)
        else:
            self.widget.setHidden(True)

        for k in self.telemetry.keys():
            res = get_from_telemetry(self.core, k)
            self.old_vals[k] += (res - self.old_vals[k]) * self.telemetry[k]['smooth_k']
            units = QApplication.translate("MovingDataTranslator", self.telemetry[k]['units'])
            if not self.no_fraction:
                self.labels[k].setText("%.1f" % (self.old_vals[k] * self.telemetry[k]['scale']) + " " + units)
            else:
                self.labels[k].setText("%d" % (round(self.old_vals[k] * self.telemetry[k]['scale'])) + " " + units)

            if ("red_less" in self.telemetry[k].keys() and self.old_vals[k] < self.telemetry[k]["red_less"]) or \
                    ("red_more" in self.telemetry[k].keys() and self.old_vals[k] > self.telemetry[k]["red_more"]):
                self.labels[k].setStyleSheet("color: %s; font-size: %s; text-transform: %s; font-weight: %s;" % (
                "#FF0000", self.lbl_value_params["font_size"], self.lbl_value_params["text_transform"],
                self.lbl_value_params["font_weight"]))
            else:
                self.labels[k].setStyleSheet("color: %s; font-size: %s; text-transform: %s; font-weight: %s;" % (
                self.lbl_value_params["color"], self.lbl_value_params["font_size"],
                self.lbl_value_params["text_transform"], self.lbl_value_params["font_weight"]))
