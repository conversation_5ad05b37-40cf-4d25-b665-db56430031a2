import os
import glob
import importlib
from importlib import import_module
from types import ModuleType

# Define the current directory
current_dir = os.path.dirname(__file__)

# List of subdirectories to process
subdirectories = ['buttons', 'controls', 'containers', 'indicators', 'text', 'map', 'cameras', 'data']

def import_from_file(file_path):
    """Import a module from a file path and expose its classes"""
    # Get just the base filename without extension
    module_name = os.path.splitext(os.path.basename(file_path))[0]
    
    # Skip __init__.py files
    if module_name == '__init__':
        return
    
    # Determine the module import path
    # If file is in subdirectory, include the subdirectory in the path
    rel_path = os.path.relpath(os.path.dirname(file_path), current_dir)
    if rel_path == '.':
        # File is in the root directory
        module_path = f"gui_modules.{module_name}"
    else:
        # File is in a subdirectory
        subdir = rel_path.replace(os.sep, '.')
        module_path = f"gui_modules.{subdir}.{module_name}"
        
    try:
        mod = import_module(module_path)
        # Re-export all classes defined in the module
        for attr in dir(mod):
            obj = getattr(mod, attr)
            if isinstance(obj, type) and obj.__module__ == mod.__name__:
                globals()[attr] = obj
    except Exception as e:
        print(f"Error importing {module_path}: {e}")

# Import from root directory (except __init__.py)
for file in glob.glob(os.path.join(current_dir, "*.py")):
    if os.path.basename(file) != "__init__.py":
        import_from_file(file)

# Import from all subdirectories
for subdir in subdirectories:
    subdir_path = os.path.join(current_dir, subdir)
    if os.path.isdir(subdir_path):
        for file in glob.glob(os.path.join(subdir_path, "*.py")):
            import_from_file(file)

# Define exports
__all__ = [name for name in globals() if not name.startswith("_")]
