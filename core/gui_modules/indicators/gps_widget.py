# Extracted class: GPSWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from typing import Optional, Any, Dict

from PyQt6.QtCore import QTimer
from PyQt6 import QtSvgWidgets

from gui_common import RESOURCES_CONFIG

class GPSWidget(object):
    """
    Widget for displaying GPS status with colored icons.

    This widget shows the current GPS status using colored SVG icons:
    - Green: GPS fix with high quality (state 4)
    - Yellow: GPS fix with medium quality (state 1-3)
    - Red: No GPS fix (state 0)
    - Gray: No GPS data available
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """
        Initialize the GPSWidget.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)
        self.gray_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "gps_gray.svg")
        self.red_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "gps_red.svg")
        self.green_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "gps_green.svg")
        self.yellow_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "gps_yellow.svg")
        self.last_qual = None

    def prepare(self, r: int = 500, x: int = 500, y: int = 800, screen: int = 1) -> None:
        """
        Prepare the GPS widget with the specified parameters.

        Args:
            r: Radius/size of the widget
            x: X position of the widget
            y: Y position of the widget
            screen: Screen number (1+ for main screen)
        """
        self.widget = QtSvgWidgets.QSvgWidget(self.core_screen)
        self.widget.load(self.gray_file_path)

        width = r
        height = r
        self.widget.setGeometry(x + self.core.screen_width * (screen - 1), y, width, height)
        self.widget.show()

    def start(self) -> None:
        """
        Show the widget and start the update timer.
        """
        if self.widget:
            self.widget.show()
            self.timer.start(100)  # Changed from 10ms to 100ms to reduce CPU usage

    def update(self) -> None:
        """
        Update the GPS widget with current telemetry data.

        This method is called periodically by the timer to update the position
        markers on the map based on the current GPS coordinates from telemetry.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None and vehid in self.core.telemetry.keys():
            state = self.core.telemetry[vehid].get('gps_quality', None)
            if state is None:
                self.last_qual = None
                return
            if state != self.last_qual:
                if state == 4:
                    self.widget.load(self.green_file_path)
                elif state == 0:
                    self.widget.load(self.red_file_path)
                else:
                    self.widget.load(self.yellow_file_path)
                self.last_qual = state
        else:
            self.widget.load(self.gray_file_path)
            self.last_qual = None
