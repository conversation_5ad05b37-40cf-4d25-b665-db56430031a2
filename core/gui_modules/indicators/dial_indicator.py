# Extracted class: DialIndicatorWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from typing import Dict, Any, Optional, List, Union

from PyQt6.QtCore import Qt, QTimer, QPointF, QRect, QPoint
from PyQt6.QtWidgets import QWidget, QApplication
from PyQt6.QtGui import (QColor, QPen, QBrush, QFont, QFontMetrics, QPixmap,
                         QPolygon, QConicalGradient, QPainter)

from gui_common import RESOURCES_CONFIG, get_from_telemetry

class DialIndicatorWidget(QWidget):
    """
    Widget for a dial indicator with a circular scale.

    This widget displays a gauge with a needle that points to a value on a circular scale.
    The scale can have a color gradient and display multiple values.
    """

    def __init__(self, core: Any, ticks_font_size: int, scale_range: int, scale_step: int,
                 initial_value: float, scale_gradient: Optional[Dict[str, float]],
                 caption: str, caption_font_size: int, units2: Optional[str],
                 *args, **kwargs) -> None:
        """
        Initialize the DialIndicatorWidget.

        Args:
            core: The core application object
            ticks_font_size: Font size for the tick labels
            scale_range: Maximum value on the scale
            scale_step: Step between tick marks
            initial_value: Initial value for the needle
            scale_gradient: Dictionary mapping colors to positions on the scale
            caption: Text caption for the widget
            caption_font_size: Font size for the caption
            units2: Units for the secondary value display
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super(DialIndicatorWidget, self).__init__(*args, **kwargs)
        background_image_file_path = os.path.join(RESOURCES_CONFIG["images_folder"],
                                                  "DialIndicator.png")
        self._bg = QPixmap(background_image_file_path)
        self.ticks_cnt = scale_range // scale_step
        self.step_angle = 270 / self.ticks_cnt
        self.tick_values = [x * scale_step for x in range(self.ticks_cnt + 1)]
        self.scale_step = scale_step
        self.caption = caption
        self.ticks_font_size = ticks_font_size
        self.caption_font_size = caption_font_size
        self.scale_gradient = scale_gradient
        self.value = initial_value
        self.value_real = 0
        self.value_real2 = None
        self._value = 0  # Will be set in setValue
        self.setValue(initial_value, 0)
        self.units2 = units2
        self.tickColor = Qt.GlobalColor.red

        self.setParent(core.main_screen)

    def setValue(self, val: float, val_real: float, val_real2: Optional[float] = None) -> None:
        """
        Set the values for the dial indicator.

        Args:
            val: Normalized value (0-1) for the needle position
            val_real: Actual value to display
            val_real2: Secondary value to display (optional)
        """
        val = float(min(max(val, 0), 1))
        self._value = -270 * val
        self.value_real = val_real
        self.value_real2 = val_real2
        self.update()

    def paintEvent(self, event) -> None:
        """
        Paint the dial indicator widget.

        Args:
            event: The paint event
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        rect = event.rect()

        # background
        gauge_rect = QRect(rect)
        size = gauge_rect.size()

        pos = gauge_rect.center()
        gauge_rect.moveCenter(QPoint(int(pos.x() - size.width()), int(pos.y() - size.height())))
        gauge_rect.setSize(size * .9)
        gauge_rect.moveCenter(pos)

        refill_rect = QRect(gauge_rect)
        size = refill_rect.size()
        pos = refill_rect.center()
        refill_rect.moveCenter(QPoint(int(pos.x() - size.width()), int(pos.y() - size.height())))
        # smaller than .9 == thicker gauge
        refill_rect.setSize(size * .9)
        refill_rect.moveCenter(pos)

        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawPixmap(rect, self._bg)
        painter.save()

        # scale gradient
        grad = QConicalGradient(QPointF(gauge_rect.center()), 270.0)
        if not self.scale_gradient:
            grad.setColorAt(.75, Qt.GlobalColor.green)
            grad.setColorAt(.5, Qt.GlobalColor.yellow)
            grad.setColorAt(.25, Qt.GlobalColor.red)
        else:
            for color, point in self.scale_gradient.items():
                grad.setColorAt(point, QColor(color))
        painter.setBrush(grad)
        # Convert float values to int for drawPie
        start_angle = int(225.0 * 16)
        span_angle = int(self._value * 16)
        painter.drawPie(gauge_rect, start_angle, span_angle)
        # раскомментировать чтобы отвязать градиент от стрелки
        # painter.drawPie(gauge_rect, 225.0*16, -270*16)
        painter.restore()

        painter.setBrush(QBrush(self._bg.scaled(rect.size())))
        painter.drawEllipse(refill_rect)

        self.tickColor = Qt.GlobalColor.red
        pen = QPen(self.tickColor, 2, Qt.PenStyle.SolidLine)
        painter.setPen(pen)
        font = QFont(RESOURCES_CONFIG["default_font_name"], self.ticks_font_size)
        painter.setFont(font)

        # move painter to center
        painter.translate(int(self.width() / 2), int(self.height() / 2))

        # draw tick lines
        painter.rotate(45.0)
        cur_angle = 45.0
        line_start = size.width() / 2
        line_end = size.width() / 2 - 0.08 * size.width()
        text_start = size.width() / 2 - 0.12 * size.width()
        metrics = QFontMetrics(font)
        for i, tick_value in enumerate(reversed(self.tick_values)):
            painter.drawLine(int(line_start), 0, int(line_end), 0)
            painter.save()
            painter.translate(int(text_start), 0)
            # remove ticks rotation
            painter.rotate(-cur_angle)
            tick_x_offset = 0
            if i / self.ticks_cnt < 0.5:
                text_width = metrics.horizontalAdvance(str(tick_value))
                tick_x_offset = -text_width * 0.9
            elif i / self.ticks_cnt == 0.5:
                text_width = metrics.horizontalAdvance(str(tick_value))
                tick_x_offset = -text_width / 1.5
            painter.setPen(Qt.GlobalColor.white)
            # draw tick labels
            painter.drawText(int(tick_x_offset), 10, str(tick_value))
            # return rotation
            painter.restore()
            painter.rotate(-self.step_angle)
            cur_angle += -self.step_angle

        # rotate to initial state
        painter.rotate(-cur_angle)

        # draw caption
        painter.save()
        painter.setPen(Qt.GlobalColor.white)

        font = QFont(RESOURCES_CONFIG["default_font_name"], self.caption_font_size + 5)
        font.setBold(True)
        painter.setFont(font)
        metrics = QFontMetrics(font)
        rv = "%.1f" % self.value_real
        painter.translate(0, int(self.height() / 2 * 0.4))
        painter.drawText(int(-metrics.horizontalAdvance(rv) / 2), 0, rv)
        painter.translate(0, int(-self.height() / 2 * 0.4))

        font = QFont(RESOURCES_CONFIG["default_font_name"], self.caption_font_size)
        painter.setFont(font)
        # move to bottom
        painter.translate(0, int(self.height() / 2 * 0.7))
        metrics = QFontMetrics(font)

        # draw multiple-line text
        lines = self.caption.split('\n')
        y = -5
        for line in lines:
            line_width = metrics.horizontalAdvance(line)
            painter.drawText(int(-line_width / 2), int(y), line)
            y += metrics.height()

        if self.value_real2 is not None:
            painter.save()
            painter.resetTransform()
            font = QFont(RESOURCES_CONFIG["default_font_name"], self.caption_font_size)
            font.setWeight(QFont.Weight.Bold)
            painter.setFont(font)
            painter.translate(int(self.width() - 50), int(self.height() - 10))
            painter.drawText(0, 0, "%.0f %s" % (self.value_real2, self.units2))
            painter.restore()

        painter.restore()

        # rotate to 0 tick
        painter.rotate(-135)
        # rotate to value
        painter.rotate(-self._value)

        # draw needle
        needle = QPolygon([
            QPoint(2, 0),
            QPoint(0, 2),
            QPoint(-2, 0),
            QPoint(0, int(-size.width() / 2 * 0.75))
        ])
        painter.setBrush(Qt.GlobalColor.red)
        painter.drawConvexPolygon(needle)
        # painter.restore()
        painter.end()
        super(DialIndicatorWidget, self).paintEvent(event)


# Extracted class: DialIndicator

#!/usr/bin/env python
# -*- coding: utf-8 -*-

class DialIndicator(object):
    """
    Class for initializing and updating a circular dial indicator.

    This class creates and manages a dial indicator widget that displays
    a value from telemetry on a circular gauge with a needle.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """
        Initialize the DialIndicator.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.mode_visible: Optional[List[str]] = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)

        # Will be initialized in prepare()
        self.field_to_read: str = ""
        self.scale_range: int = 0
        self.coeff: float = 0
        self.coeff2: Optional[float] = None
        self.digit_value_coeff: float = 0
        self.widget: Optional[DialIndicatorWidget] = None

    def prepare(self, width: int = 750, height: int = 500, x: int = 500, y: int = 800,
                ticks_font_size: int = 14, field_to_read: str = "", scale_range: int = 200,
                scale_step: int = 10, initial_value: float = 50, scale_gradient: Union[str, Dict[str, float]] = "",
                caption: str = "", caption_font_size: int = 14, coeff: float = 1,
                coeff2: Optional[float] = None, digit_value_coeff: float = 1,
                units2: Optional[str] = None, modes_visible: List[str] = ["ALL"],
                screen_num: int = 1) -> None:
        """
        Prepare the dial indicator widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            ticks_font_size: Font size for the tick labels
            field_to_read: Name of the field to read from telemetry
            scale_range: Maximum value on the scale
            scale_step: Step between tick marks
            initial_value: Initial value for the needle
            scale_gradient: Dictionary mapping colors to positions on the scale
            caption: Text caption for the widget
            caption_font_size: Font size for the caption
            coeff: Coefficient to multiply the telemetry value by
            coeff2: Secondary coefficient for the second display
            digit_value_coeff: Coefficient for the digital value display
            units2: Units for the secondary value display
            modes_visible: List of camera modes where this widget is visible
            screen_num: Screen number (1+ for main screen)
        """
        initial_value_normed = initial_value / scale_range
        self.field_to_read = field_to_read
        self.scale_range = scale_range
        self.coeff = coeff
        self.coeff2 = coeff2
        self.digit_value_coeff = digit_value_coeff
        self.widget = DialIndicatorWidget(core=self.core,
                                          ticks_font_size=ticks_font_size,
                                          scale_range=scale_range,
                                          scale_step=scale_step,
                                          initial_value=initial_value_normed,
                                          scale_gradient=scale_gradient,
                                          caption=QApplication.translate("DialIndicator", caption),
                                          caption_font_size=caption_font_size,
                                          units2=units2
                                          )
        self.widget.setFixedSize(width, height)
        self.widget.move(self.core.screen_width * (screen_num - 1) + x, y)
        self.mode_visible = modes_visible

    def start(self) -> None:
        """
        Show the widget and start the update timer.
        """
        if self.widget:
            self.widget.show()
            if self.field_to_read:
                # Use timer instead of thread for updates
                self.timer.start(50)  # Changed from 5ms to 50ms to reduce CPU usage

    def update(self) -> None:
        """
        Update the dial indicator with current telemetry data.

        This method is called periodically by the timer to update the needle
        position and displayed values based on the current telemetry data.
        """

        if self.core.cameras_mode in self.mode_visible or self.mode_visible[0] == 'ALL':
            self.widget.setHidden(False)
        else:
            self.widget.setHidden(True)
        vehid = self.core.in_rc_vehid or self.core.watched_vehid
        if not vehid:
            return
        value = get_from_telemetry(self.core, self.field_to_read)
        if value * self.coeff / self.scale_range == self.widget._value:
            return

        normed_value = value * self.coeff / self.scale_range
        self.widget.setValue(normed_value, value * self.coeff * self.digit_value_coeff,
                             value * self.coeff2 if self.coeff2 is not None else None)
