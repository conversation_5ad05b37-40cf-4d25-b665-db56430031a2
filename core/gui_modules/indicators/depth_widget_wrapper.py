# Extracted class: DepthWidgetWrapper

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, Optional

from PyQt6 import QtCore
from PyQt6.QtCore import QObject, QTimer, QSize, QPoint, <PERSON><PERSON>argins
from PyQt6.QtWidgets import QWidget, QVBoxLayout

from customClasses.darkPalette import darkPalette
from customClasses.depthDrillWidget import DepthDrillWidget

class DepthWidgetWrapper(QObject):
    """
    Widget for displaying drill position, hole depth, and target depth.

    This widget shows the current position of the drill, the actual depth of the hole,
    and the target depth. Minimum width: 155 pixels!
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """
        Initialize the DepthWidgetWrapper.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.core = core
        self.core_screen = core.main_screen
        self.updateTimer = QTimer()
        self.updateTimer.setInterval(200)
        self.updateTimer.timeout.connect(self.timerAction)

        # Will be initialized in prepare()
        self.parentWrapper: Optional[QWidget] = None
        self.depthWidth: Optional[DepthDrillWidget] = None
        self.dictToRead: str = ""
        self.actualDepthField: str = ""
        self.targetDepthField: str = ""
        self.drillPosField: str = ""

    def prepare(self, width: int, height: int, x: int, y: int, dictToRead: str,
                screen_num: int, actualDepthField: str, targetDepthField: str,
                drillPosField: str) -> None:
        """
        Prepare the depth widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            dictToRead: Name of the dictionary to read from core
            screen_num: Screen number (1+ for main screen)
            actualDepthField: Name of the field for actual depth
            targetDepthField: Name of the field for target depth
            drillPosField: Name of the field for drill position
        """
        # create layouts and fake wrapper widget just for parenting *facepalm*
        self.dictToRead = dictToRead
        self.actualDepthField = actualDepthField
        self.targetDepthField = targetDepthField
        self.drillPosField = drillPosField
        self.parentWrapper = QWidget(parent=self.core_screen)
        self.parentWrapper.setPalette(darkPalette)
        mainLayout = QVBoxLayout()
        mainLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        self.parentWrapper.setLayout(mainLayout)
        self.parentWrapper.setFixedSize(QSize(width, height))
        x += self.core.screen_width * (screen_num - 1)
        self.parentWrapper.move(QPoint(x, y))
        self.parentWrapper.setAutoFillBackground(True)
        # create wrapped object
        self.depthWidth = DepthDrillWidget()

        # composing
        mainLayout.addWidget(self.depthWidth)

    def timerAction(self) -> None:
        """
        Update the depth widget with current telemetry data.

        This method is called periodically by the timer to update the widget
        with the latest drill position, actual depth, and target depth values.
        """
        if self.core.in_rc_vehid is not None:
            vehid = self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            vehid = self.core.watched_vehid
        else:
            self.depthWidth.setDrillPosition(0)
            self.depthWidth.setTargetDepth(None)
            self.depthWidth.setActualDepth(0)
            return
        d = getattr(self.core, self.dictToRead)[vehid]
        if self.drillPosField in d.keys() and d[self.drillPosField] is not None:
            self.depthWidth.setDrillPosition(d[self.drillPosField])
        if self.targetDepthField in d.keys() and d[self.targetDepthField] is not None:
            self.depthWidth.setTargetDepth(d[self.targetDepthField])
        if self.actualDepthField in d.keys() and d[self.actualDepthField] is not None:
            self.depthWidth.setActualDepth(d[self.actualDepthField])

        if "state_duration_sec" in d.keys() and d["state_duration_sec"] is not None:
            if 'MainStateMachineNode' in d.keys():
                if d["MainStateMachineNode"] == "drilling":
                    self.depthWidth.setDrillDuration(d["state_duration_sec"])
                else:
                    if 'DrillerNode' in d.keys():
                        if d["DrillerNode"] in ['touchdown', 'overburden_pass', 'drilling', 'hard_rot', 'pullup',
                                                'after_pullup', 'raise', 'pass_soft']:
                            self.depthWidth.setDrillDuration(d["state_duration_sec"])
                        else:
                            self.depthWidth.setDrillDuration(0)
            else:
                self.depthWidth.setDrillDuration(0)
        else:
            self.depthWidth.setDrillDuration(0)

    def start(self) -> None:
        """
        Show the widget and start the update timer.
        """
        if self.parentWrapper:
            self.parentWrapper.show()
            self.updateTimer.start()
