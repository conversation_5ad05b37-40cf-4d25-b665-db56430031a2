# Extracted class: CircleWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Optional, Any, List

from PyQt6 import QtCore
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import (QWidget, QGridLayout, QLabel, QApplication,
                             QSpacerItem)
from PyQt6.QtGui import QColor, QPainter


class CircleWidgetPainter(QWidget):
    """
    A circular widget that can be colored to indicate different states.

    This widget draws a simple colored circle that can be used as a visual indicator.
    """

    def __init__(self, color: str, *args, **kwargs) -> None:
        """
        Initialize the CircleWidgetPainter.

        Args:
            color: The color of the circle in CSS format (e.g., "#00ff00")
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super(CircleWidgetPainter, self).__init__(*args, **kwargs)
        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.color = color
        self.update()

    def paintEvent(self, event) -> None:
        """
        Paint the circular widget.

        Args:
            event: The paint event
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        rect = event.rect()
        size = rect.size()

        r = int(size.width())
        painter.setBrush(QColor(self.color))
        painter.drawEllipse(0, 0, r, r)
        super(CircleWidgetPainter, self).paintEvent(event)



class CircleWidget(object):
    """
    A widget that displays a colored circle with optional labels.

    This widget can show a colored circle that changes based on telemetry data,
    with optional static and dynamic labels.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """
        Initialize the CircleWidget.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)
        self.second_field_to_read: Optional[str] = None
        self.intermediate_color: Optional[str] = None
        self.state_label: Optional[QLabel] = None
        self.static_label: Optional[QLabel] = None

        # Will be initialized in prepare()
        self.frame: Optional[QWidget] = None
        self.circle: Optional[CircleWidgetPainter] = None
        self.from_telemetry: bool = False
        self.field_to_read: str = ""
        self.on_color: str = "#00ff00"
        self.off_color: str = "#ff0000"
        self.disabled_color: str = "#aaaaaa"
        self.circle_offset: int = 0
        self.state_texts: Optional[Dict[str, str]] = None

    def prepare(self, r: int = 25, x: int = 500, y: int = 800, from_telemetry: bool = False,
                field_to_read: str = "", second_field_to_read: Optional[str] = None,
                on_color: str = "#00ff00", off_color: str = "#FF0000",
                disabled_color: str = "#AAAAAA", intermediate_color: str = "#FFA500",
                static_label: str = "", static_font_size: str = "19px",
                dynamic_label: bool = False, on_text: str = "On", off_text: str = "Off",
                intermediate_text: str = "Moving", disabled_text: str = "No data",
                font_size: str = "19px", font_color: str = "#FFFFFF",
                label_width: int = 95, dynamic_label_offset: int = 15,
                dynamic_label_width: int = 120, screen: int = 1) -> None:
        """
        Prepare the circle widget with the specified parameters.

        Args:
            r: Radius of the circle in pixels
            x: X position of the widget
            y: Y position of the widget
            from_telemetry: Whether to read data from telemetry
            field_to_read: Name of the field to read from telemetry
            second_field_to_read: Name of the secondary field to read
            on_color: Color for the 'on' state
            off_color: Color for the 'off' state
            disabled_color: Color for the 'disabled' state
            intermediate_color: Color for the 'intermediate' state
            static_label: Text for the static label
            static_font_size: Font size for the static label
            dynamic_label: Whether to show a dynamic label
            on_text: Text for the 'on' state
            off_text: Text for the 'off' state
            intermediate_text: Text for the 'intermediate' state
            disabled_text: Text for the 'disabled' state
            font_size: Font size for the dynamic label
            font_color: Font color for labels
            label_width: Width of the static label
            dynamic_label_offset: Offset for the dynamic label
            dynamic_label_width: Width of the dynamic label
            screen: Screen number (1+ for main screen)
        """

        self.from_telemetry = from_telemetry
        self.field_to_read = field_to_read
        self.second_field_to_read = second_field_to_read
        self.on_color = on_color
        self.off_color = off_color
        self.disabled_color = disabled_color
        self.intermediate_color = intermediate_color
        self.circle_offset = 2 * r

        self.frame = QWidget(self.core_screen)
        grid = QGridLayout()
        grid.setContentsMargins(0, 0, 0, 0)
        grid.setSpacing(0)
        self.frame.setLayout(grid)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        # Определяем тип виджета
        has_static = bool(static_label)
        has_dynamic = bool(dynamic_label)

        # Когда круг без надписей
        if not has_static and not has_dynamic:
            self.circle = CircleWidgetPainter(color=off_color)
            self.circle.setFixedSize(r, r)
            grid.addWidget(self.circle, 0, 0)

            width = 2 * r
            height = 2 * r
            self.frame.setGeometry(x + self.core.screen_width * (screen - 1), y, width, height)
            return

        # Для случаев с надписями
        current_column = 0
        frame_x = x
        frame_height = r

        # Статическая метка
        if has_static:
            self.static_label = QLabel(QApplication.translate("StaticLabel", static_label))
            self.static_label.setStyleSheet(f"""
                QLabel {{
                    color: {font_color};
                    font-size: {static_font_size};
                }}
            """)
            self.static_label.setFixedWidth(label_width)
            self.static_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            grid.addWidget(self.static_label, 0, current_column)
            current_column += 1

            # Добавляем отступ до круга
            spacer_width = self.circle_offset - label_width
            if spacer_width > 0:
                grid.addItem(QSpacerItem(spacer_width, 1), 0, current_column)
                current_column += 1

            # Корректируем x с учетом static_label
            frame_x = x - label_width
        # Круговой индикатор
        self.circle = CircleWidgetPainter(color=off_color)
        self.circle.setFixedSize(r, r)
        grid.addWidget(self.circle, 0, current_column)
        current_column += 1

        # Динамическая метка
        if has_dynamic:
            self.state_texts = {
                'on': on_text,
                'off': off_text,
                'intermediate': intermediate_text,
                'disabled': disabled_text
            }

            # Отступ до dynamic_label
            grid.addItem(QSpacerItem(dynamic_label_offset, 1), 0, current_column)
            current_column += 1

            # Создаем dynamic_label
            self.state_label = QLabel()
            self.state_label.setStyleSheet(f"""
                QLabel {{
                    color: {font_color};
                    font-size: {font_size};
                }}
            """)

            #max_text_length = max(len(text) for text in self.state_texts.values())
            min_width = dynamic_label_width
            self.state_label.setMinimumWidth(min_width)
            self.state_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            grid.addWidget(self.state_label, 0, current_column)

        # Устанавливаем геометрию с учетом типа виджета
        self.frame.setGeometry(
            int(frame_x + self.core.screen_width * (screen - 1)),
            int(y + r / 2),
            int(self.frame.sizeHint().width()),
            int(frame_height)
        )

    def start(self) -> None:
        """
        Show the widget and start the update timer.
        """
        if self.frame:
            self.frame.show()
            self.timer.start(100)  # Changed from 10ms to 100ms to reduce CPU usage

    def update(self) -> None:
        """
        Update the widget state based on telemetry data.

        This method is called periodically by the timer to update the circle color
        and label text based on the current state of the field being monitored.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            # Получаем значения состояний
            selected_machine_telemetry = self.core.telemetry.get(vehid)
            if self.from_telemetry:
                if selected_machine_telemetry is None:
                    self._set_state('disabled')
                    return

                flag = selected_machine_telemetry.get(self.field_to_read)
                flag2 = selected_machine_telemetry.get(self.second_field_to_read) if self.second_field_to_read else None
            else:
                flag = getattr(self.core, self.field_to_read, None)
                flag2 = getattr(self.core, self.second_field_to_read, None) if self.second_field_to_read else None

            if flag is None or (self.second_field_to_read and flag2 is None):
                self._set_state('disabled')
                return

            if self.second_field_to_read is None:
                self._set_state('on' if flag else 'off')
            else:
                if flag:
                    self._set_state('on')
                elif not flag2:
                    self._set_state('intermediate')
                else:
                    self._set_state('off')
        else:
            self._set_state('disabled')

    def _set_state(self, state: str) -> None:
        """
        Set the color and text according to the state.

        Args:
            state: The current state ('on', 'off', 'intermediate', or 'disabled')
        """
        colors = {
            'on': self.on_color,
            'off': self.off_color,
            'intermediate': self.intermediate_color,
            'disabled': self.disabled_color
        }
        self.circle.color = colors[state]
        self.circle.update()

        if self.state_label is not None:
            self.state_label.setText(
                QApplication.translate("DynamicLabel", self.state_texts[state])
            )
