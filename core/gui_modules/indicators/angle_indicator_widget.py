#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from typing import Optional, Any, Dict

from PyQt6 import QtCore
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import (QW<PERSON>t, Q<PERSON>ainWindow, QGridLayout, QGraphicsScene,
                             QGraphicsView)
from PyQt6.QtGui import (QColor, QPen, QBrush, QFont, QFontMetrics, QPixmap,
                         QTransform)

from gui_common import RESOURCES_CONFIG, get_from_telemetry, FULL_RESTRICTIONS_OFF
from customClasses.blinkBehaviour import BlinkingBehaviour
from customClasses.darkPalette import ColorSet
from base_widget import ZenohWidget


class AngleIndicatorScene(QGraphicsScene, BlinkingBehaviour):
    """
    The graphics scene that renders the angle indicator visualization.
    """

    def __init__(self, core: Any, width: int, height: int, label_font_size: int, view: Optional[QGraphicsView] = None) -> None:
        """
        Initialize the AngleIndicatorScene.

        Args:
            core: The core application object
            width: Width of the scene
            height: Height of the scene
            label_font_size: Font size for labels
            view: The QGraphicsView that displays this scene
        """
        super().__init__()
        self.core = core
        self.width = width
        self.height = height
        self.label_font_size = label_font_size
        self.view = view

        # Visual properties
        self._color = QColor("#35ff02")
        self._second_color = ColorSet.textColor.value
        self._blinkColor = ColorSet.warningRedBlinkingColor.value
        self.bubble_color = self._color
        self.lines_color = "#ffffff"
        self.two_colors = True

        # UI elements - will be initialized in draw()
        self.bg = None
        self.bg_r = 0
        self.max_r = 0
        self.x_center = 0
        self.y_center = 0
        self.bubble = None
        self.mast = None
        self.text = None
        self.roll_label = None
        self.pitch_label = None
        self.pm_center_x = 0
        self.pm_center_y = 0
        self.font_h = 0
        self.font_metrics = None
        self.mast_text_rect = None
        self.mast_text_rect_x_center = 0
        self.mast_text_rect_y_center = 0
        self.mast_text_font_size = 12

    def update_bubble(self, roll: float, pitch: float) -> None:
        """
        Update the position of the bubble based on roll and pitch angles.

        Args:
            roll: Roll angle in degrees
            pitch: Pitch angle in degrees
        """
        if not self.bubble:
            return
            
        # Scale and limit values
        roll /= 8.0
        pitch /= 8.0
        pitch = min(1.0, max(-1.0, pitch))
        roll = min(1.0, max(-1.0, roll))
        
        # Calculate position
        y = (self.max_r - 5) * pitch
        x = (self.max_r - 5) * roll

        # Check restrictions mode
        rest_mode = get_from_telemetry(self.core, 'restrictions_mode')
        if rest_mode == FULL_RESTRICTIONS_OFF:
            self.setBlinking(True)
            self.bubble.setPen(QPen(self._color, 2))
            self.bubble.setBrush(QBrush(self._color))

        self.bubble.setPos(x, y)
        self.update()

    def update_mast(self, mast_angle: float) -> None:
        """
        Update the rotation of the mast indicator.

        Args:
            mast_angle: Mast angle in degrees
        """
        if not self.mast:
            return
            
        transform = QTransform()
        transform.translate(self.pm_center_x, self.pm_center_y)
        transform.rotate(mast_angle)
        transform.translate(-self.pm_center_x, -self.pm_center_y)
        self.mast.setTransform(transform)

    def set_text(self, mast_angle: str, pitch_angle: str, roll_angle: str) -> None:
        """
        Update the text displays with current angle values.

        Args:
            mast_angle: Mast angle as a string
            pitch_angle: Pitch angle as a string
            roll_angle: Roll angle as a string
        """
        if not all([self.text, self.roll_label, self.pitch_label, self.font_metrics]):
            return
            
        self.text.setPlainText(mast_angle)
        font_w = self.font_metrics.horizontalAdvance(mast_angle)
        mast_text_left_x = int(self.mast_text_rect_x_center - font_w / 2)
        mast_text_top_y = int(self.mast_text_rect_y_center - self.font_h)
        self.text.setPos(mast_text_left_x, mast_text_top_y)

        self.roll_label.setPlainText(self.tr("Across: ") + roll_angle + "°")
        self.pitch_label.setPlainText(self.tr("Along: ") + pitch_angle + "°")
        self.update()

    def draw(self) -> None:
        """
        Draw the angle indicator with all its components.
        """
        # Set up scene dimensions
        self.setSceneRect(0, 0, self.width, self.height)
        self.setItemIndexMethod(QGraphicsScene.ItemIndexMethod.NoIndex)

        # Calculate layout parameters
        self.min_dim = min(self.width, self.height)
        text_height = self.height * 0.08
        bg_r = self.min_dim / 2 - 10 - text_height / 2
        self.x_center = self.width / 2
        self.y_center = self.height / 2 - text_height / 2
        self.bg_r = bg_r

        # Draw background
        bg_pen = QPen(QColor("#000000"), 2)
        bg_brush = QBrush(QColor("#000000"))
        d = 2 * bg_r
        self.bg = self.addEllipse(self.x_center - bg_r, self.y_center - bg_r, d, d, bg_pen, bg_brush)

        # Draw axes
        lines_pen = QPen(QColor(self.lines_color), 1, Qt.PenStyle.SolidLine)
        self.addLine(self.x_center - bg_r, self.y_center, self.x_center + bg_r, self.y_center, lines_pen)
        self.addLine(self.x_center, self.y_center - bg_r, self.x_center, self.y_center + bg_r, lines_pen)

        # Draw components
        self.draw_icon()
        self.draw_circles()
        self.draw_bubble()
        self.draw_angles_labels()
        
        # Center view
        if self.view:
            self.view.centerOn(self.x_center, self.y_center)

    def draw_circles(self) -> None:
        """Draw the concentric circles that form the background of the indicator."""
        r = 15
        delta = 30
        self.max_r = self.bg_r - 50
        lines_pen = QPen(QColor(self.lines_color), 1, Qt.PenStyle.SolidLine)

        if self.isBlinking():
            lines_pen = QPen(QColor(self._second_color), 3, Qt.PenStyle.SolidLine)

        while r < self.max_r:
            d = 2 * r
            left_x = self.x_center - r
            top_y = self.y_center - r
            self.addEllipse(left_x, top_y, d, d, lines_pen)
            r += delta

    def draw_bubble(self) -> None:
        """Draw the bubble that indicates the current tilt angle."""
        bubble_r = 10
        d = bubble_r * 2

        bubble_pen = QPen(QColor(self.bubble_color), 2)
        bubble_brush = QBrush(QColor(self.bubble_color))
        self.bubble = self.addEllipse(
            self.x_center - bubble_r, 
            self.y_center - bubble_r, 
            d, d, 
            bubble_pen, bubble_brush
        )

    def draw_icon(self) -> None:
        """Draw the mast icon and its associated text."""
        # Load and place icon
        icon_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "MastIcon.png")
        icon_size = int(0.21 * self.min_dim)
        pm = QPixmap(icon_file_path)
        pm = pm.scaledToWidth(icon_size)
        pm_item = self.addPixmap(pm)

        right_x = self.x_center + self.bg_r
        top_y = self.y_center - self.bg_r
        pm_item.setPos(right_x - icon_size, top_y)

        # Draw mast
        mast_pen = QPen(QColor("#FFFFFF"), 2)
        mast_brush = QBrush(QColor("#FFFFFF"))
        pm_center = pm_item.boundingRect().center()
        pm_center_coord = int(pm_center.x())
        self.pm_center_x = self.x_center + self.bg_r - pm_center_coord + 2
        self.pm_center_y = self.y_center - self.bg_r + pm_center_coord
        mast_w = int(icon_size * 0.4)
        mast_h = int(icon_size * 0.1)
        self.mast = self.addRect(
            self.pm_center_x,
            self.pm_center_y - mast_h / 2,
            mast_w, mast_h,
            mast_pen, mast_brush
        )

        # Draw text background
        mast_text_rect_pen = QPen(QColor("#000000"), 2)
        mast_text_rect_brush = QBrush(QColor("#000000"))
        mast_text_rect_x = right_x - icon_size
        mast_text_rect_y = top_y + icon_size - 10
        mast_text_rect_h = int(0.25 * icon_size)
        self.mast_text_rect = self.addRect(
            mast_text_rect_x + 1, 
            mast_text_rect_y, 
            icon_size - 2,
            mast_text_rect_h, 
            mast_text_rect_pen, 
            mast_text_rect_brush
        )

        # Set up text
        mast_text_rect_center = pm_item.boundingRect().center()
        self.mast_text_rect_x_center = mast_text_rect_x + mast_text_rect_center.x()
        self.mast_text_rect_y_center = mast_text_rect_y + mast_text_rect_h / 2
        self.mast_text_font_size = 12
        
        # Add initial text
        mast_text = "0.00"
        mast_text_font = QFont(RESOURCES_CONFIG["default_font_name"], self.mast_text_font_size)
        metrics = QFontMetrics(mast_text_font)
        font_w = metrics.horizontalAdvance(mast_text)
        font_h = metrics.height()
        mast_text_left_x = self.mast_text_rect_x_center - font_w / 2
        mast_text_top_y = self.mast_text_rect_y_center - font_h
        self.text = self.addText(mast_text, mast_text_font)
        self.text.setDefaultTextColor(Qt.GlobalColor.white)
        self.text.setPos(mast_text_left_x, mast_text_top_y)
        
        # Store metrics for later text updates
        self.font_h = font_h
        self.font_metrics = metrics

    def draw_angles_labels(self) -> None:
        """Draw the labels for pitch and roll angles."""
        font = QFont(RESOURCES_CONFIG["default_font_name"], self.label_font_size)
        font_metrics = QFontMetrics(font)

        # Draw pitch label
        label = self.tr("Along")
        font_width = font_metrics.horizontalAdvance(label)
        y = 0
        x = int(self.x_center - self.bg_r - 20)
        self.pitch_label = self.addText(label, font)
        self.pitch_label.setDefaultTextColor(Qt.GlobalColor.white)
        self.pitch_label.setPos(x, y)

        # Draw roll label
        label = self.tr('Across')
        font_width = font_metrics.horizontalAdvance(label)
        font_height = font_metrics.height()
        y = int(self.bg_r * 2 - font_height / 2 + 20)
        x = int(self.x_center + self.bg_r - font_width - 20)
        self.roll_label = self.addText(label, font)
        self.roll_label.setDefaultTextColor(Qt.GlobalColor.white)
        self.roll_label.setPos(x, y)


class AngleIndicator(ZenohWidget):
    """
    Widget that displays the angle indicator with real-time data updates.
    
    Uses subscription-based data updates from ZenohWidget base class.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """Initialize the AngleIndicator widget."""
        # Initialize base class
        super().__init__(core, *args, **kwargs)
        
        # Initialize instance variables
        self.frame = None
        self.view = None
        self.scene = None
        self.timer = None
        self.killed = False
        
        # Data storage
        self._values = {
            'pitch': 0.0,
            'roll': 0.0,
            'mast_angle': 0.0
        }
        
        # Connect signals
        self.data_updated.connect(self._on_data_updated)

    def prepare(self, width: int = 500, height: int = 500, x: int = 500, y: int = 800,
                label_font_size: int = 20, screen_num: int = 0, data_sources: Dict = None) -> None:
        """Prepare the widget with the specified parameters."""
        # Set up data sources in base class
        super().prepare(data_sources=data_sources)
        
        # Create and position main window
        self.frame = QMainWindow(parent=self.core.main_screen)
        x_pos = int((screen_num - 1) * self.core.screen_width + x)
        y_pos = int(y)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.frame.setGeometry(x_pos, y_pos, width, height)
        
        # Set up layout
        grid = QGridLayout()
        self.frame.setLayout(grid)
        
        # Create view
        self.view = QGraphicsView(self.frame)
        self.view.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.view.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.view.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.view.setStyleSheet("border: 0px solid black; background-color: transparent;")
        self.view.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.view.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.view.setFixedSize(width, height)
        self.view.setSceneRect(0, 0, width, height)
        grid.addWidget(self.view, 0, 0)
        
        # Create scene with reference to view
        self.scene = AngleIndicatorScene(self.core, width, height, label_font_size, self.view)
        self.view.setScene(self.scene)
        
        # Draw initial scene
        self.scene.draw()
        
        # Set up timer for auxiliary updates (checking restrictions mode)
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_scene)
        
    def start(self) -> None:
        """Start widget operation after preparation."""
        # Start subscriptions from ZenohWidget
        super().start()
        
        # Show widget and start auxiliary timer
        if self.frame:
            self.frame.show()
            self.timer.start(500)  # Update visual state every 500ms
        
        # Initialize display with current values
        self._update_display()

    def _on_data_updated(self, source_name: str, value: float) -> None:
        """Handle data updates from subscriptions."""
        if source_name in self._values:
            self._values[source_name] = value
            self._update_display()

    def _update_display(self) -> None:
        """Update the visual display with current data values."""
        if not self.scene:
            return
            
        # Get current values and update scene
        pitch = self._values['pitch']
        roll = self._values['roll']
        mast_angle = self._values['mast_angle']
        
        self.scene.update_bubble(roll, pitch)
        self.scene.update_mast(mast_angle - 90)
        self.scene.set_text(f"{mast_angle:.2f}", f"{pitch:.2f}", f"{roll:.2f}")

    def _update_scene(self) -> None:
        """Update visual state of the scene."""
        if self.scene:
            self.scene.update()  # This will trigger checking for restrictions mode

    def kill(self) -> None:
        """Clean up resources when widget is no longer needed."""
        if self.killed:
            return
            
        # Stop timer
        if self.timer and self.timer.isActive():
            self.timer.stop()
            
        # Clean up subscriptions
        self.cleanup()
        
        self.killed = True
