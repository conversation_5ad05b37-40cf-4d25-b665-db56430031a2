# Extracted class: Distance2HoleWidget

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import math
from typing import Dict, Any, Optional

from PyQt6 import QtCore
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QApplication

from gui_common import get_from_telemetry

class Distance2HoleWidget(object):
    """
    Widget for displaying the distance to the next hole.

    This widget shows the distance from the current position to the next hole location.
    """

    def __init__(self, core: Any, text: str, *args, **kwargs) -> None:
        """
        Initialize the Distance2HoleWidget.

        Args:
            core: The core application object
            text: The label text to display
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        self.core = core
        self.core_screen = core.main_screen
        self.killed = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)
        self.value = 0
        self.label_text = QApplication.translate("Distance2HoleWidget", text)

        # Will be initialized in prepare() or later
        self.frame: Optional[QWidget] = None
        self.label: Optional[QLabel] = None
        self.tower2center: Optional[float] = None

    def prepare(self, width: int = 500, height: int = 500, x: int = 500, y: int = 800,
                font_size: str = "30px", screen_num: int = 1) -> None:
        """
        Prepare the widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            font_size: Font size in CSS format (e.g., "30px")
            screen_num: Screen number (0 for sensor screen, 1+ for main screen)
        """
        if screen_num == 0:
            self.frame = QWidget(self.core.sensor_screen)
        else:
            self.frame = QWidget(self.core.main_screen)

        self.frame.setGeometry(0, 0, width, height)
        stack = QVBoxLayout()
        self.frame.setLayout(stack)
        self.frame.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.frame.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        if screen_num == 0:
            self.frame.setGeometry(x, y, width, height)
        else:
            self.frame.setGeometry(self.core.screen_width * (screen_num - 1) + x, y, width, height)

        self.label = QLabel()
        self.label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.label.setStyleSheet("font-size: %s;" % (font_size))
        self.update_text()
        stack.addWidget(self.label)
        self.tower2center = None
        self.get_from_veh()

    def update_text(self) -> None:
        """
        Update the label text with the current distance value.
        """
        if self.label:
            self.label.setText(f"{self.label_text}: {self.value:.2f} m")

    def start(self) -> None:
        """
        Show the widget and start the update timer.
        """
        if self.label and self.frame:
            self.label.show()
            self.frame.show()
            self.timer.start(100)  # 100ms interval for updates (reduced CPU usage)

    def update(self) -> None:
        """
        Update the distance to the next hole.

        This method is called periodically by the timer to calculate and display
        the current distance from the vehicle to the next hole location.
        """
        if self.tower2center is None:
            self.get_from_veh()

        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None and vehid in self.core.telemetry.keys() and self.core.next_hole_location is not None and self.tower2center is not None:
            posx = get_from_telemetry(self.core, "position_x")
            posy = get_from_telemetry(self.core, "position_y")
            yaw = get_from_telemetry(self.core, "yaw")
            posx += self.tower2center * math.cos(yaw)
            posy += self.tower2center * math.sin(yaw)
            holex = self.core.next_hole_location['x']
            holey = self.core.next_hole_location['y']
            dist = math.dist([posx, posy], [holex, holey])
            self.value = dist
            self.update_text()
            self.frame.show()
            self.label.show()
        else:
            self.frame.hide()
            self.label.hide()

    def get_from_veh(self) -> None:
        """
        Request the tower2center parameter from the vehicle.

        This method sends a request to the vehicle to get the distance from
        the tower to the center of the vehicle, which is needed for accurate
        distance calculations.
        """
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is not None:
            if 'get_params' in self.core.output_data[vehid].keys():
                pl = json.loads(self.core.output_data[vehid]['get_params'])
                pl.append("/Vehicle/geometry/tower2center")
                self.core.output_data[vehid]['get_params'] = json.dumps(pl)
            else:
                self.core.output_data[vehid]['get_params'] = json.dumps(["/Vehicle/geometry/tower2center"])

            if 'params' in self.core.telemetry[vehid].keys() and "/Vehicle/geometry/tower2center" in \
                    self.core.telemetry[vehid]['params'].keys():
                self.tower2center = self.core.telemetry[vehid]['params']["/Vehicle/geometry/tower2center"]
