from typing import Optional

from PyQt6 import QtCore
from PyQt6.QtCore import QPoint
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QGridLayout,
                             QDialog, QPushButton, QLabel)


class Form(QWidget):
    """A basic form widget with draggable functionality."""

    def __init__(self, title: str = 'Main Window', parent: Optional[QWidget] = None) -> None:
        """Initialize the form widget.

        Args:
            title: The window title
            parent: The parent widget
        """
        super().__init__(parent)

        self.oPos = self.pos()
        self.setFixedSize(800, 800)

        self.setWindowTitle(title)

    def mousePressEvent(self, event: QtCore.QEvent) -> None:
        """Handle mouse press events for dragging.

        Args:
            event: The mouse event
        """
        self.oPos = event.globalPos()

    def mouseMoveEvent(self, event: QtCore.QEvent) -> None:
        """Handle mouse move events for dragging.

        Args:
            event: The mouse event
        """
        delta = QPoint(int(event.globalPos().x() - self.oPos.x()), int(event.globalPos().y() - self.oPos.y()))
        self.move(int(self.x() + delta.x()), int(self.y() + delta.y()))
        self.oPos = event.globalPos()


class GridForm(QDialog):
    """A dialog with a grid layout and frameless window."""

    def __init__(self, title: str = 'Main Window', parent: Optional[QWidget] = None) -> None:
        """Initialize the grid form dialog.

        Args:
            title: The window title
            parent: The parent widget
        """
        super().__init__(parent)

        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        self.oPos = self.pos()
        self.setWindowTitle(title)

        top_layout = QHBoxLayout()
        top_layout.addStretch(1)

        self.main_layout = QGridLayout()
        self.main_layout.addLayout(top_layout, 0, 0, 1, 2)

        self.main_layout.setRowStretch(1, 1)
        self.main_layout.setRowStretch(2, 1)
        self.main_layout.setColumnStretch(0, 1)
        self.main_layout.setColumnStretch(1, 1)

        self.setLayout(self.main_layout)

    def add_widget(self, widget: QWidget, row: int, col: int) -> None:
        """Add a widget to the grid layout.

        Args:
            widget: The widget to add
            row: The row position
            col: The column position
        """
        self.main_layout.addWidget(widget, row, col)


class ButtonsUpdater(QtCore.QObject):
    """Thread-safe handler for button update events."""

    updated_enabled_status = QtCore.pyqtSignal(QPushButton, bool)
    updated_stylesheet = QtCore.pyqtSignal(QPushButton, str)

    def __init__(self) -> None:
        """Initialize the buttons updater."""
        super().__init__()
        self.connect()

    def connect(self) -> None:
        """Connect signals to handler slots."""
        self.updated_enabled_status.connect(
            self.set_enabled_status
        )
        self.updated_stylesheet.connect(
            self.set_stylesheet
        )

    @staticmethod
    def set_enabled_status(btn: QPushButton, status: bool) -> None:
        """Slot for button enabled status change signal.

        Args:
            btn: The button that sent the signal
            status: The enabled status to set
        """
        btn.setEnabled(status)

    @staticmethod
    def set_stylesheet(btn: QPushButton, stylesheet: str) -> None:
        """Slot for button stylesheet change signal.

        Args:
            btn: The button that sent the signal
            stylesheet: The stylesheet to apply
        """
        btn.setStyleSheet(stylesheet)

    def emit_enabled(self, btn: QPushButton, status: bool) -> None:
        """Emit signal to change button enabled status.

        Args:
            btn: The button to update
            status: The enabled status to set
        """
        self.updated_enabled_status.emit(btn, status)

    def emit_stylesheet(self, btn: QPushButton, stylesheet: str) -> None:
        """Emit signal to change button stylesheet.

        Args:
            btn: The button to update
            stylesheet: The stylesheet to apply
        """
        self.updated_stylesheet.emit(btn, stylesheet)


class LabelUpdater(QtCore.QObject):
    """Thread-safe handler for label update events."""

    updated_size = QtCore.pyqtSignal(QLabel, float, float)
    updated_position = QtCore.pyqtSignal(QLabel, float, float)
    updated_pixmap = QtCore.pyqtSignal(QLabel, QPixmap)
    updated_hide = QtCore.pyqtSignal(QLabel, bool)

    def __init__(self) -> None:
        """Initialize the label updater."""
        super().__init__()
        self.connect()

    def connect(self) -> None:
        """Connect signals to handler slots."""
        self.updated_size.connect(
            self.set_size
        )
        self.updated_position.connect(
            self.set_position
        )
        self.updated_pixmap.connect(
            self.set_pixmap
        )
        self.updated_hide.connect(self.set_hide)

    @staticmethod
    def set_hide(lbl: QLabel, val: bool) -> None:
        """Set the hidden state of a label.

        Args:
            lbl: The label to update
            val: True to hide, False to show
        """
        lbl.setHidden(val)

    @staticmethod
    def set_size(lbl: QLabel, width: float, height: float) -> None:
        """Set the fixed size of a label.

        Args:
            lbl: The label to update
            width: The width to set
            height: The height to set
        """
        lbl.setFixedSize(width, height)

    @staticmethod
    def set_position(lbl: QLabel, x: float, y: float) -> None:
        """Set the position of a label.

        Args:
            lbl: The label to update
            x: The x-coordinate
            y: The y-coordinate
        """
        lbl.move(x, y)

    @staticmethod
    def set_pixmap(lbl: QLabel, pixmap: QPixmap) -> None:
        """Set the pixmap of a label.

        Args:
            lbl: The label to update
            pixmap: The pixmap to display
        """
        lbl.setPixmap(pixmap)

    def emit_size(self, lbl: QLabel, width: float, height: float) -> None:
        """Emit signal to change label size.

        Args:
            lbl: The label to update
            width: The width to set
            height: The height to set
        """
        self.updated_size.emit(lbl, width, height)

    def emit_position(self, lbl: QLabel, x: float, y: float) -> None:
        """Emit signal to change label position.

        Args:
            lbl: The label to update
            x: The x-coordinate
            y: The y-coordinate
        """
        self.updated_position.emit(lbl, x, y)

    def emit_pixmap(self, lbl: QLabel, pixmap: QPixmap) -> None:
        """Emit signal to change label pixmap.

        Args:
            lbl: The label to update
            pixmap: The pixmap to display
        """
        self.updated_pixmap.emit(lbl, pixmap)

    def emit_hide(self, lbl: QLabel, val: bool) -> None:
        """Emit signal to change label visibility.

        Args:
            lbl: The label to update
            val: True to hide, False to show
        """
        self.updated_hide.emit(lbl, val)


class WidgetUpdater(QtCore.QObject):
    """Thread-safe handler for widget update events."""

    updated_size = QtCore.pyqtSignal(QWidget, float, float)
    updated_position = QtCore.pyqtSignal(QWidget, float, float)
    updated_value = QtCore.pyqtSignal(QWidget, object)

    def __init__(self) -> None:
        """Initialize the widget updater."""
        super().__init__()
        self.connect()

    def connect(self) -> None:
        """Connect signals to handler slots."""
        self.updated_size.connect(
            self.set_size
        )
        self.updated_position.connect(
            self.set_position
        )
        self.updated_value.connect(
            self.set_value
        )

    @staticmethod
    def set_size(widget: QWidget, width: float, height: float) -> None:
        """Set the fixed size of a widget.

        Args:
            widget: The widget to update
            width: The width to set
            height: The height to set
        """
        widget.setFixedSize(width, height)

    @staticmethod
    def set_position(widget: QWidget, x: float, y: float) -> None:
        """Set the position of a widget.

        Args:
            widget: The widget to update
            x: The x-coordinate
            y: The y-coordinate
        """
        widget.move(x, y)

    @staticmethod
    def set_value(widget: QWidget, value: object) -> None:
        """Set the value of a widget.

        Args:
            widget: The widget to update
            value: The value to set
        """
        widget.setValue(value)

    def emit_size(self, widget: QWidget, width: float, height: float) -> None:
        """Emit signal to change widget size.

        Args:
            widget: The widget to update
            width: The width to set
            height: The height to set
        """
        self.updated_size.emit(widget, width, height)

    def emit_position(self, widget: QWidget, x: float, y: float) -> None:
        """Emit signal to change widget position.

        Args:
            widget: The widget to update
            x: The x-coordinate
            y: The y-coordinate
        """
        self.updated_position.emit(widget, x, y)

    def emit_value(self, widget: QWidget, value: object) -> None:
        """Emit signal to change widget value.

        Args:
            widget: The widget to update
            value: The value to set
        """
        self.updated_value.emit(widget, value)

