#!/usr/bin/python3
# -*- coding: utf-8 -*-

import sys
import os
import json
import logging
import datetime
import time
import signal
from collections import OrderedDict
from importlib import import_module
from typing import List, Dict, Any, Optional

from PyQt6.QtWidgets import QApplication, QPushButton
from PyQt6.QtCore import QTranslator, QSystemSemaphore, QSharedMemory

from core import Core
from utils import load_json_config
from customClasses.darkPalette import darkPalette as DARK_PALETTE


class Languages:
    """ Available interface translation languages. """
    ENG = "eng"
    RU = "ru"
    ES = "es"


# Global variables
TRANSLATE_TO = Languages.ENG
MODULES_CONFIG = load_json_config("modules.json")
modules_global: List[Any] = []  # Store modules for cleanup during halt
shared_memory_global = None  # Store shared memory for cleanup

SHOW_MAIN_SCREEN = True
SHOW_SENSOR_SCREEN = True


def halt() -> None:
    """
    Safely terminate the application.

    This function handles platform-specific termination to ensure
    all processes are properly closed.
    """
    logging.info("halt event")
    try:
        # First attempt to gracefully shutdown all modules if they're in global scope
        global modules_global, shared_memory_global
        if 'modules_global' in globals() and modules_global:
            for module in modules_global:
                try:
                    if hasattr(module, 'kill'):
                        module.kill()
                except Exception as e:
                    logging.error(f"Error shutting down module {module.__class__.__name__}: {e}")
        
        # Clean up shared memory if it exists
        if shared_memory_global and shared_memory_global.isAttached():
            try:
                shared_memory_global.detach()
                logging.debug("Detached shared memory")
            except Exception as e:
                logging.error(f"Error detaching shared memory: {e}")

        if sys.platform == 'darwin':
            # macOS requires different process group handling
            os.kill(os.getpid(), signal.SIGTERM)
        else:
            os.killpg(0, signal.SIGKILL)
    except Exception as e:
        logging.error(f"Error during halt: {e}")
    finally:
        os._exit(0)

def is_app_already_running() -> bool:
    """
    Check if application is already running using platform-specific approach.

    Returns:
        bool: True if another instance is already running, False otherwise
    """
    app_id = 'zyfra_drillop'
    global shared_memory_global

    if sys.platform == 'darwin':
        # macOS: Use file lock approach
        lock_file = os.path.join(os.path.expanduser("~"), f".{app_id}.lock")

        # Check if lock file exists and process is running
        if os.path.exists(lock_file):
            try:
                with open(lock_file, 'r') as f:
                    pid = int(f.read().strip())

                # Check if process with pid exists
                try:
                    os.kill(pid, 0)  # Signal 0 doesn't kill but checks existence
                    return True  # Process exists, app is running
                except OSError:
                    # Process not running, remove stale lock file
                    os.remove(lock_file)
            except Exception as e:
                logging.error(f"Error checking lock file: {e}")
                # If any error, assume file is stale
                try:
                    os.remove(lock_file)
                except:
                    pass

        # Create new lock file
        try:
            with open(lock_file, 'w') as f:
                f.write(str(os.getpid()))

            # Register cleanup on exit
            import atexit
            atexit.register(lambda: os.remove(lock_file) if os.path.exists(lock_file) else None)
        except Exception as e:
            logging.error(f"Error creating lock file: {e}")

        return False
    else:
        # Linux/Windows: Use QSharedMemory approach
        shared_mem_id = 'zyfrasharedmem'

        try:
            # In Linux, shared memory not freed when app terminates abnormally
            if sys.platform != 'win32':
                fix_shared_mem = QSharedMemory(shared_mem_id)
                if fix_shared_mem.attach():
                    fix_shared_mem.detach()

            # Try to attach to existing shared memory
            shared_memory_global = QSharedMemory(shared_mem_id)
            if shared_memory_global.attach():
                shared_memory_global.detach()  # Detach to prevent leaks
                return True  # Already running

            # Create shared memory segment
            if not shared_memory_global.create(1):
                logging.error(f"Failed to create shared memory: {shared_memory_global.errorString()}")
                return False
                
            # Register cleanup on exit
            import atexit
            atexit.register(lambda: cleanup_shared_memory(shared_memory_global))
            return False
        except Exception as e:
            logging.error(f"Error checking shared memory: {e}")
            # Try to clean up if possible
            if shared_memory_global and shared_memory_global.isAttached():
                shared_memory_global.detach()
            return False  # Assume not running if error

def cleanup_shared_memory(sm):
    """Helper function to clean up shared memory"""
    if sm:
        try:
            if sm.isAttached():
                sm.detach()
                logging.debug("Detached shared memory in cleanup")
        except Exception as e:
            logging.error(f"Error detaching shared memory in cleanup: {e}")


def main() -> None:
    """
    Main application entry point.

    This function initializes the application, loads modules based on configuration,
    and starts the main event loop.
    """
    global MODULES_CONFIG, modules_global
    # Set the root logger level to DEBUG
    logging.Logger.setLevel(logging.getLogger(), logging.DEBUG)

    # Set the charset_normalizer logger level to WARNING to suppress DEBUG messages
    charset_normalizer_logger = logging.getLogger('charset_normalizer')
    charset_normalizer_logger.setLevel(logging.WARNING)

    # Initialize Qt application
    app = QApplication(sys.argv)
    app.setApplicationName("Zyfra Drill Operator App")

    # Check if application is already running
    if is_app_already_running():
        logging.debug('Application already running')
        halt()
        return

    # Normal process of creating & launching MainWindow
    dev_mode = h_mode = q_mode = False
    if len(sys.argv) >= 2:
        # Check arguments in priority order
        if 'dev_mode' in sys.argv[1:]:
            MODULES_CONFIG = load_json_config("develop_modules.json")
            dev_mode = True
        elif 'q_mode' in sys.argv[1:]:
            MODULES_CONFIG = load_json_config("modules_4k.json")
            q_mode = True
        elif 'h_mode' in sys.argv[1:]:
            MODULES_CONFIG = load_json_config("modules_2k.json")
            h_mode = True

    # Apply dark palette from customClasses/darkPalette
    app.setPalette(DARK_PALETTE)
    core = Core(dev_mode, h_mode, q_mode)

    modules: List[Any] = []
    modules_global = []  # Store modules in global variable for cleanup

    # Initialize translator
    src_path = os.path.dirname(os.path.abspath(__file__))
    translator = QTranslator()

    # Load appropriate translation file based on selected language
    translation_files = {
        Languages.ES: '/translations/es_ES.qm',
        Languages.RU: '/translations/ru_RU.qm',
        Languages.ENG: '/translations/en_EN.qm'
    }

    if TRANSLATE_TO in translation_files:
        translator.load(src_path + translation_files[TRANSLATE_TO])

    QApplication.installTranslator(translator)

    for module_id, args in MODULES_CONFIG.items():
        is_active = args.get('is_active')
        if not is_active:
            continue

        class_name = args.get('class_name')
        init_args = args.get('init_args')
        prep_args = args.get('prep_args')
        file_name = args.get('file_name')

        logging.debug(f"Preparing {class_name}")
        try:
            modules_file = import_module(name=file_name)
            module_class = getattr(modules_file, class_name)
            module = module_class(core=core, **init_args)
            module.prepare(**prep_args)
            logging.debug(f"Module {class_name} prepared")
            modules.append(module)
            modules_global.append(module)
        except Exception as e:
            logging.error(f"Error loading module {class_name}: {e}")

    for module in modules:
        try:
            module.start()
        except Exception as e:
            logging.error(f"Error starting module {module.__class__.__name__}: {e}")

    if SHOW_MAIN_SCREEN:
        core.main_screen.show()
    if SHOW_SENSOR_SCREEN:
        core.sensor_screen.show()

    exit_btn = QPushButton(core.sensor_screen)
    exit_btn.clicked.connect(halt)
    exit_btn.setText("Х")
    exit_btn.setFixedSize(int(40), int(40))
    if dev_mode:
        exit_btn.move(int(1450), int(867))
        core.main_screen.destroyed.connect(halt)
        core.sensor_screen.destroyed.connect(halt)
    else:
        exit_btn.move(int(1880), int(1005))
    exit_btn.show()

    # Ensure clean exit
    app.aboutToQuit.connect(halt)

    sys.exit(app.exec())


if __name__ == '__main__':
    if sys.platform != 'darwin':
        # Process group setting not fully supported on macOS
        os.setpgrp()
    main()
