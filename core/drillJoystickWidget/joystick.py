from hashlib import new
import os.path

from PyQt6 import Qt<PERSON>ore, QtGui, QtWidgets
from PyQt6.QtWidgets import (QTableWidgetItem, QWidget, QGroupBox, QSizePolicy, QMessageBox,
                             QBoxLayout, QVBoxLayout, QHBoxLayout, QPushButton, QApplication,
                             QLabel, QScrollArea, QScroller, QScrollerProperties, QStackedLayout,
                             QComboBox, QFrame, QGridLayout, QLineEdit, QCheckBox)
from PyQt6.QtCore import (Qt, QPoint, QPointF, QRect, QTimer, QPropertyAnimation,
                          QUrl, pyqtSignal, pyqtProperty, QSize, QMargins, QRectF)
from PyQt6.QtGui import (QPainter, QColor, QFont, QPixmap, QPalette,
                         QPen, QBrush, QIcon, QPainterPath, QPainterPathStroker, QImage)
# from PyQt6.QtMultimedia import QMediaPlayer, QMediaPlaylist, QMediaContent, QSound
import logging

from functools import partial
from customClasses.styledPushButton import StyledPushButton
from customClasses.styledCheckBox import StyledCheckBox
from .joystick_layouts import layouts as LAYOUTS
from customClasses.darkPalette import ColorSet

from customClasses.blinkBehaviour import BlinkingBehaviour

RESTRICTION_MODE = 'restriction_mode'
EASY_RESTRICTION_MODE = 'easy_restriction_mode'
RECOMMENDATION_MODE = 'recommendation_mode'

class DrillJoystickWidget(QWidget):
    """
    Widget shows feedback from drill joystick panel.
    Should be placed horizontally
    """
    joystickNonlinearityToggled = pyqtSignal(str, bool)

    def __init__(self, core, modes, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.core = core
        self.modes = modes
        # initialize properties

        # create UI
        self.initUI()
        self.buttonsList[0].click()
        print('text color is:')
        color = self.palette().color(QPalette.ColorRole.Text)

        self.joy_mode_timer = QTimer()
        self.joy_mode_timer.setInterval(50)  # Changed from 5ms to 50ms to reduce CPU usage

        self.joy_mode_timer.timeout.connect(self.setJoystickMode)
        self.joy_mode_timer.start(50)  # Changed from 5ms to 50ms to reduce CPU usage

    def initUI(self):
        # create layouts and widgets
        # WIDGETS:
        self.redButtonWidget = RoundButtonViewWithCaption(color=Qt.GlobalColor.red,
                                                          caption=self.tr("Red button"))
        self.redButtonWidget.setPressed(False)
        self.greenButtonWidget = RoundButtonViewWithCaption(color=Qt.GlobalColor.green,
                                                            caption=self.tr("Green button"))
        self.greenButtonWidget.setPressed(False)

        self.knobWidget = KnobViewWithCaption()
        self.knobWidget.setAccessibleName("pot_value")
        self.knobWidget.setKnobNonlinearity(
            self.core.getJoystickNonlinearity(
                self.knobWidget.accessibleName()
            )
        )

        # joysticks
        self.leftJoystick = JoystickWidget()
        self.leftJoystick.setAccessibleName("joystick1")
        self.leftJoystick.setJoystickNonlinearity(
            self.core.getJoystickNonlinearity(
                self.leftJoystick.accessibleName()
            )
        )

        self.centerJoystick = JoystickWidget()
        self.centerJoystick.setAccessibleName("joystick2")
        self.centerJoystick.setJoystickNonlinearity(
            self.core.getJoystickNonlinearity(
                self.centerJoystick.accessibleName()
            )
        )

        self.rightJoystick = JoystickWidget()
        self.rightJoystick.setAccessibleName("joystick3")
        self.rightJoystick.setJoystickNonlinearity(
            self.core.getJoystickNonlinearity(
                self.rightJoystick.accessibleName()
            )
        )

        self.tumbler = TumblerWidget()

        # names for buttons
        namesList = list(self.modes.keys())
        # empty list for button storage
        self.buttonsList = []
        for name in namesList:
            buttonName = self.modes[name]["name"]
            newButton = StyledPushButton(text=QApplication.translate("DrillJoystickWidget", buttonName), fontSize=18)  # Уменьшаем размер шрифта кнопок
            newButton.setAccessibleName(name)

            self.buttonsList.append(newButton)

        for button in self.buttonsList:
            # set buttons checkable
            button.setCheckable(True)
            # unckeck all buttons by-default
            button.setChecked(False)
            button.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                 QSizePolicy.Policy.MinimumExpanding)

            # debug TODO: fix theme!

        # set first button checked
        self.buttonsList[0].setChecked(True)

        # LAYOUTS
        mainLayout = QVBoxLayout()
        # buttons group box - stores buttons for different regimes
        buttonsPanel = QGroupBox()
        buttonsPanelLayout = QHBoxLayout()

        # widgets panel - stores widgets (widgets organized by horizontal slices)
        widgetsPanel = QGroupBox()
        widgetsPanelLayout = QHBoxLayout()

        # red-green buttons and knob layout - vertical storage for 2 widgets
        knobAndButtonsLayout = QVBoxLayout()

        # tumbler and empty space layout - have extra space for future
        tumblerAndSomethingLayout = QVBoxLayout()

        # PANELS AND PANELS LAYOUTS
        self.redAndGreenButtonsPanel = QGroupBox(self.tr("Red and Green Buttons"))
        redAndGreenButtonsPanelLayout = QHBoxLayout()

        self.knobPanel = QGroupBox(self.tr("Knob widget"))
        knobPanelLayout = QVBoxLayout()

        self.leftJoystickPanel = QGroupBox(self.tr("Left Joystick"))
        leftJoystickPanelLayout = QVBoxLayout()

        self.centerJoystickPanel = QGroupBox(self.tr("Center Joystick"))
        centerJoystickPanelLayout = QVBoxLayout()

        self.rightJoystickPanel = QGroupBox(self.tr("Right Joystick"))
        rightJoystickPanelLayout = QVBoxLayout()

        self.tumberPanel = QGroupBox(self.tr("Tumbler Widget"))
        tumblerPanelLayout = QVBoxLayout()

        # WIRING
        for button in self.buttonsList:
            button.clicked.connect(partial(self.buttonClick, button))

        for joystick in (self.leftJoystick, self.centerJoystick, self.rightJoystick):
            joystick.nonlinearToggleSwitch.toggled.connect(
                partial(self.sendNonlinearityChange, joystick.accessibleName()))
        self.knobWidget.nonlinearityToggleSwitch.toggled.connect(
            partial(self.sendNonlinearityChange, self.knobWidget.accessibleName())
        )
        # STYLING
        for panel in (self.tumberPanel, self.knobPanel,
                      self.redAndGreenButtonsPanel,
                      self.leftJoystickPanel,
                      self.centerJoystickPanel, self.rightJoystickPanel):
            panel.setStyleSheet("""
                                QGroupBox{
                                font-size: 28px;
                                border: 1px solid #444444;
                                border-radius: 3px;
                                margin-top: 45px; /* Отступ сверху для заголовка */
                                }
                                /* Стиль для заголовка */
                                QGroupBox::title{
                                subcontrol-origin: margin;
                                subcontrol-position: top center;
                                font-size: 28px;
                                font-weight: bold;
                                color: white; /* Цвет текста */
                                /* Убран фон заголовка */
                                }
                                """)

        for button in self.buttonsList:
            button.setColor(ColorSet.buttonRegularColor.value)
        # COMPOSING
        self.setLayout(mainLayout)

        mainLayout.addWidget(widgetsPanel)
        mainLayout.addWidget(buttonsPanel)
        mainLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        mainLayout.setSpacing(0)

        widgetsPanel.setLayout(widgetsPanelLayout)
        buttonsPanel.setLayout(buttonsPanelLayout)

        for button in self.buttonsList:
            buttonsPanelLayout.addWidget(button)

        # widget panel expanding as long as could
        # buttons panel has minimum height
        widgetsPanel.setSizePolicy(
            QSizePolicy.Policy.MinimumExpanding,
            QSizePolicy.Policy.MinimumExpanding
        )
        buttonsPanel.setSizePolicy(
            QSizePolicy.Policy.MinimumExpanding,
            QSizePolicy.Policy.Minimum
        )

        buttonsPanelLayout.setContentsMargins(QMargins(1, 1, 1, 1))
        buttonsPanelLayout.setSpacing(5)
        buttonsPanel.setFlat(False)  # border
        buttonsPanel.setFixedHeight(55)  # Высота кнопок еще больше
        buttonsPanel.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444444;
                border-radius: 3px;
                margin-top: 10px; /* Отступ сверху */
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                font-size: 28px; /* Увеличенный размер шрифта */
                font-weight: bold; /* Жирный шрифт */
                color: white; /* Цвет текста */
            }
        """)

        widgetsPanelLayout.setContentsMargins(QMargins(
            2, 2, 2, 5
        ))  # Reduced bottom margin to tighten spacing between widgets and separator
        widgetsPanelLayout.addLayout(knobAndButtonsLayout)
        widgetsPanelLayout.addWidget(self.leftJoystickPanel)
        widgetsPanelLayout.addWidget(self.centerJoystickPanel)
        widgetsPanelLayout.addWidget(self.rightJoystickPanel)
        widgetsPanelLayout.addLayout(tumblerAndSomethingLayout)

        knobAndButtonsLayout.addWidget(self.redAndGreenButtonsPanel)
        knobAndButtonsLayout.addStretch(1)
        knobAndButtonsLayout.addWidget(self.knobPanel)
        knobAndButtonsLayout.addStretch()

        knobAndButtonsLayout.setContentsMargins(QMargins(
            0, 0, 0, 0
        ))

        self.knobPanel.setLayout(knobPanelLayout)
        knobPanelLayout.addWidget(self.knobWidget)

        self.redAndGreenButtonsPanel.setLayout(redAndGreenButtonsPanelLayout)

        redAndGreenButtonsPanelLayout.addWidget(self.greenButtonWidget)
        redAndGreenButtonsPanelLayout.addWidget(self.redButtonWidget)

        self.leftJoystickPanel.setLayout(leftJoystickPanelLayout)
        leftJoystickPanelLayout.addWidget(self.leftJoystick)

        self.centerJoystickPanel.setLayout(centerJoystickPanelLayout)
        centerJoystickPanelLayout.addWidget(self.centerJoystick)

        self.rightJoystickPanel.setLayout(rightJoystickPanelLayout)
        rightJoystickPanelLayout.addWidget(self.rightJoystick)

        tumblerAndSomethingLayout.addWidget(self.tumberPanel, 4)
        tumblerAndSomethingLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        tumblerAndSomethingLayout.setSpacing(0)
        tumblerAndSomethingLayout.addStretch(2)
        tumblerAndSomethingLayout.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.tumberPanel.setLayout(tumblerPanelLayout)
        tumblerPanelLayout.addWidget(self.tumbler)

    def sendNonlinearityChange(self, sender, value):
        # Convert Qt.CheckState to boolean if needed
        if isinstance(value, Qt.CheckState):
            value = (value == Qt.CheckState.Checked)
        self.joystickNonlinearityToggled.emit(sender, value)

    def resizeEvent(self, e):
        # set fixed width for widgets (yeee, it's hard p*rn)
        # Устанавливаем одинаковую ширину для всех панелей
        panelWidth = int(self.width() / 5)
        self.redAndGreenButtonsPanel.setFixedWidth(panelWidth)
        self.knobPanel.setFixedWidth(panelWidth)  # Выравниваем knob widget по ширине с верхними кнопками
        self.leftJoystickPanel.setFixedWidth(panelWidth)
        self.centerJoystickPanel.setFixedWidth(panelWidth)
        self.rightJoystickPanel.setFixedWidth(panelWidth)
        self.tumberPanel.setFixedWidth(panelWidth - 10)

    def sizeHint(self) -> QtCore.QSize:
        return QSize(int(600), int(470))

    def buttonClick(self, sender):
        for button in self.buttonsList:
            if sender.accessibleName() == button.accessibleName():
                # check button
                button.setChecked(True)
                # change layouts
                self.setJoystickLayout(button.accessibleName())
                pass
            else:
                button.setChecked(False)

    def setJoystickLayout(self, layoutName):
        # set core' layout name
        self.core.current_layout_name = layoutName
        # rename all captions
        newLayout = self.modes[layoutName]

        self.leftJoystick.setCaptions(top=QApplication.translate("DrillJoystickWidget", newLayout['joy_left_top']),
                                      bottom=QApplication.translate("DrillJoystickWidget", newLayout['joy_left_bottom']))
        self.leftJoystickPanel.setTitle(QApplication.translate("DrillJoystickWidget", newLayout['joy_left']))

        # center joystick
        self.centerJoystick.setCaptions(top=QApplication.translate("DrillJoystickWidget", newLayout['joy_mid_top']),
                                        bottom=QApplication.translate("DrillJoystickWidget", newLayout['joy_mid_bottom']))
        self.centerJoystickPanel.setTitle(QApplication.translate("DrillJoystickWidget", newLayout['joy_mid']))

        # right joystick
        self.rightJoystick.setCaptions(top=QApplication.translate("DrillJoystickWidget", newLayout['joy_right_top']),
                                       bottom=QApplication.translate("DrillJoystickWidget", newLayout['joy_right_bottom']))
        self.rightJoystickPanel.setTitle(QApplication.translate("DrillJoystickWidget", newLayout['joy_right']))

        # buttons
        self.redAndGreenButtonsPanel.setTitle(QApplication.translate("DrillJoystickWidget", newLayout['buttons']))
        self.redButtonWidget.setCaption(QApplication.translate("DrillJoystickWidget", newLayout['red_button_name']))
        self.greenButtonWidget.setCaption(QApplication.translate("DrillJoystickWidget", newLayout['green_button_name']))

        # knob
        self.knobPanel.setTitle(QApplication.translate("DrillJoystickWidget", newLayout['knob']))

        # tumbler
        self.tumberPanel.setTitle(QApplication.translate("DrillJoystickWidget", newLayout['tumbler']))
        self.tumbler.setCaptions(top=QApplication.translate("DrillJoystickWidget", newLayout['tumbler_top']),
                                 mid=QApplication.translate("DrillJoystickWidget", newLayout['tumbler_mid']),
                                 bottom=QApplication.translate("DrillJoystickWidget", newLayout['tumbler_bottom']))

    def setPanelsCaption(self, joystick1, joystick2,
                         joystick3, button=None, tumbler=None, knob=None):
        """
        Set captions for widgets panels when changing layouts

        @param button: Caption for buttons panel, no change if not given
        @param joystick1: Caption for left joystck
        @param joystick2: Caption for middle joystick
        @param joystick3: Caption for right joystick
        @param tumbler: Caption for tumbler panel, no change if not given
        @param knob: Caption for knob panel, no change if not given
        """
        self.leftJoystickPanel.setTitle(joystick1)
        self.centerJoystickPanel.setTitle(joystick2)
        self.rightJoystickPanel.setTitle(joystick3)
        if button:
            self.redAndGreenButtonsPanel.setTitle(button)
        if knob:
            self.knobPanel.setTitle(knob)
        if tumbler:
            self.tumberPanel.setTitle(tumbler)

    def setButtons(self, state):
        if state == -1:
            self.redButtonWidget.setPressed(True)
            self.greenButtonWidget.setPressed(False)
            pass
        elif state == 0:
            self.redButtonWidget.setPressed(False)
            self.greenButtonWidget.setPressed(False)
        elif state == 1:
            self.redButtonWidget.setPressed(False)
            self.greenButtonWidget.setPressed(True)
        else:
            raise ValueError('GreenAndRed buttons state should be -1, 0 or 1, but {} given'.format(
                state
            ))

    def setKnobValue(self, value):
        if (value <= 100) and (value >= 0):
            self.knobWidget.setValue(value)
        else:
            raise ValueError('Knob widget value should be between 0 and 100, but {} was given'.format(
                value
            ))

    def setJoystick(self, index, value, locked=False):
        if (value <= 100) and (value >= -100):
            if index == 1:
                self.leftJoystick.setValue(value)
                self.leftJoystick.setLocked(locked)
            elif index == 2:
                self.centerJoystick.setValue(value)
                self.centerJoystick.setLocked(locked)
            elif index == 3:
                self.rightJoystick.setValue(value)
                self.rightJoystick.setLocked(locked)
            else:
                raise ValueError('Joystick index should be 1, 2 or 3, but {} was given'.format(
                    index
                ))
        else:
            raise ValueError("Joystick value should be between -100 and 100, but {} was given".format(
                value
            ))

    def setTumbler(self, value):
        if value in (-1, 0, 1):
            self.tumbler.setValue(value)
        else:
            raise ValueError("Tumbler position sould be 0, 1 or -1, but {} was given".format(
                value
            ))

    def setValues(self, knob=None, joy1=None, joy2=None, joy3=None, tumbler=None, but1=None, but2=None):
        """ Set values of all controls """
        if knob is not None:
            # self.leftJoystick.setValue(knob)
            self.knobWidget.setValue(knob)

    def setJoystickMode(self):
        self.leftJoystick.setMode(self.core.joy_mode.get('joystick1'))
        self.centerJoystick.setMode(self.core.joy_mode.get('joystick2'))
        self.rightJoystick.setMode(self.core.joy_mode.get('joystick3'))


class RoundButtonViewWithCaption(QWidget):
    """
    Countainer for Color Button View Widget with given caption.
    retranslate 'press' and 'release' action to button
    """

    def __init__(self, color=Qt.GlobalColor.gray, caption="", *args, **kwargs):
        super().__init__(*args, **kwargs)
        # create layut and widgets
        self._color = color
        self._caption = caption

        self.initUI()

    def initUI(self):
        mainLayout = QVBoxLayout()
        # button:
        self.button = RoundButtonView(color=self._color)
        # caption
        self.caption = QLabel(self._caption)

        # wiring

        # styling
        self.caption.setAlignment(Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignBottom)
        _font = QFont()
        _font.setPixelSize(16)
        self.caption.setFont(_font)
        self.button.setMinimumHeight(60)

        # composing
        self.setLayout(mainLayout)
        mainLayout.setContentsMargins(QMargins(1, 1, 1, 1, ))

        mainLayout.addWidget(self.caption, 2)
        mainLayout.addWidget(self.button, 3)

        self.caption.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                   QSizePolicy.Policy.Minimum)
        self.button.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                  QSizePolicy.Policy.MinimumExpanding)

    def setCaption(self, text):
        self.caption.setText(text)

    def setColor(self, color):
        self.button.setColor(color)
        self._color = color

    def setPressed(self, value):
        self.button.setPressed(value)


class RoundButtonView(QWidget):
    """
    Create round button representative with given color.
    Can be 'pressed' or 'released' by corresponding slots
    """

    def __init__(self, color=Qt.GlobalColor.gray, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._color = QColor(color)
        self._pressed = False
        self._dimmed = False

        self._blinkTimer = QTimer()
        self._blinkTimer.setInterval(200)
        self._blinkTimer.timeout.connect(self.toggleDimmed)
        if self._pressed:
            self._blinkTimer.start()

    def sizeHint(self):
        return QSize(int(60), int(60))

    def setPressed(self, value):
        self._pressed = value
        # if value:
        #     self._blinkTimer.start()
        # else:
        #     self._blinkTimer.stop()
        #     self._dimmed = False
        self.update()

    def isPressed(self):
        return self._pressed

    def setColor(self, color):
        self._color = QColor(color)

    def toggleDimmed(self):
        self._dimmed = not self._dimmed
        self.update()

    def paintEvent(self, a0: QtGui.QPaintEvent) -> None:
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        brushColor = self._color
        if self._dimmed:
            brushColor = brushColor.darker(300)
        if not self._pressed:
            brushColor = brushColor.darker(300)
        _brush = QBrush(brushColor, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        painter.setPen(Qt.PenStyle.NoPen)

        side = min(int(self.width()), int(self.height())) * 0.9
        painter.translate(int(self.width()) / 2, int(self.height()) / 2)
        painter.drawEllipse(int(-side / 2), int(-side / 2), int(side), int(side))


class KnobViewWithCaption(QWidget):
    """
    Container for round knob view widget with given data
    for displaying and measurement units.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._color = Qt.GlobalColor.lightGray

        self.initUI()

    def initUI(self):
        # create layouts
        mainLayout = QVBoxLayout()

        mainLayout.setContentsMargins(QMargins(2, 2, 2, 2))
        knobAndLabelLayout = QHBoxLayout()
        knobAndLabelLayout.setContentsMargins(QMargins(0, 0, 0, 0))

        # create widgets
        self.label = QLabel(text='')
        self.knob = KnobWidget()
        self.nonlinearityToggleSwitch = StyledCheckBox(text=self.tr("Nonlinear", "Toggle Switch caption"))
        self.nonlinearityToggleSwitch.setChecked(False)

        # wiring

        # styling
        self.label.setAlignment(Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter)
        _font = QFont()
        _font.setPixelSize(22)
        _font.setBold(True)
        self.label.setFont(_font)

        self.nonlinearityToggleSwitch.setFontSize(15)
        self.nonlinearityToggleSwitch.setColor(ColorSet.buttonRegularColor.value)

        # composing
        self.setLayout(mainLayout)
        mainLayout.addLayout(knobAndLabelLayout, 5)
        mainLayout.addWidget(self.nonlinearityToggleSwitch, 2)

        knobAndLabelLayout.addWidget(self.label, 2)
        knobAndLabelLayout.addWidget(self.knob, 2)

    def setKnobNonlinearity(self, value):
        self.nonlinearityToggleSwitch.setChecked(value)

    def setValue(self, value):
        if value is not None:
            self.knob.setValue(value)
            self.label.setText(str(round(value)) + "%")


class KnobWidget(QWidget):
    """
    Round knob allow to repeat physical knob on joystick panel
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._color = Qt.GlobalColor.lightGray
        self._value = 0

    def setValue(self, value):
        if value >= 0 and value <= 100:
            self._value = value
            self.update()
        else:
            raise ValueError("Knob value should be less than 100 and greater than 0, but {} was given".format(
                value
            ))

    def sizeHint(self):
        return QSize(int(90), int(90))

    def resizeEvent(self, a0: QtGui.QResizeEvent) -> None:
        self._knobRadius = min(int(self.width()), int(self.height())) * 0.3

    def paintEvent(self, a0: QtGui.QPaintEvent) -> None:
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        painter.translate(int(self.width()) / 2, int(self.height()) / 2)
        # draw knob itself
        _brush = QBrush(self._color, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        side = min(int(self.width()), int(self.height())) * 0.9
        painter.drawEllipse(int(-self._knobRadius), int(-self._knobRadius), int(self._knobRadius * 2), int(self._knobRadius * 2))

        # draw mark on the knob
        # _penColor = self.palette().color(QPalette.Background)
        # _penColor = ColorSet.buttonRegularColor.value  # TODO: fix theme
        _penColor = ColorSet.buttonOrangeColor.value.darker(120)
        _pen = QPen(_penColor, 2, Qt.PenStyle.SolidLine)
        painter.setPen(_pen)
        angle = self._value * 1.2 - 105
        painter.rotate(angle * 2)
        painter.drawLine(int(self._knobRadius - 2), 0, int(self._knobRadius - 12), 0)
        painter.rotate(-angle * 2)

        # draw marks outside the knob
        _penColor = self.palette().color(QPalette.ColorRole.Text)
        _pen = QPen(_penColor, 1, Qt.PenStyle.SolidLine)
        painter.setPen(_pen)
        painter.drawArc(QRectF(float(-self._knobRadius * 1.2), float(-self._knobRadius * 1.2), float(self._knobRadius * 2.4), float(self._knobRadius * 2.4)),
                        -30 * 16, 240 * 16)
        # draw little marks:
        for angle in range(-210, 40, 15):
            painter.rotate(angle)
            if (angle + 30) % 60 == 0:
                length = self._knobRadius + 13
            else:
                length = self._knobRadius + 10
            painter.drawLine(int(self._knobRadius * 1.2 + 0.5), 0, int(length), 0)
            painter.rotate(-angle)


class JoystickWidget(QWidget, BlinkingBehaviour):
    """
    Joystick state widget contains Joy track, current value
    and saved value, axis captions and 'nonlinear' checkbox
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._locked = False
        self.initUI()

        self._color = self.palette().color(QPalette.ColorRole.Text)
        self._blinkColor = ColorSet.warningRedBlinkingColor.value

    def initUI(self):
        # create layouts
        mainLayout = QVBoxLayout()
        centerLayout = QHBoxLayout()

        # create widgets
        self.topLabel = QLabel(self.tr("Top Action"))
        self.bottomLabel = QLabel(self.tr("Bottom Action"))
        self.valueLabel = QLabel(self.tr("0.0%"))


        self.joystickWidget = JoystickViewWidget()

        self.nonlinearToggleSwitch = StyledCheckBox(text=self.tr("Nonlinear"))
        self.nonlinearToggleSwitch.setChecked(False)

        # wiring

        # styling
        # style labels
        _font = QFont()
        _font.setPixelSize(21)
        _font.setBold(True)
        for label in (self.topLabel, self.bottomLabel):
            # apply alignment to labels
            label.setAlignment(Qt.AlignmentFlag.AlignRight)
            # create and apply new font style to labels
            label.setFont(_font)
        # style value label separately
        self.valueLabel.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignHCenter)
        _font.setPixelSize(24)
        self.valueLabel.setFont(_font)
        # style checkbox
        self.nonlinearToggleSwitch.setFontSize(15)
        self.nonlinearToggleSwitch.setColor(ColorSet.buttonRegularColor.value)

        self.joystickWidget.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.MinimumExpanding)
        self.valueLabel.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                      QSizePolicy.Policy.MinimumExpanding)

        # composing
        self.setLayout(mainLayout)

        mainLayout.setContentsMargins(QMargins(2, 2, 2, 2))
        mainLayout.addWidget(self.topLabel)
        mainLayout.addLayout(centerLayout)
        mainLayout.addWidget(self.bottomLabel)
        mainLayout.addWidget(self.nonlinearToggleSwitch)

        centerLayout.addWidget(self.valueLabel)
        centerLayout.addWidget(self.joystickWidget)

    def setJoystickNonlinearity(self, value):
        self.nonlinearToggleSwitch.setChecked(value)

    def setLocked(self, value):
        self._locked = value
        self.joystickWidget.setLocked(value)

    def setValue(self, value):
        if value is not None:
            self.joystickWidget.setValue(value)
            self.valueLabel.setText(str(round(value)) + "%")
            if self.joystickWidget.viewMode == RESTRICTION_MODE:
                self.valueLabel.setStyleSheet("color: %s" % str(self._color.name()))
                self.setBlinking(True)
            else:
                self.valueLabel.setStyleSheet("color: %s" % str(self._color.name()))
                self.setBlinking(False)


    def setCaptions(self, top, bottom):
        """
        Set captions for joystick widget

        @param top: caption for joystick action when press
        @param bottom: caption for joystick action when pull
        """
        self.topLabel.setText(top)
        self.bottomLabel.setText(bottom)

    def setMode(self, mode):
        self.joystickWidget.setViewMode(mode)

    # def setRecommendationMode(self, mode):
    #     self.joystickWidget.set_recommendation_mode(mode)

    # def setLightlyRestrictedMode(self, mode):
    #     self.joystickWidget.set_lightly_restricted_mode(mode)


class JoystickViewWidget(QWidget, BlinkingBehaviour):
    """
    Visual representation for physical joystick with track,
    thumb and marks
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # geometric constants
        self._trackWidth = 22
        self._thumbRadius = 14
        self._value = 0
        self._locked = False
        self.viewMode = None
        self.counter = 0
        self._color = self.palette().color(QPalette.ColorRole.Text)
        self._second_color = ColorSet.buttonOrangeColor.value.darker(120)
        self._blinkColor = ColorSet.warningRedBlinkingColor.value.darker(100)

    def setValue(self, value):
        if value >= -100 and value <= 100:
            self._value = value
        else:
            raise ValueError("Joystick input value should be between 0 and 100, {} was given".format(value))
        self.update()

    def sizeHint(self):
        return QSize(int(52), int(165))

    def resizeEvent(self, e):
        pass

    def setLocked(self, value):
        self._locked = value
        self.update()

    def setViewMode(self, mode):
        self.viewMode = mode

    def paintEvent(self, a0: QtGui.QPaintEvent) -> None:

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)

        # create brush for track
        painter.translate(int(self.width()) / 2, 0)
        # _brushColor = self.palette().color(QPalette.ColorRole.Text)
        if not self.viewMode:
            _brushColor = self.palette().color(QPalette.ColorRole.Text)
            _brushColor.setAlpha(60)
        else:
            _brushColor = self._color
            _brushColor.setAlpha(200)
        _brush = QBrush(_brushColor, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        painter.setPen(Qt.PenStyle.NoPen)
        # draw track
        trackRect = QRectF(float(-self._trackWidth / 2), float(0), float(self._trackWidth), float(int(self.height())))
        painter.drawRoundedRect(trackRect, self._trackWidth / 2, self._trackWidth / 2)

        # draw thumb
        thumbPos = QPointF(0, (int(self.height()) / 2 - self._thumbRadius) * self._value / 100)
        painter.translate(0, int(self.height()) / 2)

        if not self.viewMode:
            _brushColor = ColorSet.buttonOrangeColor.value.darker(120)
            _penColor = self.palette().color(QPalette.ColorRole.Text)
            _penColor.setAlpha(30)
        else:
            _brushColor = self._second_color
            _penColor = self._second_color
            _penColor.setAlpha(200)

        _brush.setColor(_brushColor)
        painter.setBrush(_brush)
        _pen = QPen(_penColor, 1, Qt.PenStyle.SolidLine)
        painter.setPen(_pen)
        painter.drawEllipse(thumbPos, int(self._thumbRadius), int(self._thumbRadius))

        # outer ring
        if not self.viewMode:
            _penColor = ColorSet.buttonOrangeColor.value.darker(200)
            _penColor.setAlpha(150)
        else:
            _brushColor = self._second_color
            _penColor = self._second_color
            _penColor.setAlpha(200)

        _pen.setColor(_penColor)
        painter.setPen(_pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawEllipse(thumbPos, int(self._thumbRadius), int(self._thumbRadius))

        if self.viewMode == RECOMMENDATION_MODE:
            self.setBlinkColor(ColorSet.buttonGreenColor.value.darker(100))
            self.setBlinking(True)
        elif self.viewMode == RESTRICTION_MODE:
            self.setBlinkColor(ColorSet.warningRedBlinkingColor.value.darker(100))
            self.setBlinking(True)
        elif self.viewMode == EASY_RESTRICTION_MODE:
            self.setBlinkColor(ColorSet.buttonOrangeColor.value.darker(100))
            self.setBlinking(True)
        elif not self.viewMode:
            self.setBlinking(False)

        # draw thumb icon if needed
        if self._locked == True:
            img_folder = os.path.abspath(__package__)
            img_path = os.path.join(img_folder, "lock.png")
            image = QImage(img_path)
            painter.translate(thumbPos)
            painter.drawImage(QRectF(float(-int(image.width()) / 2), float(-int(image.height()) / 2), float(int(image.width())), float(int(image.height()))),
                              image)
            painter.translate(-thumbPos)


class TumblerWidget(QWidget):
    """
    Container for tumbler view widget and captions
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._value = 0
        self.initUI()

    def initUI(self):
        # create layouts
        mainLayout = QHBoxLayout()
        captionsLayout = QVBoxLayout()

        # create widgets
        self.topLabel = QLabel(self.tr('Tumbler On'))
        self.midLabel = QLabel(self.tr('Tumbler in the Middle'))
        self.bottomLabel = QLabel(self.tr('Tumbler Off'))

        self.tumblerWidget = TumblerViewWidget()
        self.tumblerWidget.setFixedWidth(20)
        self.tumblerWidget.setValue(1)

        # wiring

        # styling
        labelFont = QFont()
        labelFont.setPixelSize(21)

        for label in (self.topLabel, self.midLabel, self.bottomLabel):
            label.setFont(labelFont)
            label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

        # composing
        self.setLayout(mainLayout)
        mainLayout.addLayout(captionsLayout)
        mainLayout.addWidget(self.tumblerWidget)

        captionsLayout.addWidget(self.topLabel)
        captionsLayout.addWidget(self.midLabel)
        captionsLayout.addWidget(self.bottomLabel)

    def setValue(self, value):
        self._value = value
        self.tumblerWidget.setValue(value)

    def setCaptions(self, top, mid, bottom):
        self.topLabel.setText(top)
        self.midLabel.setText(mid)
        self.bottomLabel.setText(bottom)


class TumblerViewWidget(QWidget):
    """
    Tumbler view widget. Has slots for defining it's state
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._position = 0

    def sizeHint(self):
        return QSize(int(15), int(100))

    def resizeEvent(self, a0: QtGui.QResizeEvent) -> None:
        self.thumbRadius = int(self.width())
        self.trackWidth = int(self.width()) / 2

    def setValue(self, value):
        if value in (0, -1, 1):
            self._position = value
            self.update()
        else:
            raise ValueError("Tumbler Position should be -1, 0 or 1 only")

    def paintEvent(self, a0: QtGui.QPaintEvent) -> None:
        # setup painter
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        # move to the center top point of canvas
        painter.translate(int(self.width()) / 2, 0)
        # setup colors for track drawing
        brushColor = self.palette().color(QPalette.ColorRole.Button).lighter()

        _brush = QBrush(brushColor, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        painter.setPen(Qt.PenStyle.NoPen)
        # draw track
        painter.drawRoundedRect(QRectF(float(-self.trackWidth / 2), float(0), float(self.trackWidth), float(int(self.height()))),
                                self.trackWidth / 2, self.trackWidth / 2)

        # find a center point to draw thumb
        startY = int(self.height()) / 2
        delta = (int(self.height()) / 2 - self.thumbRadius / 2)

        # draw guide dots
        dotsColor = self.palette().color(QPalette.ColorRole.Window)
        dotsColor.setAlpha(100)
        _pen = QPen(dotsColor, 3, Qt.PenStyle.SolidLine)
        painter.setPen(_pen)
        for i in (-1, 0, 1):
            painter.drawPoint(QPointF(0, startY + delta * i))

        # setup color for thumb drawing
        # brushColor = self.palette().color(QPalette.ColorRole.Highlight)
        brushColor = ColorSet.buttonOrangeColor.value.darker(120)
        _brush.setColor(brushColor)
        painter.setBrush(_brush)
        painter.setPen(Qt.PenStyle.NoPen)

        painter.drawEllipse(QPointF(0, int(startY + delta * (-self._position))), int(self.width()) / 2, int(self.width()) / 2)
