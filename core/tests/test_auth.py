
from core.auth import UserRoles, ResponseTypes, Auth


def test_auth():
    # В базе данных ХАЛа не должно быть пользователя-администратора для
    # успешного завершения теста.

    # Успешная регистрации администратора
    auth = Auth(hal_ip='http://localhost:5000')

    resp = auth.signup(
        username="admin",
        password="123456",
        role=UserRoles.ADMIN
    )
    assert bool(resp) == True
    assert type(resp.value) == str

    # Ошибка при повторной регистрации администратора
    resp = auth.signup(
        username="admin",
        password="123456",
        role=UserRoles.ADMIN
    )
    assert bool(resp) == False
    assert resp.type == ResponseTypes.ADMIN_EXISTS_ERROR

    # Успешная аутентификация администратора
    resp = auth.login(
        username="admin",
        password="123456",
    )
    assert bool(resp) == True
    assert type(resp.value) == dict
    assert resp.value.get('access_token') is not None
    assert resp.value.get('access_token') == auth.cur_access_jwt
    assert resp.value.get('refresh_token') is not None
    assert resp.value.get('refresh_token') == auth.cur_refresh_jwt
    assert auth.cur_user == "admin"

    # Успешная регистрации пользователя
    resp = auth.signup(
        username="test_user",
        password="123456",
    )
    assert bool(resp) == True
    assert type(resp.value) == str

    # Ошибка при попытке повторной регистрации пользователя
    resp = auth.signup(
        username="test_user",
        password="123456",
    )
    assert bool(resp) == False
    assert resp.type == ResponseTypes.USER_EXISTS_ERROR

    # Успешное обновление токена доступа
    old_jwt = auth.cur_access_jwt
    resp = auth.refresh_token()
    assert bool(resp) == True
    assert type(resp.value) == str
    assert auth.cur_access_jwt != old_jwt

    # Успешная инвалидация токенов
    resp1 = auth.logout_access()
    resp2 = auth.logout_refresh()
    assert bool(resp1) == True
    assert bool(resp2) == True

    assert auth.cur_access_jwt is None
    assert auth.cur_refresh_jwt is None
