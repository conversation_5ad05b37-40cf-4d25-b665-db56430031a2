#!/usr/bin/env python
# -*- coding: utf-8 -*-

from collections import namedtuple

from core import core
from core.joystick_adapter import JoystickAdapter
from core.utils import load_json_config


_TestCase = namedtuple("TestCase", ["data", "layout_name", "expected"])


def test_parse_control_data():
    jsa_config = load_json_config("modules.json")["joystick_adapter"]
    init_args = jsa_config["init_args"]
    prep_args = jsa_config["prep_args"]

    jsa = JoystickAdapter(core=core.Core, **init_args)
    jsa.prepare(**prep_args)

    test_cases = [
        _TestCase(
            data="1A010111111111101110.150.080.082.970.002.470.00FF",
            layout_name="driving",
            expected={'cat': {'right_track': -1.0, 'left_track': -0.0}}
        ),
        _TestCase(
            data="1A010101111111101110.150.080.082.970.002.470.00FF",
            layout_name="drilling",
            expected={'compressor': {'turn_off': 0, 'turn_on': 0},
                      'drill': {'rotation_speed': -0.0, 'feed_pressure': 37.1959},
                      'arm': {'move_arm': 0.0}}
        ),
        _TestCase(
            data="1A010111111111101110.150.080.082.970.002.470.00FF",
            layout_name="leveling",
            expected={'leveler': {'right_jack': -1.0, 'left_jack': -0.0, 'rear_jack': -0.0}}
        ),
        _TestCase(
            data="1A01001111111110110.150.080.082.970.002.471.24FF",
            layout_name="tower_control",
            expected={'tower': {'inclined_pin': -0.0, 'vertical_pin': -1.0, 'tower_tilt': 0.502, },
                      'arm': {'move_arm': -1}}
        ),
        _TestCase(
            data="1A010111111111101110.150.080.082.970.002.470.00FF",
            layout_name="buildup",
            expected={'tower': {'inclined_pin': -0.0, 'tower_tilt': -0.0, 'vertical_pin': -1.0},
                      'arm': {'move_arm': -0.0}}
        ),
        _TestCase(
            data="1A010111111111101110.150.080.082.970.002.470.00FF",
            layout_name="carousel_control",
            expected={'carousel': {'move_carousel': -1.0, 'turn_carousel': 1.0},
                      'drill': {'rotation_speed': -0.0, 'feed_speed': -1.0}}
        )
    ]

    for tc in test_cases:
        result = jsa.generate_controls(tc.data, tc.layout_name)
        expected = tc.expected
        assert expected == result


if __name__ == "__main__":
    test_parse_control_data()
