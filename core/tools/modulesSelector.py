# This tool should be useful for
# Switching modules on and off
# without spending all your work day with draining willpower

# Zyfra Robotics
# <PERSON>, 2021

import sys
import os
import json
import logging
from functools import partial

from collections import OrderedDict
from importlib import import_module

from PyQt6.QtWidgets import (QApplication, QCheckBox, QHBoxLayout, QLabel, QMainWindow, QPushButton, QSizePolicy, QTextEdit, QToolButton,
                            QVBoxLayout, QWidget, QFileDialog, QLineEdit, QFormLayout, QScrollArea)
from PyQt6.QtCore import pyqtSignal, QTranslator, QPoint, QRect, QSize, QTimer, Qt

class ModuleSelector(QMainWindow):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.settings = None
        self.fileName = None
        self.setWindowTitle('Modules Configurator')
        # self.setMinimumWidth(400)
        self.setBaseSize(300, 800)

        self.initUI()
        self.show()
        self.centralize()

    def centralize(self):
        self.move(QPoint(int(2560*1.5-int(self.width())/2),
                         int(1440/2-int(self.height())/2)))

    def initUI(self):
        print('will be initialized')
        topLevel = QWidget(self)
        self.setCentralWidget(topLevel)
        mainLayout = QVBoxLayout()
        testLabel = QLabel("Test test test")
        topLevel.setLayout(mainLayout)

        elementsScrollWrapper = QWidget()

        elementsScrollArea = QScrollArea()
        elementsScrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        elementsScrollArea.setWidgetResizable(True)
        elementsScrollArea.setWidget(elementsScrollWrapper)


        self.elementsLayout = QFormLayout()
        elementsScrollWrapper.setLayout(self.elementsLayout)
        # elementsScrollWrapper.setFixedHeight(900)


        fileLayout = QHBoxLayout()
        self.pathTextEdit = QLineEdit()
        self.pathTextEdit.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                        QSizePolicy.Policy.Minimum)

        self.curdir = os.getcwd()
        self.pathTextEdit.setText(self.curdir)

        openFileButton = QPushButton(text='Open...')
        self.saveFileButton = QPushButton(text="Save Changes")
        self.saveFileButton.setDisabled(True)
        self.saveFileButton.clicked.connect(self.writeConfig)

        openFileButton.setSizePolicy(QSizePolicy.Policy.Minimum,
                                        QSizePolicy.Policy.Minimum)

        openFileButton.pressed.connect(self.openFile)

        fileLayout.addWidget(self.pathTextEdit)
        fileLayout.addWidget(openFileButton)
        fileLayout.addWidget(self.saveFileButton)

        mainLayout.addLayout(fileLayout)
        mainLayout.addWidget(testLabel)
        mainLayout.addWidget(elementsScrollArea)

        QTimer.singleShot(1, self.quickOpen)

    def quickOpen(self):
        self.fileName = os.path.join(self.curdir, 'modules.json')
        self.readConfig()

    def openFile(self):
        openFileDialog = QFileDialog()
        openFileDialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        newFileName = openFileDialog.getOpenFileName(self,
                        'Open Config',
                        self.curdir,
                        "Config file (*.json)")

        print(newFileName)
        if newFileName[0]:
            self.fileName = newFileName[0]
            self.pathTextEdit.setText(self.fileName)

            self.readConfig()

    def readConfig(self):
        if self.fileName:
            with open(self.fileName, 'r') as file:
                self.settings = json.load(file)

            if self.settings:
                print('Succefully read config file')
                fileName = self.fileName.split('/')[-1]
                self.setWindowTitle(f"{self.windowTitle()} [{fileName}]")
                self.pathTextEdit.setText(self.fileName)
                self.buildConfigView()

    def writeConfig(self):
        if self.settings and self.elements:
            for topKey in self.settings.keys():
                activeness = self.settings[topKey].get('is_active')
                if activeness is not None:
                    self.settings[topKey]['is_active']=self.elements[topKey].isChecked()
        jsonString = json.dumps(self.settings, sort_keys=False, indent=4)
        with open(self.fileName, 'w') as fileToWrite:
            fileToWrite.write(jsonString)
        self.saveFileButton.setDisabled(True)

    def buildConfigView(self):
        self.elements = {}
        for topKey in self.settings.keys():
            label = QLabel(text=topKey)
            checkBox = QCheckBox()
            activeness = self.settings[topKey].get('is_active')
            if activeness:
                checkBox.setChecked(True)
            self.elements[topKey] = checkBox
            self.elementsLayout.addRow(label, checkBox)


        for element in self.elements.keys():
            # Connect checkbox toggled signal - in PyQt6 it sends a boolean value directly
            self.elements[element].toggled.connect(lambda checked, btn=self.saveFileButton:
                btn.setDisabled(False))


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ModuleSelector()

    app.exec()

