#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Standard library imports
import logging
from typing import Dict, List, Any, Optional, Union

# Third-party imports
import requests
from PyQt6 import QtCore
from PyQt6.QtCore import Qt, QTimer, QDateTime
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import (QApplication, QWidget, QLabel, QPushButton,
                             QComboBox, QPlainTextEdit, QDateTimeEdit, QGroupBox,
                             QCheckBox, QLineEdit, QFormLayout, QHBoxLayout,
                             QDialog, QMessageBox, QDialogButtonBox)

# Local imports
from customClasses.smartButton import SmartButton
from customClasses.darkPalette import ColorSet

# Configure logger
logger = logging.getLogger(__name__)

class DowntimesFormDialog(QDialog):
    """
    Dialog for creating and editing downtime records.

    This dialog allows users to create new downtime records or edit existing ones,
    including setting start and end times, downtime type, and comments.
    """

    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("Downtime Edit"))
        self.setMinimumSize(800, 600)

        self.downtime_id_field = QLabel(self)
        self.vehicle_label = QLabel(self)
        self.downtime_type_combobox = QComboBox(self)
        self.comment_field = QPlainTextEdit(self)
        self.start_time_field = QDateTimeEdit(self)
        self.end_time_groupbox = QGroupBox(self.tr("End time"), self)
        self.end_time_groupbox.setEnabled(False)
        self.end_time_checkbox = QCheckBox(self.tr("I want to finish downtime record"), self)
        self.end_time_checkbox.setChecked(False)

        self.end_time_field = QDateTimeEdit(self.end_time_groupbox)
        self.auto_end_time_field = QLineEdit(self.end_time_groupbox)
        self.auto_end_time_field.setDisabled(True)
        self.end_time_message = QLabel(self.end_time_groupbox)
        self.end_time_message.setStyleSheet("color: red;")
        self.end_time_message.hide()

        self.auto_end_time_apply_btn = QPushButton(self.end_time_groupbox, text=self.tr('Apply auto end time'))
        self.set_current_time_btn = QPushButton(self.end_time_groupbox, text=self.tr('Set Current Time'))

        self.ok_button = QPushButton(self.tr("Save downtime"), self)
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Cancel, self)
        self.button_box.addButton(self.ok_button, QDialogButtonBox.ButtonRole.AcceptRole)

        # Layout
        end_time_layout = QFormLayout()
        end_time_layout.addRow(self.tr("End time"), self.end_time_field)
        end_time_layout.addRow(self.end_time_message)
        end_time_layout.addRow(self.tr("Auto end time"), self.auto_end_time_field)
        end_buttons_layout = QHBoxLayout()
        end_buttons_layout.addWidget(self.auto_end_time_apply_btn)
        end_buttons_layout.addWidget(self.set_current_time_btn)
        end_time_layout.addRow(end_buttons_layout)
        self.end_time_groupbox.setLayout(end_time_layout)

        layout = QFormLayout(self)
        layout.addRow(self.tr("Downtime ID"), self.downtime_id_field)
        layout.addRow(self.tr("Vehicle ID"), self.vehicle_label)
        layout.addRow(self.tr("Type"), self.downtime_type_combobox)
        layout.addRow(self.tr("Description"), self.comment_field)
        layout.addRow(self.tr("Start time"), self.start_time_field)
        layout.addRow(self.end_time_checkbox)
        layout.addRow(self.end_time_groupbox)
        layout.addWidget(self.button_box)

        # Signals
        self.auto_end_time_apply_btn.clicked.connect(self.apply_auto_end_time)
        self.set_current_time_btn.clicked.connect(self.set_current_time)
        self.button_box.accepted.connect(self.accept_pressed)
        self.button_box.rejected.connect(self.reject_pressed)
        self.end_time_checkbox.stateChanged.connect(self.toggle_end_time)
        self.end_time_field.dateTimeChanged.connect(self.validate_end_time)

    def apply_auto_end_time(self) -> None:
        """
        Apply the auto-suggested end time to the end time field.

        Reads the auto end time from the field, validates it, and applies it
        to the end time field if valid.
        """
        recommended_str = self.auto_end_time_field.text().strip()
        if recommended_str and recommended_str != self.tr("Not available"):
            recommended_time = QDateTime.fromString(recommended_str, "yyyy-MM-dd HH:mm:ss")
            if recommended_time.isValid():
                self.end_time_field.setDateTime(recommended_time)
                self.validate_end_time(recommended_time)

    def set_current_time(self) -> None:
        """
        Set the end time field to the current date and time.

        Updates the end time field with the current system time and validates it.
        """
        current_time = QDateTime.currentDateTime()
        self.end_time_field.setDateTime(current_time)
        self.validate_end_time(current_time)

    def validate_end_time(self, end_time: QDateTime) -> None:
        """
        Validate the end time against the start time and current time.

        Checks if the end time is valid (after start time and before current time)
        and updates the UI accordingly.

        Args:
            end_time: The end time to validate
        """
        start_time = self.start_time_field.dateTime()
        current_time = QDateTime.currentDateTime()
        if self.end_time_checkbox.isChecked():
            if end_time <= start_time or end_time > current_time:
                self.ok_button.setDisabled(True)
                self.end_time_message.setText(self.tr("End time must be later than start time and before the current time."))
                self.end_time_message.show()
            else:
                self.ok_button.setEnabled(True)
                self.end_time_message.hide()
        else:
            self.end_time_message.hide()
            self.ok_button.setEnabled(True)

    def toggle_end_time(self, checked: Union[bool, Qt.CheckState]) -> None:
        """
        Toggle the end time fields based on the checkbox state.

        Enables or disables the end time fields and updates the button text
        based on whether the user wants to finish the downtime record.

        Args:
            checked: The state of the checkbox (can be boolean or Qt.CheckState)
        """
        # Convert Qt.CheckState to boolean if needed
        # In PyQt6, Qt.CheckState.Checked is 2, not True
        is_checked = checked
        if isinstance(checked, Qt.CheckState):
            is_checked = (checked == Qt.CheckState.Checked)

        self.end_time_groupbox.setEnabled(is_checked)
        self.end_time_field.setEnabled(is_checked)
        self.auto_end_time_field.setEnabled(is_checked)
        self.set_current_time_btn.setEnabled(is_checked)

        if is_checked:
            self.ok_button.setText(self.tr("Close downtime record"))
        else:
            self.ok_button.setText(self.tr("Save downtime"))

        # Validate the current end time
        self.validate_end_time(self.end_time_field.dateTime())

    def accept_pressed(self) -> None:
        """
        Handle the accept button press.

        Accepts the dialog, which will close it with an Accepted result.
        """
        self.accept()

    def reject_pressed(self) -> None:
        """
        Handle the reject button press.

        Rejects the dialog, which will close it with a Rejected result.
        """
        self.reject()

    def set_mode(self, mode: str = 'create') -> None:
        """
        Set the dialog mode (create or edit).

        Configures the dialog based on whether we're creating a new downtime
        record or editing an existing one.

        Args:
            mode: The mode to set ('create' or 'edit')
        """
        # Currently both modes have the same behavior, but this allows for
        # future differentiation between create and edit modes
        _ = mode  # Acknowledge the parameter to avoid IDE warnings
        self.end_time_checkbox.setChecked(False)
        self.ok_button.setText(self.tr("Save downtime"))

    def set_mode_create(self) -> None:
        """
        Set the dialog to create mode.

        This is a convenience method that calls set_mode('create').
        """
        self.set_mode('create')

    def set_mode_edit(self) -> None:
        """
        Set the dialog to edit mode.

        This is a convenience method that calls set_mode('edit').
        """
        self.set_mode('edit')

    def load_downtime_types(self, downtime_types: Optional[List[Any]]) -> None:
        """
        Load downtime types into the combo box.

        Populates the downtime type combo box with the available downtime types.

        Args:
            downtime_types: List of downtime types from the server
        """
        self.downtime_type_combobox.clear()
        self.downtime_type_combobox.addItem(self.tr("Not set"), None)

        for downtime_type in downtime_types or []:
            if len(downtime_type) >= 3:
                # Add the downtime type to the combo box
                self.downtime_type_combobox.addItem(
                    QApplication.translate("DowntimesTypes", downtime_type[1] + '_description'),
                    downtime_type[0]
                )

                # Set tooltip for the item
                self.downtime_type_combobox.setItemData(
                    self.downtime_type_combobox.count()-1,
                    QApplication.translate("DowntimesTypes", downtime_type[1]),
                    role=QtCore.Qt.ItemDataRole.ToolTipRole
                )

    def reset_form_fields(self) -> None:
        """
        Reset all form fields to their default values.

        Clears all input fields and resets the form to its initial state.
        """
        self.end_time_groupbox.setEnabled(False)
        self.end_time_field.clear()
        self.auto_end_time_field.clear()
        self.downtime_id_field.clear()
        self.vehicle_label.clear()

        if self.downtime_type_combobox.count() > 0:
            self.downtime_type_combobox.setCurrentIndex(0)

        self.comment_field.clear()
        self.start_time_field.clear()
        self.end_time_checkbox.setChecked(False)
        self.end_time_message.hide()
        self.ok_button.setEnabled(True)

    def fill_form_with_downtime_data(self, downtime: Optional[Dict[str, Any]]) -> None:
        """
        Fill the form with data from a downtime record.

        Populates all form fields with data from the provided downtime record.

        Args:
            downtime: Dictionary containing downtime data from the server
        """
        if downtime is None:
            return

        # Set downtime ID
        if 'id' in downtime:
            self.downtime_id_field.setText(str(downtime.get('id')))
        else:
            self.downtime_id_field.clear()

        # Set downtime type
        downtime_type_id = downtime.get('downtime_type')
        if downtime_type_id:
            idx = self.downtime_type_combobox.findData(downtime_type_id)
            if idx >= 0:
                self.downtime_type_combobox.setCurrentIndex(idx)
            else:
                self.downtime_type_combobox.setCurrentIndex(0)
        else:
            self.downtime_type_combobox.setCurrentIndex(0)

        # Set comment
        self.comment_field.setPlainText(downtime.get('comment', ''))

        # Set start time
        start_str = downtime.get('start_time', '')
        start_dt = QDateTime.fromString(start_str, "yyyy-MM-dd HH:mm:ss")
        if start_dt.isValid():
            self.start_time_field.setDateTime(start_dt)
        else:
            self.start_time_field.setDateTime(QDateTime.currentDateTime())

        # Set auto end time if available
        if downtime.get('auto_end_time'):
            self.auto_end_time_field.setText(downtime['auto_end_time'])
            self.auto_end_time_apply_btn.setEnabled(True)
            self.auto_end_time_apply_btn.show()
        else:
            self.auto_end_time_field.setText(self.tr("Not available"))
            self.auto_end_time_apply_btn.setEnabled(False)
            self.auto_end_time_apply_btn.hide()

    def get_form_data(self) -> Dict[str, Any]:
        """
        Get the form data as a dictionary.

        Collects all form field values into a dictionary suitable for sending to the server.

        Returns:
            Dictionary containing the form data
        """
        current_downtime_type_id = self.downtime_type_combobox.currentData()
        data = {
            "downtime_type": current_downtime_type_id,
            "comment": self.comment_field.toPlainText(),
            "start_time": self.start_time_field.dateTime().toString("yyyy-MM-dd HH:mm:ss")
        }

        if self.end_time_checkbox.isChecked():
            data["end_time"] = self.end_time_field.dateTime().toString("yyyy-MM-dd HH:mm:ss")
        return data

    def get_downtime_id(self) -> str:
        """
        Get the downtime ID from the form.

        Returns:
            The downtime ID as a string
        """
        return self.downtime_id_field.text().strip()

    def get_end_time_checkbox_state(self) -> bool:
        """
        Get the state of the end time checkbox.

        Returns:
            True if the checkbox is checked, False otherwise
        """
        return self.end_time_checkbox.isChecked()


class DowntimesButton(SmartButton):
    """
    Button for creating and managing downtime records.

    This button allows users to create new downtime records or edit existing ones.
    It also provides visual indicators for active downtimes.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_indicator_default()

        self.widget = QWidget(self.core_screen)
        self.widget.setStyleSheet("""
            * {
                font-size: 22px;
                min-height: 30px;
                margin: 10px;
                padding: 5px;
            }
            QComboBox QAbstractItemView {
                font-size: 22px;
                min-height: 30px;
            }
            QPushButton {
                min-height: 40px;
            }
            QCheckBox {
                font-size: 22px;
            }
            QCheckBox::indicator {
                width: 35px;
                height: 35px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid white;
            }
        """)

        self.all_vehicles = None
        self.downtime_types = None
        self.last_downtime_response = None
        self.is_editing = False
        self.current_mode = 'create'
        self.initial_downtime_id = None
        
        # Check if server is available
        self.server_available = self.check_server_available()

        if self.server_available:
            self.load_downtime_types()
            self.load_vehicles()

            self.timer = QTimer()
            self.timer.setInterval(1000)
            self.timer.timeout.connect(self.check_downtimes)
            self.timer.start()
            self.check_downtimes()
        else:
            # Disable functionality if server is not available
            self.setText(self.tr("HAL Server Unavailable"))
            self.setEnabled(False)
            logger.warning("HAL server is unavailable, downtime functionality is disabled")
    
    def check_server_available(self) -> bool:
        """
        Check if the HAL server is available.
        
        Returns:
            True if the server is available, False otherwise
        """
        try:
            # Just try to connect, don't wait for a response
            response = requests.head(
                self.core.config['hal_ip'],
                timeout=1.0
            )
            return True
        except requests.RequestException:
            logger.warning(f"HAL server is not available at {self.core.config['hal_ip']}")
            return False

    def get_active_vehicle_id(self) -> Optional[str]:
        """
        Get the ID of the currently active vehicle.

        Returns whichever vehid is currently active, or None if none can be found.

        Returns:
            The ID of the active vehicle, or None if no vehicle is active
        """
        if self.core.in_rc_vehid is not None:
            return self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            return self.core.watched_vehid
        elif self.core.selected_vehid is not None:
            return self.core.selected_vehid
        return None

    def buttonClickAction(self) -> None:
        """
        Handle button click to create or update a downtime record.

        This method is called when the user clicks the button to create a new
        downtime record or edit an existing one.
        """
        logger.info("Create/Update downtimes pressed")

        # Check if user is authenticated
        if not self.core.auth.cur_access_jwt:
            QMessageBox.warning(self, self.tr('Authorization Needed'),
                                self.tr('You need to log in to perform this action.'),
                                QMessageBox.StandardButton.Ok)
            return

        # Get the active vehicle ID
        active_vehid_key = self.get_active_vehicle_id()
        if not active_vehid_key:
            logger.debug('No vehicle selected or available.')
            return

        # Safety check that telemetry has the key we need
        if active_vehid_key not in self.core.telemetry:
            logger.debug(f'No telemetry data found for vehicle "{active_vehid_key}".')
            return

        self.is_editing = True

        # Determine if we're creating a new downtime or editing an existing one
        if (not self.last_downtime_response or
            not self.last_downtime_response.get('start_time') or
            self.last_downtime_response.get('end_time')):
            self.current_mode = 'create'
            self.initial_downtime_id = None
        else:
            self.current_mode = 'edit'
            self.initial_downtime_id = self.last_downtime_response.get('id')

        self.show_form_dialog()

    def show_form_dialog(self) -> None:
        """
        Show the downtime form dialog.

        Creates and configures the downtime form dialog based on the current mode
        and available data.
        """
        self.dialog = DowntimesFormDialog(self.widget)

        # Load downtime types if available
        if self.downtime_types is not None:
            self.dialog.load_downtime_types(self.downtime_types)

        self.dialog.reset_form_fields()

        # Set the vehicle label
        active_vehid_key = self.get_active_vehicle_id()
        if not active_vehid_key:
            self.dialog.vehicle_label.setText(self.tr("None"))
        else:
            self.dialog.vehicle_label.setText(str(active_vehid_key))

        # Configure the dialog based on the current mode
        if self.current_mode == 'create':
            self.dialog.set_mode_create()
            self.dialog.start_time_field.setDateTime(QDateTime.currentDateTime())
        else:
            self.dialog.set_mode_edit()
            if self.last_downtime_response:
                self.dialog.fill_form_with_downtime_data(self.last_downtime_response)

        # Connect our own handlers to the dialog buttons
        self.dialog.button_box.accepted.disconnect(self.dialog.accept_pressed)
        self.dialog.button_box.accepted.connect(self.ok_pressed)  # connect our own handler
        self.dialog.button_box.rejected.disconnect(self.dialog.reject_pressed)
        self.dialog.button_box.rejected.connect(self.cancel_pressed)

        # Use exec() instead of show() to make it modal
        self.dialog.exec()

    def ok_pressed(self) -> None:
        """
        Handle the OK button press in the downtime form dialog.

        Validates the form data, checks for conflicts, and saves the downtime record
        to the server.
        """
        active_vehid_key = self.get_active_vehicle_id()
        if not active_vehid_key or active_vehid_key not in self.core.telemetry:
            QMessageBox.warning(self.dialog, self.tr('Vehicle Error'),
                                self.tr('No valid vehicle to save downtime for.'),
                                QMessageBox.StandardButton.Ok)
            return

        selected_vehid = self.core.telemetry[active_vehid_key]['vehid']

        latest_response = self.get_last_downtime(selected_vehid)
        conflict_resolved = self.handle_conflicts(latest_response)
        if not conflict_resolved:
            # User chose "Yes" in create mode:
            # Now we have switched to edit mode and updated downtime_id in the same dialog instance.
            # The form is not closed or reset, user's input stays intact.
            QMessageBox.information(self.dialog, self.tr("Switched to New Downtime"),
                                    self.tr("A new active downtime was detected.\n"
                                            "Your entered comment and type are now associated with the new downtime.\n"
                                            "Please press 'Save downtime' again to finalize these changes."),
                                    QMessageBox.StandardButton.Ok)
            return  # Do not accept or close, user will press Save again

        request_data = self.dialog.get_form_data()
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {self.core.auth.cur_access_jwt}"
        }

        downtime_id = self.dialog.get_downtime_id()

        if self.current_mode == 'edit' and downtime_id:
            url = f"{self.core.config['hal_ip']}/api/downtimes/{downtime_id}"
            request_method = requests.put
        else:
            url = f"{self.core.config['hal_ip']}/api/downtimes"
            request_method = requests.post
            request_data["vehid"] = str(selected_vehid)

        try:
            response = request_method(url, headers=headers, json=request_data, timeout=2.0)
            if response.ok:
                self.finish_saving()
            else:
                logger.error(f"Failed to save downtime: {response.text}")
                QMessageBox.critical(self.dialog, self.tr("Error"), self.tr("Failed to save downtime. Please try again."),
                                    QMessageBox.StandardButton.Ok)
                # User can try again without closing dialog
        except requests.RequestException as e:
            logger.error(f"Network error during downtime save: {e}")
            QMessageBox.critical(self.dialog, self.tr("Network Error"),
                                self.tr("Failed to connect to the server. Please check your connection."),
                                QMessageBox.StandardButton.Ok)
        except Exception as e:
            logger.error(f"Error occurred during downtime save: {e}")
            QMessageBox.critical(self.dialog, self.tr("Error"), str(e),
                                QMessageBox.StandardButton.Ok)
        finally:
            self.is_editing = False
            self.current_mode = 'create'
            self.initial_downtime_id = None

    def finish_saving(self) -> None:
        """
        Finish the saving process and close the dialog.

        Resets the editing state, closes the dialog, and refreshes the downtimes.
        """
        self.is_editing = False
        self.current_mode = 'create'
        self.initial_downtime_id = None
        self.dialog.close()
        self.check_downtimes()

    def cancel_pressed(self) -> None:
        """
        Handle the cancel button press in the downtime form dialog.

        Resets the editing state, closes the dialog, and refreshes the downtimes.
        """
        self.is_editing = False
        self.current_mode = 'create'
        self.initial_downtime_id = None
        self.dialog.close()
        self.check_downtimes()

    def handle_conflicts(self, latest_response: Optional[Dict[str, Any]]) -> bool:
        """
        Handle conflicts between the current form data and the latest server data.

        Checks for conflicts between the downtime record being edited and the latest
        data from the server, and prompts the user to resolve them if necessary.

        Args:
            latest_response: The latest downtime record from the server

        Returns:
            True if conflicts were resolved or there were no conflicts,
            False if the user chose to reload data and the form should not be submitted yet
        """
        if self.current_mode == 'create':
            if latest_response and not latest_response.get('end_time'):
                # Conflict: a new active downtime appeared
                choice = QMessageBox.question(
                    self.dialog,
                    self.tr("Downtime Changed"),
                    self.tr("A new active downtime was created automatically while you were editing.\n\n"
                            "Do you want to edit the newly created downtime instead?\n\n"
                            "If you choose 'Yes', your currently entered comment and downtime type will now be "
                            "applied to the new downtime. The form will remain open with your input unchanged.\n"
                            "Press 'Save downtime' again to finalize.\n\n"
                            "If you choose 'No', we will attempt to create a new downtime anyway."),
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )
                if choice == QMessageBox.StandardButton.Yes:
                    new_id = latest_response.get('id')
                    self.current_mode = 'edit'
                    self.initial_downtime_id = new_id
                    self.dialog.downtime_id_field.setText(str(new_id))
                    return False
                # else proceed anyway
        elif self.current_mode == 'edit':
            if self.initial_downtime_id and latest_response and latest_response.get('id') != self.initial_downtime_id:
                choice = QMessageBox.question(
                    self.dialog,
                    self.tr("Downtime Changed"),
                    self.tr("The downtime you were editing has changed on the server.\n\n"
                            "Do you want to reload the latest downtime data?\n\n"
                            "If you choose 'Yes', your current inputs will be replaced with the server's data.\n"
                            "If you choose 'No', we will attempt to save your current changes anyway."),
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )
                if choice == QMessageBox.StandardButton.Yes:
                    self.dialog.fill_form_with_downtime_data(latest_response)
                    self.initial_downtime_id = latest_response.get('id')
                    return False
                # else proceed anyway
        return True

    def get_last_downtime(self, veh_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the last downtime record for a vehicle from the server.

        Args:
            veh_id: The vehicle ID to get the last downtime for

        Returns:
            The last downtime record as a dictionary, or None if no record was found
            or an error occurred
        """
        if self.core.hal_connection_status:
            try:
                response = requests.get(
                    f"{self.core.config['hal_ip']}/api/downtimes/last", 
                    params={"vehid": veh_id},
                    timeout=2.0  # Add a 2-second timeout
                )
                if response.ok:
                    return response.json()
                else:
                    logger.error(f"Error fetching last downtime: {response.status_code} - {response.text}; veh_id: {veh_id}")
            except requests.RequestException as e:
                logger.error(f"Network error while fetching last downtime: {e}")
            except Exception as e:
                logger.error(f"Exception while fetching last downtime: {e}")
        return None

    def check_downtimes(self) -> None:
        """
        Check for active downtimes and update the button state accordingly.

        This method is called periodically by the timer to check for active downtimes
        and update the button's appearance based on the current state.
        """
        # Load data if not already loaded
        if not self.all_vehicles:
            self.load_vehicles()
        if not self.downtime_types:
            self.load_downtime_types()

        # Get the active vehicle ID
        active_vehid_key = self.get_active_vehicle_id()
        if not active_vehid_key:
            self.setEnabled(False)
            self.set_indicator_default()
            return

        # If telemetry is missing the key, also disable
        if active_vehid_key not in self.core.telemetry:
            self.setEnabled(False)
            self.set_indicator_default()
            return

        veh_data = self.core.telemetry[active_vehid_key]

        if 'vehid' not in veh_data:
            self.setEnabled(False)
            self.set_indicator_default()
            logger.debug(f"Vehicle data missing 'vehid' key: {veh_data}")
            return

        # Enable the button and get the last downtime
        selected_vehid = veh_data['vehid']
        self.setEnabled(True)

        response = self.get_last_downtime(selected_vehid)
        self.last_downtime_response = response

        # Update the button appearance based on the downtime state
        if response is None:
            self.set_indicator_default()
        elif response.get('end_time'):
            self.set_indicator_default()
        elif response.get('auto_end_time'):
            self.set_indicator_red_blinking()
        else:
            self.set_indicator_yellow()

    def load_vehicles(self) -> None:
        """
        Load the list of vehicles from the server.

        Fetches the list of available vehicles from the server and stores it
        in the all_vehicles attribute.
        """
        try:
            response = requests.get(
                f"{self.core.config['hal_ip']}/api/vehicles",
                timeout=2.0
            )
            if response.ok:
                self.all_vehicles = response.json()
            else:
                logger.error(f"Failed to load vehicles: {response.status_code} - {response.text}")
                self.all_vehicles = []
        except requests.RequestException as e:
            logger.error(f"Failed to load vehicles due to connection error: {e}")
            self.all_vehicles = []

    def load_downtime_types(self) -> None:
        """
        Load the list of downtime types from the server.

        Fetches the list of available downtime types from the server and stores it
        in the downtime_types attribute.
        """
        try:
            response = requests.get(
                f"{self.core.config['hal_ip']}/api/downtime_types",
                timeout=2.0
            )
            if response.ok:
                self.downtime_types = response.json()
            else:
                logger.error(f"Failed to load downtime types: {response.status_code} - {response.text}")
                self.downtime_types = []
        except requests.RequestException as e:
            logger.error(f"Failed to load downtime types due to connection error: {e}")
            self.downtime_types = []

    def format_elapsed_time(self, start_time: QDateTime, end_time: QDateTime) -> str:
        """
        Format the elapsed time between two dates as a human-readable string.

        Args:
            start_time: The start time
            end_time: The end time

        Returns:
            A formatted string representing the elapsed time (e.g., "2 days, 3 hours, 45 minutes")
        """
        elapsed_time_seconds = int(start_time.secsTo(end_time))
        elapsed_days = elapsed_time_seconds // (24 * 60 * 60)
        elapsed_time_seconds %= (24 * 60 * 60)
        elapsed_hours = elapsed_time_seconds // (60 * 60)
        elapsed_time_seconds %= (60 * 60)
        elapsed_minutes = elapsed_time_seconds // 60
        elapsed_seconds = elapsed_time_seconds % 60

        elapsed_parts = []
        if elapsed_days > 0:
            elapsed_parts.append(f"{elapsed_days} {self.tr('days')}")
        if elapsed_hours > 0:
            elapsed_parts.append(f"{elapsed_hours} {self.tr('hours')}")
        if elapsed_minutes > 0:
            elapsed_parts.append(f"{elapsed_minutes} {self.tr('minutes')}")
        if elapsed_seconds > 0:
            elapsed_parts.append(f"{elapsed_seconds} {self.tr('seconds')}")

        # If no time has elapsed, return "0 seconds"
        if not elapsed_parts:
            return f"0 {self.tr('seconds')}"

        return ", ".join(elapsed_parts)

    def set_indicator_yellow(self) -> None:
        """
        Set the button indicator to yellow (active downtime).

        Updates the button appearance to indicate that there is an active downtime,
        including showing the elapsed time since the downtime started.
        """
        if self.last_downtime_response and self.last_downtime_response.get('start_time'):
            start_dt = QDateTime.fromString(self.last_downtime_response['start_time'], "yyyy-MM-dd HH:mm:ss")
            formatted_time = self.format_elapsed_time(start_dt, QDateTime.currentDateTime())
        else:
            formatted_time = ""

        self.setText(self.tr("Has active downtime!"))
        self.setSubCaption(formatted_time)
        self.setBlinking(False)
        self.setColor(QColor('orange'))
        self.show()
        self.update()

    def set_indicator_red_blinking(self) -> None:
        """
        Set the button indicator to blinking red (downtime to finish).

        Updates the button appearance to indicate that there is a downtime that
        needs to be finished, including showing the elapsed time since the downtime started.
        """
        if self.last_downtime_response and self.last_downtime_response.get('start_time'):
            start_dt = QDateTime.fromString(self.last_downtime_response['start_time'], "yyyy-MM-dd HH:mm:ss")
            formatted_time = self.format_elapsed_time(start_dt, QDateTime.currentDateTime())
        else:
            formatted_time = ""

        self.setText(self.tr("Has downtime to finish!"))
        self.setSubCaption(formatted_time)
        self.setBlinkColor(QColor("red"))
        self.setBlinking(True)
        self.show()
        self.update()

    def set_indicator_default(self) -> None:
        """
        Set the button indicator to the default state (no active downtime).

        Updates the button appearance to indicate that there is no active downtime
        and the button can be used to create a new downtime record.
        """
        self.setText(self.tr("Create Downtime"))
        self.setColor(ColorSet.buttonRegularColor.value)
        self.setSubCaption('')
        self.setBlinking(False)
        self.update()
