from PyQt6 import QtCore, QtGui, QtWidgets
from PyQt6.QtWidgets import (QTableWidgetItem, QWidget, QGroupBox, QSizePolicy, QMessageBox,
                             QBoxLayout, QVBoxLayout, QHBoxLayout, QPushButton, QApplication,
                             QLabel, QScrollArea, QScroller, QScrollerProperties, QStackedLayout,
                             QComboBox, QFrame, QGridLayout, QLineEdit, QCheckBox, QFormLayout,
                             QStyle, QDialog, QDialogButtonBox)
from PyQt6.QtCore import (Qt, QPoint, QPointF, QRect, QTimer, QPropertyAnimation,
                          QUrl, pyqtSignal, pyqtProperty, QSize, QMargins, QRectF)
from PyQt6.QtGui import (QPainter, QColor, QFont, QPixmap, QPalette,
                         QPen, QBrush, QIcon, QPainterPath, QPainterPathStroker, QImage)
import logging
from enum import Enum
import os
import json
import hashlib
from functools import partial
from .vehicleErrors import VehicleErrors
from .styledPushButton import StyledPushButton
from .styledCheckBox import StyledCheckBox, ToggleSlider
from .ledWithCaption import LedWithCaption
from .darkPalette import ColorSet, darkPalette
from .separator import Separator
from .blinkBehaviour import BlinkingBehaviour

class SmartButton(StyledPushButton):
    """ Smart Button is a regular iPlace button which can be included in project
        via modules.json and send data to core/vehicle """

    def __init__(self,*, core, text, vehicleParamDict=None,
                 paramName=None, fontSize=None, needConfirm=False,
                 needPassword=False, confirmationText=None, value=None, color=None, **kwargs):
        self.core = core
        self.core_screen = core.sensor_screen
        self.vehicleParamDict = vehicleParamDict
        self.paramName = paramName
        self.value = value
        self.needConfirm = needConfirm
        self.needPassword = needPassword

        self.updateTimer = QTimer()
        self.updateTimer.setInterval(500)
        self.updateTimer.timeout.connect(self.buttonTimerAction)

        self.confirmationText = confirmationText if confirmationText else  \
            QApplication.translate("SmartButton", "Please, confirm action for button:\n")+text
        super().__init__(text=QApplication.translate("SmartButton", text), parent=self.core_screen, **kwargs)

        if fontSize is not None:
            self.setFontSize(fontSize)
        if color is not None:
            self.setColor(QColor(color))

    def prepare(self, x, y, width=None, height=None):
        print('smart button init')

        # apply visual parameters
        self.setFixedSize(int(width), int(height))
        self.move(int(x), int(y))

        self.clicked.connect(self.buttonClickAction)

    def start(self):
        self.show()
        self.updateTimer.start()

    def buttonClickAction(self):
        """
        This method called then button clicked.
        override this to change default behaviour. Don't call super().buttonClickAction()
        if you don't want typical button stuff like write to vehicle or into core dicts
        """
        if self.askForConfirmation():
            print('default act to veh')
            if self.core.in_rc_vehid is not None:
                self.core.output_data_remote[self.core.in_rc_vehid][self.vehicleParamDict] = \
                    {self.paramName: self.value}
            elif self.core.watched_vehid is not None:
                self.core.output_data_remote[self.core.watched_vehid][self.vehicleParamDict] = \
                    {self.paramName: self.value}

    def buttonTimerAction(self):
        """
        This method called then update timer is tick.
        override this to change default behaviour
        """
        pass

    def askForConfirmation(self):
        """
        This method calls for QMessageBox, which confirms user input.
        Override this if you want non-standart confirmation
        @:return bool: True if confirmed False if not
        """
        def checkPass():
            """ Compare given pass with ultra-secret admin' pass hash """
            passw = passInput.text()
            print('CHECKING PASS:')
            global result
            if hashlib.md5(passw.encode()).hexdigest() == self.core.config['admin_pass_hash']:
                print("ACCEPTED")
                mb.accept()
            else:
                print("FAILED")
                incorrectPassLabel.show()

        if not self.needConfirm:
            return True
        else:
            # create message box
            if not self.needPassword:
                # simple checkout, create usual mb
                mb = QMessageBox()
                mb.setText(self.confirmationText)
                mb.setInformativeText(self.tr("Do you confirm this action?"))
                mb.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
                mb.setDefaultButton(QMessageBox.Cancel)
                mb.setIcon(QMessageBox.Warning)
                result = mb.exec()
                print("result = ", result)
            else:
                # not so simple checkout
                # create QDialog with custom layout, password input field etc
                mb = QDialog()
                mb.setModal(True)
                # create new dialog layouts with password field
                mbLayout = QVBoxLayout()
                passLayout = QFormLayout()

                # create components
                iconLabel = QLabel()
                iconLabel.setPixmap(self.style().standardIcon(QStyle.SP_MessageBoxCritical).pixmap(52, 52))

                passInput = QLineEdit()
                passInput.setEchoMode(QLineEdit.EchoMode.Password)
                passInput.setPlaceholderText(self.tr("Enter admin Password"))

                incorrectPassLabel = QLabel(text=self.tr("Incorrect Password!"))
                incorrectPassLabel.hide()
                # buttons for dialog
                buttonBox = QDialogButtonBox(QDialogButtonBox.StandardButton.Cancel | QDialogButtonBox.StandardButton.Ok)

                # STYLING
                mbLayout.setAlignment(Qt.AlignmentFlag.AlignHCenter)

                incorrectPassLabel.setStyleSheet("""
                color: #FA0000
                """)

                mbLayout.setContentsMargins(QMargins(20, 5, 5, 20))

                # WIRING
                iconLabel.setAlignment(Qt.AlignmentFlag.AlignHCenter)
                buttonBox.accepted.connect(checkPass)
                buttonBox.rejected.connect(mb.reject)

                # COMPOSING
                mb.setLayout(mbLayout)

                passLayout.addRow(QLabel(text=self.tr("Password:")), passInput)
                passLayout.addRow(QLabel(text=""), incorrectPassLabel)


                # iconLabel.setPixmap(QPixmap(self.style().standardIcon(QStyle.SP_MessageBoxCritical)))
                mbLayout.addWidget(iconLabel)
                mbLayout.addWidget(QLabel(self.confirmationText))
                mbLayout.addWidget(QLabel(text=self.tr("Do you confirm this action?")))
                mbLayout.addLayout(passLayout)

                mbLayout.addWidget(buttonBox)

                return True if mb.exec() == QDialog.Accepted else False

            print("result: ", result)
            if result == QMessageBox.Ok:
                return True
            else:
                return False


class SmartButtonForCore(SmartButton):
    """
    This is overloaded version of smart button. It already has
    "start" and "prepare" actions, inherited from it's parent,
    so overload this methods if it should be somehow unique.

    For example here I changed __init__ method, I set another set of arguments
    for this kind of buttons, and buttonClickAction also changed
    """
    def __init__(self, coreParam, **kwargs):
        print('args:')
        self.coreParam = coreParam

        for arg in kwargs:
            print(arg)
        super().__init__(**kwargs)

    def buttonClickAction(self):
        """
        Overrided method for special button. This one is sending data to core
        """
        # still should write this down coz we not use parents buttonClickAction method
        if self.askForConfirmation():
            print('overloaded act to core')
            if self.coreParam:
                print('old core flag:')
                print(getattr(self.core, self.coreParam))
                setattr(self.core, self.coreParam, self.value)
                print('new core flag:')
                print(getattr(self.core, self.coreParam))


class SmartButtonGroup(QWidget):
    """
    SmartButtonGroup is a set of checkable buttons. Buttons are SmartButtons,
    One pressed button becomes pressed, all other will uncheck
    """

    def __init__(self, core, buttons,
                 caption, colCount=1, paramName=None, defaultButton=None,
                 captionSize=None, fontSize=None, tracking=False, **kwargs):
        self.core = core
        self.core_screen = core.sensor_screen
        self.buttons = buttons
        self.paramName = paramName
        self.tracking = tracking

        super().__init__(**kwargs)
        self.setParent(self.core_screen)

        # CREATE UI
        topmostLayout = QVBoxLayout()
        # mainPanel = QGroupBox(title=caption)
        mainPanel = QGroupBox(title=QApplication.translate("SmartButtonGroup", caption))
        mainLayout = QGridLayout()

        self.buttonsWidgets = []

        self.updateTimer = QTimer()
        self.updateTimer.setInterval(200)  # Changed from 100ms
        self.updateTimer.timeout.connect(self.timerAction)


        print("fontSize is not None: ", fontSize is not None)
        for button, value in self.buttons.items():
            newButton = StyledPushButton(text=QApplication.translate("ButtonInGroup", button))
            if fontSize is not None:
                newButton.setFontSize(fontSize)
            newButton.setAccessibleName(button)
            newButton.setSizePolicy(QSizePolicy.Policy.MinimumExpanding, QSizePolicy.Policy.MinimumExpanding)
            newButton.setCheckable(True)
            newButton.clicked.connect(self.buttonClickedAction)
            if button == defaultButton:
                newButton.setChecked(True)
            else:
                newButton.setChecked(False)

            self.buttonsWidgets.append(newButton)

        if colCount == 1 and len(self.buttons)>1: # rounded corners should be only on external corners of widget!
            # topmost button
            self.buttonsWidgets[0].setRadiuses(6, 6, 0, 0)
            self.buttonsWidgets[-1].setRadiuses(0, 0, 6, 6)
            for button in self.buttonsWidgets[1: -1]:
                button.setRadiuses(0, 0, 0, 0)



        # STYLING
        mainLayout.setSpacing(15)
        # topmostLayout.setContentsMargins(3, 3, 3, 3)
        mainLayout.setContentsMargins(2, 2, 2, 2)
        if captionSize:
            styleSheet = """ QGroupBox{
                                font-size: %dpx;
                                }
                                QGroupBox::title{
                                subcontrol-position: top center;
                                color: white;
                                }
                                """ % captionSize
            mainPanel.setStyleSheet(styleSheet)
        mainLayout.setSpacing(2)
        # COMPOSING
        self.setLayout(topmostLayout)
        mainPanel.setLayout(mainLayout)

        topmostLayout.addWidget(mainPanel)

        row, col = 0, 0
        for button in self.buttonsWidgets:
            mainLayout.addWidget(button, row, col)
            if col == colCount-1:
                row += 1
                col = 0
            else:
                col += 1


    def prepare(self, x, y, width, height):
        self.setFixedSize(int(width), int(height))
        self.move(int(x), int(y))
        pass

    def start(self):
        self.updateTimer.start()
        self.show()

    def buttonClickedAction(self):
        # detect sender button
        thisButton = self.sender()

        # deselect other buttons
        if not self.tracking:
            for button in self.buttonsWidgets:
                if button != thisButton:
                    button.setChecked(False)

        # send data to vehicle
        value = self.buttons[thisButton.accessibleName()]
        if value.upper == 'TRUE':
            value = True
        if value.upper == 'FALSE':
            value = False

        print("Send", value,)

        if self.core.in_rc_vehid is not None:
            self.core.output_data[self.core.in_rc_vehid][self.paramName] = value
        elif self.core.watched_vehid is not None:
            self.core.output_data[self.core.watched_vehid][self.paramName] = value

        print("Gonna send to vehicle:")

    def timerAction(self):
        if self.core.in_rc_vehid is not None:
            vehid = self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            vehid = self.core.watched_vehid
        else:
            vehid = None

        if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
            self.setEnabled(True)
            if self.tracking:
                for button in self.buttonsWidgets:
                    # print(self.core.telemetry[vehid]['channels_states'][self.paramName], self.buttons[button.accessibleName()])
                    value = self.core.telemetry[vehid]['channels_states'][self.paramName]
                    if value == True:
                        value = "True"
                    if value == False:
                        value = "False"
                    if self.buttons[button.accessibleName()] != value :
                        button.setChecked(False)

                    else:
                        button.setChecked(True)
        else:
            self.setEnabled(False)


class SmartButtonGroupForCore(SmartButtonGroup):

    def __init__(self, *, coreParam, **kwargs):
        self.coreParam = coreParam
        super().__init__(**kwargs)

    def buttonClickedAction(self):
        # detect sender button
        thisButton = self.sender()

        # deselect other buttons
        for button in self.buttonsWidgets:
            if button != thisButton:
                button.setChecked(False)

        # send data to vehicle
        value = self.buttons[thisButton.accessibleName()]
        setattr(self.core, self.coreParam, value)
        print("Gonna send to vehicle:")
        print("core Param: {}, value: {}".format(self.coreParam, value))

    def timerAction(self):
        pass


class SmartCheckbox(StyledCheckBox):
    """ Smart Checkbox is a regular iPlace checkbox which can be included in project
        via modules.json and send data to core/vehicle """

    def __init__(self, *, core, text, fontSize=None,
                 paramName=None,  valueOnTrue=None, valueOnFalse=None, color=None, tracking=False, **kwargs):
        self.core = core
        self.core_screen = core.sensor_screen
        super().__init__(text=QApplication.translate("SmartButton", text), parent=self.core_screen, **kwargs)
        # Note: slider is already initialized in the parent class (StyledCheckBox)
        self.paramName = paramName
        self.valueOnTrue = valueOnTrue
        self.valueOnFalse = valueOnFalse
        self.tracking = tracking
        self.updateTimer = QTimer()
        self.updateTimer.setInterval(250)  # Changed from 100ms to 250ms to reduce CPU usage
        self.updateTimer.timeout.connect(self.checkboxTimerAction)


        if fontSize is not None:
            self.setFontSize(fontSize)
        if color is not None:
            self.setColor(QColor(color))

    def prepare(self, x, y, width=None, height=None):
        # apply visual parameters
        self.setFixedSize(int(width), int(height))
        self.move(int(x), int(y))

    def start(self):
        self.updateTimer.start()
        self.show()

    def mouseReleaseEvent(self, e: QtGui.QMouseEvent) -> None:
        if self.rect().contains(e.pos()):
            self.checkboxClickAction()

    def checkboxClickAction(self):
        """
        This method called then button clicked.
        override this to change default behaviour. Don't call super().buttonClickAction()
        if you don't want typical button stuff like write to vehicle or into core dicts
        """
        print('default act to veh')
        if not self.isChecked():
            value = self.valueOnTrue if self.valueOnTrue is not None else True
        else:
            value = self.valueOnFalse if self.valueOnFalse is not None else False

        if self.core.in_rc_vehid is not None:
            self.core.output_data[self.core.in_rc_vehid][self.paramName] = value
        elif self.core.watched_vehid is not None:
            self.core.output_data[self.core.watched_vehid][self.paramName] = value
        print("TR: ", self.tracking)
        if not self.tracking:
            newState = not self.isChecked()
            self.setChecked(newState)
            # Make sure the slider state is updated
            self.slider.setState(newState)

    def checkboxTimerAction(self):
        """
        This method called then update timer is tick.
        override this to change default behaviour
        """
        if self.core.in_rc_vehid is not None:
            vehid = self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            vehid = self.core.watched_vehid
        else:
            vehid = None

        if vehid is not None:
            self.setEnabled(True)
            if self.tracking:
                newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
                self.setChecked(newState)
                # Make sure the slider state is updated
                self.slider.setState(newState)
        else:
            self.setEnabled(False)


class SmartCheckboxForCore(SmartCheckbox):
    def __init__(self, coreParam, **kwargs):
        self.coreParam = coreParam
        super().__init__(**kwargs)

    def checkboxClickAction(self):
        print('default act to Core')
        if self.isChecked():
            value = self.valueOnTrue if self.valueOnTrue is not None else True
        else:
            value = self.valueOnFalse if self.valueOnFalse is not None else False

        setattr(self.core, self.coreParam, value)
        self.setChecked(not self.isChecked())
        print('gonna set value: ', value)


