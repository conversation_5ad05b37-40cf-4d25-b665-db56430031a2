from typing import Dict, List, Any, Union

from PyQt6 import QtGui, QtWidgets
from PyQt6.QtWidgets import (
    QWidget, QGroupBox, QSizePolicy,
    QVBoxLayout, QHBoxLayout, QPushButton, QApplication,
    QLabel, QScrollArea, QDialog, QTextEdit
)
from PyQt6.QtWidgets import QScroller, QScrollerProperties, QGridLayout
from PyQt6.QtCore import (
    Qt, QPoint,
    pyqtSignal, pyqtProperty, QSize, QMargins
)
from PyQt6.QtGui import QPainter, QColor, QPixmap, QPalette, QBrush

import logging
from enum import Enum
import os
import json
from functools import partial

from .vehicleErrors import VehicleErrors
from .styledPushButton import StyledPushButton
from .styledCheckBox import StyledCheckBox
from .ledWithCaption import Led<PERSON>ith<PERSON>aption
from .darkPalette import ColorSet
from .separator import Separator
from .blinkBehaviour import BlinkingBehaviour

dir_path = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(dir_path, '../resources_settings.json')
with open(config_path, 'r') as config:
    RESOURCES_CONFIG = json.load(config)


class VehicleCard(QtWidgets.QWidget, BlinkingBehaviour):
    """Widget representing a vehicle card in the vehicle selector.

    This widget displays vehicle information and provides controls for watching,
    controlling, and managing vehicle settings.
    """
    # Signal definitions with documentation
    shouldWatch = pyqtSignal(str)  # user clicks 'watch' button on card
    shouldControl = pyqtSignal(str)  # user clicks 'control' button on card with given vehid
    shouldRelease = pyqtSignal(str)  # user wants to disconnect, clear core's watch and rc vehid
    togglePermission = pyqtSignal(str, bool)  # user wants to change move permission state
    toggleRobot = pyqtSignal(str, bool)  # user wants to change robomode
    clicked = pyqtSignal(str)  # user clicked on free space
    wasCollapsed = pyqtSignal(str)  # card was collapsed
    wasExpanded = pyqtSignal(str)  # card was expanded
    clickedEmergencyButton = pyqtSignal(str)  # emergency button was clicked

    def __init__(self, name: str, vehicleType: int = 0, *args, **kwargs) -> None:
        """Initialize the VehicleCard.

        Args:
            name: The vehicle ID/name
            vehicleType: Type of vehicle (experimental feature)
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)

        # Set up properties for connected vehicle
        self._vehid = name  # str vehicle name (id)
        self._status = False  # boolean True if card is online
        self._task = ""  # str with current task
        self._errors = None  # list of errors
        self._mode = CardStatus.disconnected  # vehicle mode - watched, controlled, disconnected
        self._robot = False  # vehicle robot status, True is robot
        self._movePermission = False  # vehicle permission boolean, True if permit
        self._vehicleType = vehicleType  # experimental: when different types of vehicles are present
        self._rcNeeded = False  # whether remote control is needed
        self._emergency = False  # emergency status
        self.oldPos = None  # for tracking mouse movement

        self.setAutoFillBackground(True)
        self._color = ColorSet.buttonRegularColor.value.darker()

        # card properties
        self._expanded = False
        self._highlighted = False
        self._cardStatus = CardStatus.disconnected
        self.initUI()

        # initialize card as disabled
        self.setVehicleActivity(False)

    def initUI(self):
        # create layouts and widgets
        mainLayout = QVBoxLayout()
        # main panel stores all the widgets and layout
        # except for mainLayout = its top-layer layout element

        # mainPanel = QGroupBox('Vehicle')
        mainPanel = QGroupBox()
        # mainPanel.setAutoFillBackground(True)
        # self.setAutoFillBackground(True)

        palette = self.palette()
        palette.setColor(QPalette.ColorRole.Window, Qt.GlobalColor.yellow)
        mainPanel.setPalette(palette)

        palette.setColor(QPalette.ColorRole.Window, Qt.GlobalColor.green)
        self.setPalette(palette)

        mainPanelLayout = QVBoxLayout()
        headerLayout = QHBoxLayout()
        ledLayout = QHBoxLayout()
        buttonsLayout = QGridLayout()
        togglesLayout = QVBoxLayout()

        self.vehidCaption = QLabel(text=self._vehid.upper())
        self.taskCaption = QLabel(text=self.tr("Task: No info"))
        tridotImage = TridotLabel()
        self.statusLED = LedWithCaption(text=self.tr("Offline"))
        self.errorsLED = LedWithCaption(text=self.tr("No info"))
        self.modeLED = LedWithCaption(text=self.tr("No info"))

        # expandable area is just a wrapper to store controls when card is expanded
        self.expandableArea = QWidget()
        self.expandableArea.setAutoFillBackground(True)
        self.expandableArea.setVisible(False)
        pal = self.palette()
        color = self.palette().color(QPalette.ColorRole.Text)
        color.setAlpha(5)
        pal.setColor(QPalette.ColorRole.Window, color)
        self.expandableArea.setPalette(pal)
        expandableAreaLayout = QVBoxLayout()
        self.watchButton = StyledPushButton(text=self.tr("Watch", "like watch what this vehicle is doing"),
                                            fontSize=18)
        # self.watchButton.setPalette(darkPalette)
        self.watchButton.setPadding([3, 10, 3, 10])
        self.watchButton.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        self.controlButton = StyledPushButton(text=self.tr("Control", "like control this vehicle remotely"),
                                              fontSize=18)
        self.emergencyButton = StyledPushButton(text=self.tr("Emergency", "reset emergency"),
                                                fontSize=18)
        self.disconnectButton = StyledPushButton(text=self.tr("Disconnect",
                                                              "there means not watch and not control vehicle"),
                                                 fontSize=18)
        self.permissionCheckBox = StyledCheckBox(text=self.tr("Move Permission",
                                                              "does vehicle has permission to move?"),
                                                 fontSize=18, tracking=True)
        self.robotCheckBox = StyledCheckBox(
            text=self.tr("RoboMode", "Checkbox, if checked -> vehicle is robot, else manual"),
            fontSize=18, tracking=True)

        # wiring
        self.watchButton.clicked.connect(partial(self.shouldWatch.emit, self._vehid))
        self.controlButton.clicked.connect(partial(self.shouldControl.emit, self._vehid))
        self.disconnectButton.clicked.connect(partial(self.shouldRelease.emit, self._vehid))
        self.emergencyButton.clicked.connect(partial(self.clickedEmergencyButton.emit, self._vehid))
        self.permissionCheckBox.clicked.connect(self.permissionToggle)
        self.robotCheckBox.clicked.connect(self.robotToggle)

        # styling
        # captionBackgoundColor = self.palette().color(QPalette.ColorRole.Text)
        captionBackgroundColor = ColorSet.textColor.value
        captionBackgroundColor.setAlpha(20)

        # line
        # captionBorderColor = self.palette().color(QPalette.ColorRole.Text)
        self.watchButton.setColor(ColorSet.buttonRegularColor.value)
        self.controlButton.setColor(ColorSet.buttonBlueColor.value)
        self.emergencyButton.setColor(ColorSet.buttonRedColor.value)
        self.disconnectButton.setColor(ColorSet.buttonRedColor.value)
        self.permissionCheckBox.setColor(ColorSet.buttonRegularColor.value)
        self.robotCheckBox.setColor(ColorSet.buttonRegularColor.value)
        # COMPOSING
        self.setLayout(mainLayout)
        mainLayout.setContentsMargins(QMargins(0, 0, 0, 2))

        for button in (self.watchButton, self.controlButton, self.disconnectButton, self.permissionCheckBox,
                       self.robotCheckBox):
            button.setMinimumHeight(65)
        self.emergencyButton.setMinimumHeight(50)
        # widgets in topmost place (always visible
        headerLayout.addWidget(self.vehidCaption, 2)
        headerLayout.addWidget(self.taskCaption, 3)
        headerLayout.addWidget(tridotImage)
        tridotImage.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)

        # LED statuses
        ledLayout.addWidget(self.statusLED)
        ledLayout.addWidget(self.errorsLED)
        ledLayout.addWidget(self.modeLED)

        # buttons to control vehicle
        buttonsLayout.addWidget(self.watchButton, 0, 0)
        buttonsLayout.addWidget(self.controlButton, 0, 1)
        buttonsLayout.addWidget(self.disconnectButton, 1, 0, 1, 0)

        # toggles to control some togglable parametes
        togglesLayout.addWidget(self.permissionCheckBox)
        togglesLayout.addWidget(self.robotCheckBox)

        mainLayout.addWidget(mainPanel)
        mainPanel.setLayout(mainPanelLayout)

        # mainLayout.addLayout(headerLayout)
        # mainLayout.addWidget(self.expandableArea)
        mainPanelLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        mainPanelLayout.addLayout(headerLayout)
        mainPanelLayout.addLayout(ledLayout)
        mainPanelLayout.addWidget(self.expandableArea)

        self.expandableArea.setLayout(expandableAreaLayout)
        expandableAreaLayout.setContentsMargins(QMargins(4, 4, 4, 4))
        # expandableAreaLayout.addLayout(ledLayout, 2)
        expandableAreaLayout.addWidget(self.emergencyButton)
        expandableAreaLayout.addLayout(buttonsLayout, 4)
        separatorColor = ColorSet.textColor.value
        separatorColor.setAlpha(50)
        expandableAreaLayout.addWidget(Separator(color=separatorColor))
        expandableAreaLayout.addLayout(togglesLayout, 10)

        # self.name = name
        # self.vehicleType = vehicleType
        # self.selected = False
        # self.expanded = False
        #
        # # info to display
        # self.online = False
        # self.vehError = VehicleErrors.noConnection
        # # ERROR CODES:
        # # 0 - no errors (green LED)
        # # 1 - warning (yellow LED)
        # # 2 - error (red LED)
        # self.isRobot = False
        #
        # self.control_btn = StyledPushButton(parent=self, text='ПОДКЛЮЧИТЬСЯ',
        #                                     fontSize=18,
        #                                     )
        # self.control_btn.setColor(ColorSet.buttonBlueColor.value)
        # self.control_btn.setFixedSize(int(320), int(61))
        # self.control_btn.move(int(10), int(79))
        # self.control_btn.clicked.connect(self.connect)
        #
        # self.stop_btn = StyledPushButton(parent=self, text='Остановить',
        #                                 fontSize=18,
        #                                 )
        # self.stop_btn.setFixedSize(int(150), int(61))
        # self.stop_btn.move(int(10), int(147))
        # self.stop_btn.clicked.connect(self.stop)
        #
        # self.emergency_btn = StyledPushButton(parent=self,fontSize=18,
        #                                       text='АВАРИЙНАЯ\nОСТАНОВКА',
        #                                         )
        # self.emergency_btn.setColor(ColorSet.buttonRedColor.value)
        #
        # # connect buttons to actions
        # self.emergency_btn.setFixedSize(int(163), int(61))
        # self.emergency_btn.move(int(167), int(147))
        # self.emergency_btn.clicked.connect(self.emergencyStop)
        # self.hosting = hosting

    def setTask(self, task):
        self._task = task
        self.update()

    def setCardStatus(self, newStatus):
        self._cardStatus = newStatus
        self.update()

    def setVehicleActivity(self, newStatus):
        self._status = newStatus
        # update text color (dimmed if vehicle offline)
        if not newStatus:
            textColor = ColorSet.textColor.value
            # textColor = Qt.GlobalColor.white
        else:
            textColor = ColorSet.dimmedTextColor.value
            # textColor = Qt.GlobalColor.black

        palette = self.palette()
        palette.setColor(QPalette.ColorRole.WindowText, textColor)
        self.setPalette(palette)

        self.update()

    def setRobot(self, newRobotStatus):
        self._robot = newRobotStatus
        self.robotCheckBox.setChecked(newRobotStatus)
        if newRobotStatus:
            self.modeLED.setColor(ColorSet.ledGreenColor.value)
            self.modeLED.setText(self.tr('Robot'))
        else:
            self.modeLED.setColor(ColorSet.ledRedColor.value)
            self.modeLED.setText(self.tr('Manual',
                                         "Caption for RED LED when vehicle is not permitted to move"))

        self.update()

    def setMode(self, mode):
        self._mode = mode
        self.update()

    def setPermission(self, newPermission):
        self._movePermission = newPermission
        self.permissionCheckBox.setChecked(newPermission)
        if newPermission:
            self.errorsLED.setColor(ColorSet.ledGreenColor.value)
            self.errorsLED.setText(self.tr('Ok'))
        else:
            self.errorsLED.setColor(ColorSet.ledRedColor.value)
            self.errorsLED.setText(self.tr('Need Permission',
                                           "Caption for RED LED when vehicle is not permitted to move"))

        self.update()

    def setRCNeeded(self, value):
        """
        Set True when need to blink with Control button or
        whole body if collapsed
        """
        self._rcNeeded = value
        if value:
            if self._expanded:
                self.controlButton.setBlinking(True)
            else:
                self.setBlinking(True)
        else:
            self.setBlinking(False)
            self.controlButton.setBlinking(False)

    def setEmergency(self, value):
        """
        Set True when need to blink with Emergency button or
        whole body if collapsed
        """
        self._emergency = value
        if value:
            if self._expanded:
                self.emergencyButton.setVisible(True)
                self.emergencyButton.setBlinking(True)
            else:
                self.setBlinking(True)
        else:
            self.setBlinking(False)
            self.emergencyButton.setBlinking(False)
            self.emergencyButton.setVisible(False)

    def isRCNeeded(self):
        return self._rcNeeded

    def update(self):
        super().update()
        self.updateLEDs()
        self.updateButtons()
        self.updateTasks()
        self.updateMode()

        palette = self.palette()
        palette.setColor(QPalette.ColorRole.Window, self._color)
        self.setPalette(palette)

    def _paintCaption(self, color=None):
        if not color:
            captionBackgroundColor = ColorSet.textColor.value
            captionBackgroundColor.setAlpha(20)
        else:
            captionBackgroundColor = color

        captionBorderColor = ColorSet.textColor.value
        captionBorderColor.setAlpha(50)
        if self._status:
            textColor = ColorSet.textColor.value
        else:
            textColor = ColorSet.dimmedTextColor.value
        basicStyleSheet = """
        border: 1px solid {};
        border-radius: 5px;
        padding: 10px;
        font-size: 22px;
        color: {};
        """.format(
            captionBorderColor.name(QColor.NameFormat.HexArgb),
            textColor.name()
        )
        backgroundStyleSheet = """
        background-color: {};
        """.format(
            captionBackgroundColor.name(QColor.NameFormat.HexArgb),
        )
        self.vehidCaption.setStyleSheet(basicStyleSheet + backgroundStyleSheet)

    def updateMode(self):
        if not self._status:
            color = ColorSet.textColor.value
            color.setAlpha(20)
        if self._mode == CardStatus.watched:
            color = ColorSet.buttonGreenColor.value
        elif self._mode == CardStatus.disconnected:
            color = ColorSet.buttonRegularColor.value
        elif self._mode == CardStatus.controlled:
            color = ColorSet.buttonOrangeColor.value

        self._paintCaption(color)

    def updateTasks(self):
        if self._task != "":
            # self.taskCaption.setText(self._task)
            tr_text = QApplication.translate("Task", self._task)
            self.taskCaption.setText(tr_text)
        else:
            self.taskCaption.setText(self.tr("No task",
                                             "Label in vehicle selector when no task given"))

    def updateLEDs(self):
        if self._status:
            self.statusLED.setColor(ColorSet.ledGreenColor.value)
            self.statusLED.setText(self.tr('Online', "LED indicator when vehicle is online"))

            if self._robot:
                self.modeLED.setColor(ColorSet.ledGreenColor.value)
                self.modeLED.setText(self.tr("Robot", "LED when vehicle is in Robo-mode"))
            else:
                self.modeLED.setColor(ColorSet.ledRedColor.value)
                self.modeLED.setText(self.tr("Manual", "LED when vehicle is not in Robo-mode"))
        else:
            # if vehicle offline then all LEDs should be disconnected
            self.statusLED.setColor(ColorSet.ledRedColor.value)
            self.statusLED.setText(self.tr('Offline', "LED indicator when vehicle is offline"))
            self.modeLED.setColor(ColorSet.ledRedColor.value)
            self.modeLED.setText(self.tr("No Data", "LED indicator when vehicle is offline"))
            self.errorsLED.setColor(ColorSet.buttonRedColor.value)
            self.errorsLED.setText(self.tr("No Data", "LED indicator when vehicle is offline"))

        # TODO: errors processing to error LED
        pass

    def updateButtons(self):
        if not self._status:
            # if vehicle is not Online then all disabled
            self.watchButton.setEnabled(False)
            self.controlButton.setEnabled(False)
            self.disconnectButton.setEnabled(False)
            self.permissionCheckBox.setEnabled(False)
            self.robotCheckBox.setEnabled(False)
            self.emergencyButton.setVisible(False)
        else:
            # if vehicle is online, then follow conditions
            if self._mode == CardStatus.disconnected:
                self.watchButton.setEnabled(True)
                self.controlButton.setEnabled(True)
                self.disconnectButton.setEnabled(False)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)
            elif self._mode == CardStatus.watched:
                self.watchButton.setEnabled(False)
                self.controlButton.setEnabled(True)
                self.disconnectButton.setEnabled(True)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)
            elif self._mode == CardStatus.controlled:
                self.watchButton.setEnabled(True)
                self.controlButton.setEnabled(False)
                self.disconnectButton.setEnabled(True)
                self.permissionCheckBox.setEnabled(True)
                self.robotCheckBox.setEnabled(True)

        #
        # self.watchButton.setEnabled(True)
        # self.controlButton.setEnabled(True)
        # self.disconnectButton.setEnabled(True)
        # self.permissionCheckBox.setEnabled(True)

    def setDisconnected(self, value: bool) -> None:
        """Set the disconnected state of the vehicle.

        Args:
            value: Whether the vehicle is disconnected
        """
        if value:
            self.control_btn.setText('Подключиться')
        else:
            self.control_btn.setText('Отключиться')

    def checkForActiveErrors(self, data: Dict[str, List[Dict[str, Any]]]) -> None:
        """Check for active errors in the data.

        Args:
            data: Dictionary of active errors by vehicle ID
        """
        activeErrors = data.get(self.name)

        if activeErrors is not None:
            if len(activeErrors) == 0:
                self.vehError = VehicleErrors.noError
            elif len(activeErrors) == 1:
                if activeErrors[0]['code'] == 10123:
                    self.vehError = VehicleErrors.moveRequest
            else:
                self.vehError = VehicleErrors.fatalError

    def checkForUpdates(self, data: Dict[str, Dict[str, Any]]) -> None:
        """Check for updates in the data.

        Args:
            data: Dictionary of vehicle data by vehicle ID
        """
        # check if vehicle not connected anymore:
        if self.core.watched_vehid != self.name:
            self.control_btn.setText('ПОДКЛЮЧИТЬСЯ')

        if data.get(self.name):
            self.online = True
            _robot = False if data.get(self.name).get('status') == 0 else True
            self.isRobot = _robot
            # check for errors:
            error = data.get(self.name).get('error')
            # check for loader error:
            if error is not None:
                if error:
                    self.vehError = VehicleErrors.fatalError
                    print(self.vehError)
                else:
                    self.vehError = VehicleErrors.noError

            # self.vehError = VehicleErrors.noError
        else:
            self.online = False
            self.isRobot = False
            self.vehError = VehicleErrors.noConnection
        self.update()

    def setLostedVehicle(self, vehicleId: str) -> None:
        """Undock lost vehicle and set its fields to default.

        Args:
            vehicleId: The ID of the lost vehicle
        """
        if self.name == vehicleId:
            logging.warning(f"Vehicle {vehicleId} set inactive in vehSelector")
            self.online = False
            self.isRobot = False
            self.vehError = VehicleErrors.noConnection
            self.update()

    # actions
    def connect(self) -> None:
        """Send connect or disconnect signal to core."""
        if self.sender().text() == 'ПОДКЛЮЧИТЬСЯ':
            # set empty to watched vehid first
            self.core.setWatchedVeh("")
            self.core.setWatchedVeh(self.name)
            for card in self.hosting.panels:
                card.setDisconnected(True)
            self.parent().update()
            # set control button text on self
            self.setDisconnected(False)
        else:
            self.control_btn.setText('ПОДКЛЮЧИТЬСЯ')
            self.core.setWatchedVeh("")
            self.core.setControlledVeh("")
        self.update()

    # def stop(self):
    #     logging.debug('stop vehicle {}'.format(self.name))
    #     self.core.setPermission(self.name, False)

    # def emergencyStop(self):
    #     logging.debug('Emergency stop vehicle {}'.format(self.name))
    #     self.core.sendHardStop(self.name)

    def paintEvent(self, _: QtGui.QPaintEvent) -> None:
        """Handle paint events for the widget."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

    def mousePressEvent(self, event: QtGui.QMouseEvent) -> None:
        self.oldPos = event.pos()

    def mouseReleaseEvent(self, event: QtGui.QMouseEvent) -> None:
        """Handle mouse release events for the widget.

        Args:
            event: The mouse event
        """
        if self.oldPos is not None:
            deltaPoint = self.oldPos - event.pos()

            if deltaPoint.manhattanLength() > 20:
                event.ignore()
            else:
                self.setExpanded(not self.expanded())
                # On macOS, ensure the event is accepted
                event.accept()

    def permissionToggle(self, isPermit: Union[bool, Qt.CheckState]) -> None:
        """Called when permission check box is toggled.

        Args:
            isPermit: The new permission state (either a boolean or Qt.CheckState)
        """
        try:
            # Convert Qt.CheckState to boolean if needed
            if isinstance(isPermit, Qt.CheckState):
                is_permitted = (isPermit == Qt.CheckState.Checked)
            else:
                is_permitted = bool(isPermit)

            self.togglePermission.emit(self._vehid, is_permitted)
        except Exception as e:
            logging.error(f"Error in permission toggle: {e}")

    def robotToggle(self, isRobot: Union[bool, Qt.CheckState]) -> None:
        """Called when robot mode check box is toggled.

        Args:
            isRobot: The new robot mode state (either a boolean or Qt.CheckState)
        """
        try:
            # Convert Qt.CheckState to boolean if needed
            if isinstance(isRobot, Qt.CheckState):
                is_robot_mode = (isRobot == Qt.CheckState.Checked)
            else:
                is_robot_mode = bool(isRobot)

            self.toggleRobot.emit(self._vehid, is_robot_mode)
        except Exception as e:
            logging.error(f"Error in robot toggle: {e}")

    def setExpanded(self, value: bool) -> None:
        """Set whether the card is expanded.

        Args:
            value: Whether to expand the card
        """
        self._expanded = value
        if value:
            # expand this card
            self.expandableArea.show()
            try:
                self.wasExpanded.emit(self._vehid)
            except Exception as e:
                logging.error(f"Error emitting wasExpanded: {e}")

            # change blinking if needed
            if self.isRCNeeded():
                print('Try to expend but RC needed')
                self.setBlinking(False)
                self.controlButton.setBlinking(True)
        else:
            self.expandableArea.hide()
            try:
                self.wasCollapsed.emit(self._vehid)
            except Exception as e:
                logging.error(f"Error emitting wasCollapsed: {e}")

            if self.isRCNeeded():
                print('Try to collapse but RC needed')
                self.setBlinking(True)
                self.controlButton.setBlinking(False)

    def expanded(self) -> bool:
        """Get whether the card is expanded.

        Returns:
            Whether the card is expanded
        """
        return self._expanded

    expanded_ = pyqtProperty(bool, fset=setExpanded, fget=expanded)


class VehicleSelector(QtWidgets.QWidget):
    """Widget for selecting and managing vehicles.

    This widget displays a list of vehicle cards and provides functionality for
    watching, controlling, and managing vehicle settings.
    """

    def __init__(self, core: Any, cars: List[str], *args, **kwargs) -> None:
        """Initialize the VehicleSelector.

        Args:
            core: The core application object
            cars: List of vehicle IDs
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)

        # Initialize properties
        self.cars: List[str] = []
        self.core = core

        # Get vehicle IDs from core if available
        for (vehicle_id, _) in self.core.veh_active_states.items():
            self.cars.append(vehicle_id)

        # Override with provided cars list
        self.cars = cars
        self.panels: List[VehicleCard] = []
        # create layouts and components
        mainLayout = QVBoxLayout()
        # scroll area wrapper
        scrollWrapper = QWidget(parent=self)
        # scrollWrapper.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.scrollLayout = QVBoxLayout()
        self.scrollLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        self.scrollLayout.setSpacing(0)
        self.scrollLayout.setAlignment(Qt.AlignmentFlag.AlignTop)
        scrollWrapper.setLayout(self.scrollLayout)
        # add scroll area
        self.scrollArea = QScrollArea()
        # self.scrollArea.setContentsMargins(QMargins(0,0,0,0))
        self.scrollArea.setWidget(scrollWrapper)
        self.scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scrollArea.setWidgetResizable(True)

        # add scroller and its properties
        scrollerProp = QScrollerProperties()
        scrollerProp.setScrollMetric(QScrollerProperties.ScrollMetric.OvershootScrollDistanceFactor, 0.2)
        scrollerProp.setScrollMetric(QScrollerProperties.ScrollMetric.VerticalOvershootPolicy, QScrollerProperties.OvershootPolicy.OvershootAlwaysOn)
        scroller = QScroller.scroller(self.scrollArea)
        scroller.setScrollerProperties(scrollerProp)
        # scroller.grabGesture(self.scrollArea, QScroller.ScrollerGestureType.LeftMouseButtonGesture)
        scroller.grabGesture(self.scrollArea, QScroller.ScrollerGestureType.TouchGesture)

        for car in self.cars:
            vehicleCard = VehicleCard(name=car)
            vehicleCard.setBlinkColor(ColorSet.darkRedColor.value)
            vehicleCard.setBlinkingFrequency(300)
            vehicleCard.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                      QSizePolicy.Policy.Fixed)
            vehicleCard.wasExpanded.connect(self.processExpandedCard)

            self.panels.append(vehicleCard)
            self.scrollLayout.addWidget(vehicleCard)

        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                             QSizePolicy.Policy.MinimumExpanding)

        # WIRING
        for card in self.panels:
            # card.shouldWatch.connect(partial(self.cardCallToWatch, card))
            # card.shouldControl.connect(partial(self.cardCallToControl, card))
            # card.shouldRelease.connect(partial(self.cardCallToDisconnect, card))
            card.shouldWatch.connect(self.cardCallToWatch)
            card.shouldControl.connect(self.cardCallToControl)
            card.shouldRelease.connect(self.cardCallToDisconnect)
            card.clickedEmergencyButton.connect(self.cardEmergency)
            card.wasExpanded.connect(partial(self.changeCardState, card._vehid, True))
            card.wasCollapsed.connect(partial(self.changeCardState, card._vehid, False))
            card.togglePermission.connect(self.cardTogglePermission)
            card.toggleRobot.connect(self.cardToggleRobot)

        self.panels[1].setRCNeeded(True)

        # COMPOSING
        self.setLayout(mainLayout)
        mainLayout.setContentsMargins(QMargins(1, 1, 1, 1))

        self.scrollLayout.addWidget(spacer)
        self.scrollLayout.setContentsMargins(QMargins(0, 0, 0, 0))
        mainLayout.addWidget(self.scrollArea)

    def changeCardState(self, vehid, state):
        if state:
            self.core.setSelectedVehid(vehid)
        else:
            self.core.setSelectedVehid(None)

    def sendFinishMovingCmd(self) -> None:
        """Send a finish moving command to the active vehicle."""
        print("sendFinishMovingCmd")
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid
        if vehid is None:
            return
        cmd = {
            'finish_moving': True
        }
        self.core.output_data[vehid].update(cmd)


    def cardCallToWatch(self, vehid: str) -> None:
        """Handle request to watch a vehicle.

        Args:
            vehid: The vehicle ID to watch
        """
        print(f'Card for {vehid} vehid wants to watch')


        class Switch2MovingDialog(QDialog):
            def __init__(self, parent=None):
                super(Switch2MovingDialog, self).__init__(parent)
                self.setWindowTitle(self.tr("Switch to Autonomous Moving") + ", vehicle " + vehid)

                self.vehicle_selector = parent

                self.setMinimumWidth(640)
                self.setMinimumHeight(360)

                layoutH = QHBoxLayout()
                layoutV = QVBoxLayout()

                self.labelImg = QLabel()
                icon_file_path = os.path.join(RESOURCES_CONFIG["images_folder"], "warning.png")
                pixmap = QPixmap(icon_file_path)
                self.labelImg.setPixmap(pixmap.scaledToWidth(150))

                self.text = QTextEdit()
                self.text.textCursor().insertHtml(self.tr("<b>Drill with vehicle id {} could move after switch to autonomous mode.</b><br>".format(vehid)))
                self.text.textCursor().insertHtml(self.tr("Drill could move after switch to autonomous mode, because planner is in Moving, Approach or Computing mode. <br><br>"
                                                          "If this is what you expect, check the goal point and just press <b>Continue</b> button. <br><br>"
                                                          "If you finished moving manually and want robot to continue with drilling, "
                                                          "press <b>Finish moving and continue autonomous</b>."))
                self.text.setReadOnly(True)

                self.buttonContinue = QPushButton(self.tr("Continue"))
                self.buttonFinishAndContinue = QPushButton(self.tr("Finish moving and continue autonomous"))
                self.buttonCancel = QPushButton(self.tr("Cancel"))

                self.buttonContinue.clicked.connect(self.accept)
                self.buttonCancel.clicked.connect(self.reject)

                self.buttonFinishAndContinue.clicked.connect(self.onButtonFinishAndContinueClicked)

                layoutH.addWidget(self.labelImg)
                layoutV.addWidget(self.text)
                layoutV.addWidget(self.buttonContinue)
                layoutV.addWidget(self.buttonFinishAndContinue)
                layoutV.addWidget(self.buttonCancel)
                layoutH.addLayout(layoutV)
                layoutH.setContentsMargins(15, 15, 15, 15)
                self.setLayout(layoutH)

            def onButtonFinishAndContinueClicked(self):
                print("onButtonFinishAndContinueClicked")
                self.vehicle_selector.sendFinishMovingCmd()
                self.accept()


        if vehid is not None and "PlannerNode" in self.core.telemetry[vehid].keys() and \
                "MainStateMachineNode" != self.core.telemetry[vehid].keys():
            if self.core.telemetry[vehid]["MainStateMachineNode"] == "remote" and \
                    self.core.telemetry[vehid]["PlannerNode"] in ("moving", "approach", "computing"):
                warn_dialog = Switch2MovingDialog(self)
                # warn_dialog.buttonFinishAndContinue.clicked.connect(partial(self.sendFinishMovingCmd))
                if warn_dialog.exec():
                    print("warn_dialog returned true")
                else:
                    print("warn_dialog returned false")
                    return


        if self.core.setWatchedVehid(vehid):
            # find card for given vehid
            for card in self.panels:
                if card._vehid == vehid:
                    card.setCardStatus(CardStatus.watched)
                else:
                    card.setCardStatus(CardStatus.disconnected)

    def cardCallToControl(self, vehid: str) -> None:
        """Handle request to control a vehicle.

        Args:
            vehid: The vehicle ID to control
        """
        print(f'Card for {vehid} vehid wants to control')
        if self.core.setControlledVehid(vehid):
            for card in self.panels:
                if card._vehid == vehid:
                    card.setCardStatus(CardStatus.controlled)
                else:
                    card.setCardStatus(CardStatus.disconnected)

    def cardEmergency(self, vehid: str) -> None:
        """Handle emergency button click for a vehicle.

        Args:
            vehid: The vehicle ID
        """
        class EmergencyDialog(QDialog):
            def __init__(self, parent=None):
                super(EmergencyDialog, self).__init__(parent)
                self.setWindowTitle(self.tr("Emergency") + ", vehicle " + vehid)

                self.setMinimumWidth(640)
                self.setMinimumHeight(360)

                layoutH = QHBoxLayout()
                layoutV = QVBoxLayout()

                self.labelImg = QLabel()
                icon_file_path = os.path.join(RESOURCES_CONFIG["images_folder"],
                                              "emergency.png")
                pixmap = QPixmap(icon_file_path)
                self.labelImg.setPixmap(pixmap.scaledToWidth(150))

                self.text = QTextEdit()
                self.text.textCursor().insertHtml(self.tr("<b>Emergency was set for the {} vehicle.</b><br><br>".format(vehid)))
                self.text.textCursor().insertHtml(self.tr("Operator can activate the emergency mode with a button on the "
                                                          "control panel or remotely with a radio remote control button. <br><br>"
                                                          "Motor would be automatically stopped in this mode. <br><br>"
                                                          "Please identify the problem that caused the emergency and eliminate it. "
                                                          "Verify with the cameras that the operation is safe. <br><br>"
                                                          "Afterwards, you can reset the emergency with the button below. <br><br>"
                                                          "To hide this window, press the cancel button. "
                                                          "You can return to this window later by pressing the emergency button "
                                                          "in the vehicle selection window."))
                self.text.setReadOnly(True)

                self.buttonReset = QPushButton(self.tr("Reset emergency"))
                self.buttonCancel = QPushButton(self.tr("Hide this dialog"))

                self.buttonReset.clicked.connect(self.accept)
                self.buttonCancel.clicked.connect(self.reject)

                layoutH.addWidget(self.labelImg)
                layoutV.addWidget(self.text)
                layoutV.addWidget(self.buttonReset)
                layoutV.addWidget(self.buttonCancel)
                layoutH.addLayout(layoutV)
                layoutH.setContentsMargins(15, 15, 15, 15)
                self.setLayout(layoutH)

        em_dialog = EmergencyDialog()
        # em_dialog.buttonReset.clicked.connect(partial(self.sendResetEmergencyCmd, vehid))     # use to leave dialog open after click

        if em_dialog.exec():
            print("Send reset emergency command for vehid {}".format(vehid))
            self.sendResetEmergencyCmd(vehid)


    def sendResetEmergencyCmd(self, vehid: str) -> None:
        """Send a reset emergency command to a vehicle.

        Args:
            vehid: The vehicle ID
        """
        print(f"Sending reset emergency command to vehid {vehid}")
        cmd = {"emergency_control": "reset"}
        if self.core.in_rc_vehid is not None:
            self.core.output_data[self.core.in_rc_vehid].update(cmd)
        elif self.core.watched_vehid is not None:
            self.core.output_data[self.core.watched_vehid].update(cmd)
        elif self.core.selected_vehid is not None:
            self.core.output_data[self.core.selected_vehid].update(cmd)

    def cardCallToDisconnect(self, vehid: str) -> None:
        """Handle request to disconnect from a vehicle.

        Args:
            vehid: The vehicle ID to disconnect from
        """
        print(f'Card for {vehid} vehid wants to disconnect')
        if self.core.disconnectFromAll():
            for card in self.panels:
                card.setCardStatus(CardStatus.disconnected)

    def cardTogglePermission(self, vehid: str, isPermit: bool) -> None:
        """Handle request to toggle move permission for a vehicle.

        Args:
            vehid: The vehicle ID
            isPermit: Whether to permit movement
        """
        print(f'Card for {vehid} vehid wants to set move permission: {isPermit}')
        self.core.setParameter(vehid, 'move_permission', isPermit)

    def cardToggleRobot(self, vehid: str, isRobot: bool) -> None:
        """Handle request to toggle robot mode for a vehicle.

        Args:
            vehid: The vehicle ID
            isRobot: Whether to enable robot mode
        """
        print(f'Card for {vehid} vehid wants to set robomode: {isRobot}')
        self.core.setParameter(vehid, 'robot', isRobot)

    def setVehiclePermission(self, vehid: str, isPermit: bool) -> None:
        """Set the move permission for a vehicle.

        Args:
            vehid: The vehicle ID
            isPermit: Whether to permit movement
        """
        for card in self.panels:
            if card._vehid == vehid:
                card.setPermission(isPermit)

    def setRobot(self, vehid: str, robotMode: bool) -> None:
        """Set the robot mode for a vehicle.

        Args:
            vehid: The vehicle ID
            robotMode: Whether to enable robot mode
        """
        for card in self.panels:
            if card._vehid == vehid:
                card.setRobot(robotMode)

    def processExpandedCard(self, vehid: str) -> None:
        """Process a card being expanded.

        Args:
            vehid: The vehicle ID of the expanded card
        """
        for card in self.panels:
            if card._vehid != vehid:
                card.setExpanded(False)
            else:
                self.core.setSelectedVehid(card._vehid)

    def setActiveStatus(self, vehid: str, status: bool) -> None:
        """Set the active status for a vehicle.

        Args:
            vehid: The vehicle ID
            status: Whether the vehicle is active
        """
        for card in self.panels:
            if card._vehid == vehid:
                card.setVehicleActivity(status)

    def setVehicleTask(self, vehid: str, task: str) -> None:
        """Set the task for a vehicle.

        Args:
            vehid: The vehicle ID
            task: The task description
        """
        for card in self.panels:
            if card._vehid == vehid:
                card.setTask(task)

    def setVehicleMode(self, vehid: str, mode: str) -> None:
        """Set the mode for a vehicle.

        Args:
            vehid: The vehicle ID
            mode: The mode ('rc', 'watched', or 'disconnected')
        """
        for card in self.panels:
            if card._vehid == vehid:
                if mode == 'rc':
                    card.setMode(CardStatus.controlled)
                elif mode == 'watched':
                    card.setMode(CardStatus.watched)
                elif mode == 'disconnected':
                    card.setMode(CardStatus.disconnected)

    def setVehicleNeedRC(self, vehid: str, rcNeeded: bool) -> None:
        """Set whether a vehicle needs remote control.

        Args:
            vehid: The vehicle ID
            rcNeeded: Whether remote control is needed
        """
        for card in self.panels:
            if card._vehid == vehid:
                card.setRCNeeded(rcNeeded)

    def setEmergency(self, vehid: str, em: bool) -> None:
        """Set the emergency status for a vehicle.

        Args:
            vehid: The vehicle ID
            em: Whether there is an emergency
        """
        for card in self.panels:
            if card._vehid == vehid:
                card.setEmergency(em)

    def update(self, *args, **kwargs) -> None:
        """Update the widget and all its children.

        Args:
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        for child in self.children():
            child.update()
        super().update(*args, **kwargs)

    def collapseAll(self) -> None:
        """Collapse all vehicle cards."""
        for card in self.panels:
            card.setExpanded(False)

    def addWid(self) -> None:
        """Add a new vehicle card (test function).

        Note: This is a test function and should be removed before release.
        """
        _button = VehicleCard(name='new car', parent=self)
        _button.setFixedHeight(74)
        self.layout.removeWidget(self.spacer)
        self.layout.addWidget(_button)
        self.layout.addWidget(self.spacer)


class TridotLabel(QWidget):
    """A custom widget that displays three vertical dots.

    This widget is used as a visual indicator in the vehicle card header.
    """

    def __init__(self, *args, **kwargs) -> None:
        """Initialize the TridotLabel.

        Args:
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)

    def paintEvent(self, a0: QtGui.QPaintEvent) -> None:
        """Paint the three dots.

        Args:
            a0: The paint event
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)

        # Create color (it's the text color in current color scheme)
        _color = self.palette().color(QPalette.ColorRole.Text)
        _brush = QBrush(_color, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        painter.setPen(Qt.PenStyle.NoPen)

        # Calculate x and y coordinates
        x = int(self.width()) / 2
        dotRadius = 3
        ys = [int(self.height()) / 4 * n for n in range(1, 4)]

        # Draw the dots
        for y in ys:
            painter.drawEllipse(QPoint(int(x), int(y)), dotRadius, dotRadius)

        return super().paintEvent(a0)

    def sizeHint(self) -> QSize:
        """Return the recommended size for the widget.

        Returns:
            The recommended size
        """
        return QSize(40, 40)


class CardStatus(Enum):
    """Enum representing the status of a vehicle card.

    Attributes:
        disconnected: The vehicle is not being watched or controlled
        watched: The vehicle is being watched
        controlled: The vehicle is being controlled
    """
    disconnected = 0
    watched = 1
    controlled = 2
