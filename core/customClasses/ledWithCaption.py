from PyQt6 import Qt<PERSON>ore, QtGui, QtWidgets
from PyQt6.QtWidgets import (QTableWidgetItem, QWidget, QGroupBox, QSizePolicy, QMessageBox,
                             QBoxLayout, QVBoxLayout, QHBoxLayout, QPushButton, QApplication,
                             QLabel, QScrollArea, QScroller, QScrollerProperties, QStackedLayout,
                             QComboBox, QFrame, QGridLayout, QLineEdit, QCheckBox)
from PyQt6.QtCore import (Qt, QPoint, QPointF, QRect, QTimer, QPropertyAnimation,
                          QUrl, pyqtSignal, QSize, QMargins, QRectF)
from PyQt6.QtGui import (QFontMetrics, QPainter, QColor, QFont, QPixmap, QPalette,
                         QPen, QBrush, QIcon, QPainterPath, QPainterPathStroker)
# from PyQt6.QtMultimedia import QMediaPlayer, QMediaPlaylist, QMediaContent, QSound
# from PyQt6.QtMultimediaWidgets import QVideoWidget

from functools import partial
from .vehicleErrors import VehicleErrors
from .darkPalette import ColorSet


class LEDBulb(QtWidgets.QWidget):
    def __init__(self, color=Qt.GlobalColor.gray,
                 offColor=Qt.GlobalColor.gray,
                 size=15,
                 blinkFreq=-1, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._color = color
        self._offColor = offColor
        self._size = size
        self._blinkFreq = blinkFreq
        self._active = True

        self.blinkTimer = QTimer()
        self.blinkTimer.timeout.connect(self.toggleColor)
        if self._blinkFreq > 0:
            self.blinkTimer.setInterval(self._blinkFreq)
            self.blinkTimer.start()
        else:
            self.blinkTimer.setInterval(0)

    def paintEvent(self, e):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        _pen = QPen(self.palette().dark().color(), 0.25, Qt.PenStyle.SolidLine)
        if self._active:
            _brush = QBrush(self._color, Qt.BrushStyle.SolidPattern)
        else:
            _brush = QBrush(self._offColor, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        painter.setPen(_pen)
        painter.translate(QPointF(
            int(self.width())/2,
            int(self.height())/2
        ))
        painter.drawEllipse(QPoint(0, 0), int(self._size/2), int(self._size/2))

    def setSize(self, size):
        self._size = size
        self.update()

    def resizeEvent(self, event):
        self.setSize(min(int(event.size().width()), int(event.size().height())))

    def sizeHint(self):
        return(QSize(int(self._size), int(self._size)))

    def setColor(self, color):
        self._color = color
        self.update()

    def setBlinkFreq(self, freq):
        self._blinkFreq = freq
        self.blinkTimer.setInterval(freq)

    def setBlinking(self, blinkState, freq=None):
        """"
        Set LED to blink or stop blinking

        :param blinkState Is blinking active
        :type blinkState bool
        :param freq Blinking frequency
        :type freq int
        """
        if freq:
            self.setBlinkFreq(freq)
        if blinkState:
            self.blinkTimer.start()
        else:
            self.blinkTimer.stop()

    def toggleColor(self):
        self._active = not self._active
        self.update()

class LedWithCaption(QWidget):
    def __init__(self,
                 text='',
                 fontSize=None,
                 ledColor=Qt.GlobalColor.gray,
                 ledOffColor=Qt.GlobalColor.gray,
                 blinkFreq=-1,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        # properties

        # create components and layouts
        mainLayout = QHBoxLayout()
        # create text label
        self.caption = QLabel(text=text)
        self.caption.setSizePolicy(QSizePolicy.Policy.MinimumExpanding, QSizePolicy.Policy.MinimumExpanding)

        # refine label text size
        if fontSize is not None:
            _font = QFont()
            _font.setPixelSize(fontSize)
            self.caption.setFont(_font)

        # create led bulb with relative size
        _fontMetrics = QFontMetrics(self.caption.font())
        ledSize = int(_fontMetrics.height())*0.8
        self.ledBulb = LEDBulb(color=ledColor,
                               offColor=ledOffColor,
                               blinkFreq=blinkFreq,
                               size=ledSize)
        self.ledBulb.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.MinimumExpanding)


        # wiring

        # composing
        self.setLayout(mainLayout)
        mainLayout.addWidget(self.ledBulb)
        mainLayout.addWidget(self.caption)

    def sizeHint(self):
        return QSize(
            int(self.ledBulb.sizeHint().width() + self.caption.sizeHint().width()),
            int(self.ledBulb.sizeHint().height() + self.caption.sizeHint().height())
        )

    def setColor(self, color):
        self._color = color
        self.ledBulb.setColor(self._color)

    def setText(self, text):
        self.caption.setText(text)

    def setFontSize(self, size):
        _font = self.caption.font()
        _font.setPixelSize(size)
        self.caption.setFont(_font)

    def text(self):
        return self.caption.text()

    def color(self):
        return self._color

    def setBlinking(self, blinkState, freq=None):
        self.ledBulb.setBlinking(blinkState, freq)
