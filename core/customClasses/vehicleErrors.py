from enum import Enum

class VehicleErrors(Enum):
    """ Describe all types of vehilce errors.
        Stores list of values
        value 0 - error value (2- red value, 1 - yellow value, 0 - green value),
            for LED widgets
        value 1 - error message to error labels """
    noConnection = (2, 'Not connected')
    moveRequest = (1, 'Move Request')
    fatalError = (2, 'Fatal Error')
    noError = (0, 'No Errors')