from PyQt6 import Qt<PERSON>ore, QtGui, QtWidgets
from PyQt6.QtWidgets import (QTableWidgetItem, QWidget, QGroupBox, QSizePolicy, QMessageBox,
                             QBoxLayout, QVBoxLayout, QHBoxLayout, QPushButton, QApplication,
                             QLabel, QScrollArea, QScroller, QScrollerProperties, QStackedLayout,
                             QComboBox, QFrame, QGridLayout, QLineEdit, QCheckBox)
from PyQt6.QtCore import (Qt, QPoint, QPointF, QRect, QTimer, QPropertyAnimation,
                          QUrl, pyqtSignal, QSize, QMargins, QRectF, pyqtProperty)
from PyQt6.QtGui import (QFontMetrics, QPainter, QColor, QFont, QPixmap, QPalette,
                         QPen, QBrush, QIcon, QPainterPath, QPainterPathStroker)
# from PyQt6.QtMultimedia import QMediaPlayer, QMediaPlaylist, QMediaContent, QSound
# from PyQt6.QtMultimediaWidgets import QVideoWidget

from functools import partial
from .vehicleErrors import VehicleErrors
from .darkPalette import ColorSet
from .blinkBehaviour import BlinkingBehaviour


class StyledPushButton(QtWidgets.QPushButton, BlinkingBehaviour):
    def __init__(self, fontSize=24,
                 subFontSize=10,
                 icon=None,
                 iconShift=0.85,
                 alignment=Qt.AlignmentFlag.AlignHCenter,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Адаптивный размер шрифта в зависимости от размера кнопки
        self.fontSize = min(fontSize, self.height() // 2) if self.height() > 0 else fontSize
        self._alignment = alignment
        self._padding = [2, 2, 2, 2]
        self._disabled = False
        self._hover = False
        self.radiuses = [6, 6, 6, 6]

        self._subCaption = ""
        self.subFontSize = subFontSize

        self._color = self.palette().color(QPalette.ColorRole.Button)

        if icon != None:
            self.icon = QPixmap(icon)
            self.iconShift = iconShift
        else:
            self.icon = None

        self._font = QFont()

    def setSubCaption(self, text):
        """Set the sub-caption of the button."""
        self._subCaption = text
        self.update()

    def setRadiuses(self,
                    top=None,
                    right=None,
                    bottom=None,
                    left=None):
        if top is not None:
            self.radiuses[0] = top
        if right is not None:
            self.radiuses[1] = right
        if bottom is not None:
            self.radiuses[2] = bottom
        if left is not None:
            self.radiuses[3] = left

        self.update()


    def sizeHint(self):
        if hasattr(self, "_font"):
            fm = QFontMetrics(self._font)
            return QSize(int(fm.horizontalAdvance(self.text()) * 1.5 + 10), int(fm.height() + self._padding[0] + self._padding[2]))
        else:
            return QSize(80, 20)

    def setColor(self, color):
        if type(color) == QBrush:
            color = color.color()
        self._color = color
        self._brush = QBrush(color)
        self.update()

    def resetColor(self):
        self._color = self.palette().color(QPalette.ColorRole.Button)
        self._brush = QBrush(self._color)
        self.update()

    def setFontSize(self, size):
        self.fontSize = size
        self.update()

    def setIcon(self, pixmap):
        self.icon = pixmap
        self.update()

    def startBlinking(self, color=None):
        if color is not None:
            pass
        else:
            color = self._color

    def paintEvent(self, e):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        # painting instruments:

        self._pen = QPen(self.palette().dark().color(), 0.25, Qt.PenStyle.SolidLine)

        # Адаптивный размер шрифта в зависимости от размера кнопки
        adaptiveFontSize = min(self.fontSize, self.height() // 3) if self.height() > 0 else self.fontSize

        self._font = QFont()
        self._font.setFamily('Roboto')
        self._font.setPixelSize(adaptiveFontSize)
        self._font.setBold(True)
        self._font.setCapitalization(QFont.Capitalization.AllUppercase)
        self._font.setLetterSpacing(QFont.SpacingType.PercentageSpacing, 115)
        self._brush = QBrush(self._color)
        self._pen = QPen(self.palette().dark().color(), 0.25, Qt.PenStyle.SolidLine)
        self._pen.setColor(self.palette().color(QPalette.ColorRole.Dark))
        painter.setPen(self._pen)
        if not self.isEnabled():
            self._brush.setColor(self.palette().color(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button))
        else:
            if self._hover:
                self._brush.setColor(self._color.darker(120))
            if self.isDown():
                self._brush.setColor(self._color.darker(150))
            if self.isChecked():
                self._brush.setColor(self.palette().color(QPalette.ColorRole.Highlight))

        painter.setBrush(self._brush)
        if self.radiuses[0] == self.radiuses[1] == self.radiuses[2] == self.radiuses[3]:
            painter.drawRoundedRect(self.rect(), self.radiuses[0], self.radiuses[0])
        else:
            # create non-even rounded rectangular form

            # boringPath = QPainterPath()
            # boringHeadRect = QRectF(float(self.w1 + 5), float(self.h1 + drillPositionToDraw - 1), float(self.boreholeRadius * 2 - 10), float(-47))
            # boringPath.addRoundedRect(boringHeadRect, 7, 7)
            # boringRodPath = QPainterPath()
            # boringRodPath.addRect(QRectF(float(#     self.w1 + 10), float(0), float(#     self.boreholeRadius * 2 - 20), float(self.drawedDrillPosition - 3 + self.h1
            # )))
            #
            # painter.drawPath((boringPath.united(boringRodPath)))

            roundRectPath = QPainterPath()
            roundRectPath.moveTo(self.radiuses[0], 0)
            roundRectPath.lineTo(int(self.width())-self.radiuses[1], 0)
            roundRectPath.arcTo(QRectF(int(self.width())-self.radiuses[1]*2, float(0), float(self.radiuses[1]*2), float(self.radiuses[1]*2)), 90, -90)
            roundRectPath.lineTo(int(self.width()), int(self.height())-self.radiuses[2])
            roundRectPath.arcTo(QRectF(int(self.width())-self.radiuses[2]*2, int(self.height())-self.radiuses[2]*2, float(self.radiuses[2]*2), float(self.radiuses[2]*2)), 0, -90)
            roundRectPath.lineTo(self.radiuses[3], int(self.height()))
            roundRectPath.arcTo(QRectF(float(0), int(self.height())-self.radiuses[3]*2, float(self.radiuses[3]*2), float(self.radiuses[3]*2)), -90, -90)
            roundRectPath.lineTo(0, self.radiuses[0])
            roundRectPath.arcTo(QRectF(float(0), float(0), float(self.radiuses[0]*2), float(self.radiuses[0]*2)), -180, -90)

            roundRectPath.closeSubpath()

            painter.drawPath(roundRectPath)


        # print text upon button
        painter.setFont(self._font)
        if self.isEnabled():
            # self._pen = QPen(self.palette().color(QPalette.ColorRole.WindowText), Qt.PenStyle.SolidLine)
            self._pen.setColor(self.palette().color(QPalette.ColorRole.ButtonText))
        else:
            self._pen.setColor(self.palette().color(QPalette.ColorGroup.Inactive, QPalette.ColorRole.ButtonText))
            self._pen.setColor(Qt.GlobalColor.darkGray)
            # self._pen = QPen(self.palette().color(QPalette.Disabled, QPalette.ColorRole.WindowText), Qt.PenStyle.SolidLine)
        painter.setPen(self._pen)

        # print sub-caption if it's not empty
        if self._subCaption:
            padding = 7

            # main text shifted up
            painter.drawText(QRect(int(self._padding[3]), int(self._padding[0] - padding), int(self.width()) - self._padding[1], int(self.height()) - self._padding[2] + padding),
                             self._alignment + Qt.AlignmentFlag.AlignVCenter, self.text())

            sub_font = QFont(self._font)
            sub_font.setPixelSize(self.subFontSize)
            painter.setFont(sub_font)
            # Shift the sub-caption down by self.fontSize + some_padding
            painter.drawText(QRect(int(self._padding[3]), int(self._padding[0] + self.fontSize + padding), int(self.width()) - self._padding[1], int(self.height()) - self._padding[2] - self.fontSize - padding),
                             self._alignment + Qt.AlignmentFlag.AlignVCenter, self._subCaption)
        else:
            painter.drawText(QRect(int(self._padding[3]), int(self._padding[0]), int(self.width())-self._padding[1], int(self.height())-self._padding[2]),
                             self._alignment + Qt.AlignmentFlag.AlignVCenter, self.text())

        # add icon if needed
        if self.icon != None:
            if self.iconShift:
                painter.drawPixmap(QPoint(int(self.width() * self.iconShift), int(self.height()/2 - self.icon.height()/2)), self.icon)
            else:
                painter.drawPixmap(QPoint(int(self.width()/2 - self.icon.width()/2),
                                          int(self.height()/2 - self.icon.height()/2)), self.icon)

    def disabled(self):
        return self._disabled

    def setAlignment(self, alignment):
        self._alignment = alignment
        self.update()

    def setPadding(self, padding):
        self._padding = padding
        self.update()

    def setDisabled(self, value):
        if type(value) == str:
            self._disabled = not bool(value)
        else:
            self._disabled = bool(value)

        self.update()

    def mousePressEvent(self, e):
        if not self.disabled():
            super().mousePressEvent(e)
            self.update()

    def touchEndEvent(self, e):
        self.click()

    def mouseReleaseEvent(self, e):
        super().mouseReleaseEvent(e)
        self.update()

    def leaveEvent(self, e):
        self._hover = False
        self.update()

    def enterEvent(self, e):
        self._hover = True
        self.update()
