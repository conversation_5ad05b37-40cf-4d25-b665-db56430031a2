from PyQt6.QtCore import Qt, QSize
from PyQt6.QtWidgets import QWidget
from PyQt6.QtGui import QPalette, QPainter, QPen, QColor

class Separator(QWidget):
    """Separator is a simple widget with just one line with given width, color"""
    def __init__(self, lineWidth=1, orientation=Qt.Orientation.Horizontal,
                 color=None, padding=.0, *args, **kwargs):
        """
        create new Separator
        :param lineWidth thickness of separator line
        :type lineWidth int
        :param orientation orientation of separator (vertical of horizontal)
        :type PyQt6.QtCore.Qt.Orientation
        :param color color of line (with alpha if needed)
        :type color PyQt6.QtGui.QColor
        :param padding How wide should the line be (from 0 to 1)
        :type padding float
        """
        super().__init__(*args, **kwargs)
        self._orientation = orientation
        self._lineWidth = lineWidth
        self._padding = padding
        if color:
            self._color = color
        else:
            self._color = self.palette().color(QPalette.ColorRole.Text)

    def sizeHint(self) -> QSize:
        if self._orientation == Qt.Orientation.Vertical:
            return QSize(int(self._lineWidth+2), int(0))
        else:
            return QSize(int(0), int(int(int(self._lineWidth+2))))

    def setColor(self, color):
        self._color = color
        self.update()

    def paintEvent(self, _) -> None:
        painter = QPainter(self)
        painter.setPen(QPen(self._color, self._lineWidth, Qt.PenStyle.SolidLine))
        # just draw simple line
        if self._orientation == Qt.Orientation.Horizontal:
            painter.drawLine(
                int(int(self.width())*self._padding/2),
                int(self.height()/2),
                int(int(self.width())-int(self.width())*self._padding/2),
                int(self.height()/2)
            )
        else:
            painter.drawLine(
                int(self.width()/2),
                int(int(self.height())*self._padding/2),
                int(self.width()/2),
                int(int(self.height())-int(self.height())*self._padding/2)
            )


