from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import Qt, QPoint, QRect, QTimer, QSize, QRectF
from PyQt6.QtGui import (QPainter, QColor, QPalette, QPen, QBrush, QPainterPath)
import datetime


class DepthDrillWidget(QWidget):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.initiateSizes()
        self.actualDepth = 0.0
        self.targetDepth = None
        self.drillPosition = -1
        self.drillDuration = 0

        self.drawedActualDepth = 0
        self.drawedTargetDepth = 0
        self.drawedDrillPosition = 0

        self.largeMarks = []
        self.tinyMarkCount = 5

        self.largeMarkLength = 20
        self.tinyMarkLength = 14

        self.updateTimer = QTimer()
        self.updateTimer.setInterval(33)  # Changed from 16ms (60fps) to 33ms (30fps) to reduce CPU usage
        self.updateTimer.timeout.connect(self.update)
        self.updateTimer.start()

    def paintEvent(self, _):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # painter.drawRect(int(self.rect())
        # painter.drawLine(int(self.rect().topLeft())), int(self.rect().bottomRight())
        # fill the background
        _brush = QBrush(Qt.GlobalColor.white, Qt.BrushStyle.SolidPattern)
        _brush.setColor(self.palette().color(QPalette.ColorRole.Window))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(self.rect())
        # draw actual borehole
        actualDepthToDraw = self.actualDepthLength * 0.1 + self.drawedActualDepth * 0.9
        actualBoreholeRect = QRectF(float(self.w1 + 5), float(self.h1 - 10), float(self.boreholeRadius * 2 - 10), float(actualDepthToDraw + 10))
        _color = self.palette().color(QPalette.ColorRole.Text)
        _color.setAlpha(150)
        _pen = QPen(_color, 1, Qt.PenStyle.SolidLine)
        _pen.setStyle(Qt.PenStyle.SolidLine)
        painter.setPen(_pen)
        _color.setAlpha(120)

        _brush.setColor(_color)
        painter.setBrush(_brush)

        # going to clip upper part of rounded rectangle,
        # to make top line completely flat
        painter.setClipRect(QRect(int(0), int(self.h1), int(self.width()), int(self.height()) - self.h1))
        # draw actual borehole without top-part
        painter.drawRoundedRect(actualBoreholeRect, 7, 7)

        # restore default clipping back
        painter.setClipRect(self.rect())

        self.drawedActualDepth = actualDepthToDraw

        # draw actual borehole depth caption
        _pen.setColor(self.palette().color(QPalette.ColorRole.Text))
        painter.setPen(_pen)
        textRect = QRect(int(self.w1), int(self.h1 + actualDepthToDraw), int(self.boreholeRadius * 2), int(20))
        painter.drawText(textRect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop, str(round(self.actualDepth, 1)))

        # draw target depth
        if self.targetDepth is not None:
            targetDepthToDraw = self.targetDepthLength * 0.1 + self.drawedTargetDepth * 0.9

            targetDepthRect = QRectF(float(self.w1 - 7), float(self.h1), float(self.fillerGap), float(targetDepthToDraw))
            if actualDepthToDraw > targetDepthToDraw:
                # will draw one red rect of actual depth and yellow one
                # above it as target depth
                actualDepthRect = QRectF(float(self.w1 - 7), float(self.h1), float(self.fillerGap), float(actualDepthToDraw))

                _brush.setColor(Qt.GlobalColor.red)
                painter.setBrush(_brush)
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRect(actualDepthRect)

                # draw right copy
                # targetDepthRect.setX(self.w2+7)
                actualDepthRect = QRectF(float(self.w2), float(self.h1), float(self.fillerGap), float(actualDepthToDraw))
                painter.drawRect(actualDepthRect)

            # will draw single yellow rect
            _brush.setColor(Qt.GlobalColor.yellow)
            painter.setBrush(_brush)
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRect(targetDepthRect)

            # draw right copy
            targetDepthRect = QRect(int(self.w2), int(self.h1), int(self.fillerGap), int(targetDepthToDraw))
            painter.drawRect(targetDepthRect)

            # draw target depth dotted line
            _pen = QPen(Qt.GlobalColor.yellow, 1, Qt.PenStyle.DashLine)
            painter.setPen(_pen)
            painter.drawLine(int(self.w1), int(self.h1 + targetDepthToDraw), int(self.w2), int(self.h1 + targetDepthToDraw))

            # draw text label
            _color = self.palette().color(QPalette.ColorRole.Text)
            _pen.setColor(_color)
            _pen.setStyle(Qt.PenStyle.SolidLine)
            painter.setPen(_pen)
            painter.drawText(QRect(int(0), int(self.h1 + targetDepthToDraw - 10), int(self.w1 - self.largeMarkLength-5), int(20)),
                            Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter,
                            str(round(self.targetDepth, 2)))
            # fix this' frame position
            self.drawedTargetDepth = targetDepthToDraw
        # draw left axis
        painter.translate(self.w1, self.h1)
        _pen = QPen(self.palette().color(QPalette.ColorRole.Text), 1, Qt.PenStyle.SolidLine)
        painter.setPen(_pen)
        painter.drawLine(0, 0, 0, int(self.hScale))
        painter.drawLine(int(-self.fillerGap), 0, int(-self.fillerGap), int(self.hScale))
        painter.translate(-self.w1, -self.h1)

        # draw right axis
        painter.translate(self.w2, self.h1)
        _pen = QPen(self.palette().color(QPalette.ColorRole.Text), 1, Qt.PenStyle.SolidLine)
        painter.setPen(_pen)
        painter.drawLine(0, 0, 0, int(self.hScale))
        painter.drawLine(int(self.fillerGap), 0, int(self.fillerGap), int(self.hScale))
        painter.translate(-self.w2, -self.h1)

        # calculate tiny marks spacer:
        if len(self.largeMarks)>2:
            spacer = (self.largeMarks[1][1] - self.largeMarks[0][1])/(self.tinyMarkCount+1)
            spaceDiff = (self.largeMarks[1][0] - self.largeMarks[0][0])/(self.tinyMarkCount+1)
        else:
            spacer = None
        # draw marks:
        _color = self.palette().color(QPalette.ColorRole.Text)
        _color.setAlpha(255)
        _pen.setColor(_color)
        painter.setPen(_pen)
        for i in self.largeMarks:
            # large marks
            painter.drawLine(QPoint(int(self.w2), int(i[1])), QPoint(int(self.w2+self.largeMarkLength), int(i[1])))
            painter.drawLine(QPoint(int(self.w1), int(i[1])), QPoint(int(self.w1-self.largeMarkLength), int(i[1])))

            # tinyMarks just one large mark divided by 5
            if not i[0] == self.lowerBound:
                if spacer:
                    for j in range(1, self.tinyMarkCount+1):
                        markHeight = i[1]+j*spacer
                        painter.drawLine(QPoint(int(self.w2), int(markHeight)), QPoint(int(self.w2+self.tinyMarkLength), int(markHeight)))

                        painter.drawLine(QPoint(int(self.w1), int(markHeight)), QPoint(int(self.w1-self.tinyMarkLength), int(markHeight)))

                        if spacer > 35:
                            _color.setAlpha(80)
                            _pen.setColor(_color)
                            painter.setPen(_pen)
                            markCaptionRect = QRect(int(self.w2 + self.largeMarkLength + 5), int(markHeight - 10), int(40), int(20))
                            painter.drawText(markCaptionRect,
                                             Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft,
                                             str(round(i[0]+spaceDiff*j, 2))
                                             )
                            _color.setAlpha(255)
                            _pen.setColor(_color)
                            painter.setPen(_pen)

            markCaptionRect = QRect(int(self.w2+self.largeMarkLength+5), int(i[1]-10), int(40), int(20))
            painter.drawText(markCaptionRect, Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft, str(i[0]))

        # draw filler
        fillerRect = QRect(int(self.w1 + 5), int(self.h1), int(self.boreholeRadius * 2 - 10), int(self.hScale))
        _fillerColor = self.palette().color(QPalette.ColorRole.Text)
        _fillerColor.setAlpha(7)
        _brush.setColor(_fillerColor)
        _fillerColor.setAlpha(14)
        _pen.setColor(_fillerColor)
        painter.setBrush(_brush)
        painter.setPen(_pen)
        painter.drawRect(fillerRect)

        # draw boring rod
        drillPositionToDraw = self.drillPositionLength * 0.1 + self.drawedDrillPosition * 0.9

        _color = self.palette().color(QPalette.ColorRole.Text)
        _color.setAlpha(110)

        _pen.setColor(_color)
        _pen.setStyle(Qt.PenStyle.SolidLine)
        painter.setPen(_pen)

        # _color = self.palette().color(QPalette.ColorRole.Mid)
        _color = QColor(Qt.GlobalColor.gray)
        _brush.setColor(_color.darker())
        painter.setBrush(_brush)

        boringPath = QPainterPath()
        boringHeadRect = QRectF(float(self.w1 + 5), float(self.h1+drillPositionToDraw-1), float(self.boreholeRadius * 2 - 10), float(-47))
        boringPath.addRoundedRect(boringHeadRect, 7, 7)
        boringRodPath = QPainterPath()
        boringRodPath.addRect(QRectF(float(self.w1+10), float(0), float(self.boreholeRadius*2-20), float(self.drawedDrillPosition-3+self.h1)))

        painter.drawPath(boringPath.united(boringRodPath))
        self.drawedDrillPosition = drillPositionToDraw

        # draw actual drill depth caption
        _pen.setColor(self.palette().color(QPalette.ColorRole.Text))
        painter.setPen(_pen)
        textRect = QRect(int(self.w1), int(self.h1 + drillPositionToDraw), int(self.boreholeRadius * 2), int(-47))
        painter.drawText(textRect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter, str(round(self.drillPosition, 1)))

        # draw percentage caption if there is target drill depth
        if self.targetDepth:
            textRect = QRect(0, int(self.h2), int(self.width()), int(self.height())-self.h2)
            drillPercentage = round((self.actualDepth / self.targetDepth)*100)
            indicatorText = str(drillPercentage)+'%'
            if self.drillDuration > 0:
                indicatorText += '\n' + str(datetime.timedelta(seconds=round(self.drillDuration)))
            painter.drawText(textRect,
                             Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignHCenter,
                             indicatorText)
        elif self.drillDuration > 0:
            textRect = QRect(0, int(self.h2), int(self.width()), int(self.height())-self.h2)
            painter.drawText(textRect,
                             Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignHCenter, '\n' + str(datetime.timedelta(seconds=round(self.drillDuration))))


    def resizeEvent(self, _):
        # recalculate basic geometry anchors for re-drawing
        self.h1 = self.cutterHeight + 5
        self.h2 = int(self.height()) - 60
        self.w1 = int(self.width()) / 2 - self.boreholeRadius
        self.w2 = int(self.width()) / 2 + self.boreholeRadius
        self.hScale = self.h2 - self.h1

        self.recalculateBounds()

    def recalculateBounds(self):
        """
        Lower bound is a round measure value, max of which we
        show on ruler
        """
        deepestPoint = max(self.targetDepth if self.targetDepth else 0, self.actualDepth, self.drillPosition)
        if deepestPoint < 10:
            # add to full 2 meters
            lowerBound = int(deepestPoint + 2)
            self.lowerBound = lowerBound - lowerBound % 2
        elif deepestPoint < 30:
            # add to full 5 meters
            lowerBound = int(deepestPoint + 5)
            self.lowerBound = lowerBound - lowerBound % 5
        else:
            # add to full 10 meters
            lowerBound = int(deepestPoint + 10)
            self.lowerBound = lowerBound - lowerBound % 10

        # calculate scale marks positions on rulers
        self.recalculateMarks()

        # calculate length of visible rectangles
        if self.targetDepth is not None:
            self.targetDepthLength = (self.hScale * self.targetDepth) / self.lowerBound
        self.actualDepthLength = (self.hScale * self.actualDepth) / self.lowerBound
        self.drillPositionLength = (self.hScale * self.drillPosition) / self.lowerBound

    def recalculateMarks(self):
        self.largeMarks = []
        if self.lowerBound<=5:
            # draw every meter
            for i in range(self.lowerBound+1):
                self.largeMarks.append([i, round(self.h1+(self.hScale * i) / self.lowerBound)])
            self.tinyMarkCount = 3
        elif self.lowerBound<=10:
            # draw every two meters:
            for i in range(0, self.lowerBound+1, 2):
                self.largeMarks.append([i, round(self.h1+(self.hScale * i) / self.lowerBound)])
            self.tinyMarkCount = 3
        elif self.lowerBound<=30:
            # draw every five meters:
            for i in range(0, self.lowerBound+1, 5):
                self.largeMarks.append([i, round(self.h1+(self.hScale * i) / self.lowerBound)])
            self.tinyMarkCount = 4
        else:
            # draw every ten meters
            for i in range(0, self.lowerBound + 1, 10):
                self.largeMarks.append([i, round(self.h1+(self.hScale * i) / self.lowerBound)])
            self.tinyMarkCount = 4

    def initiateSizes(self):
        self.cutterHeight = 47
        self.longMarkLength = 20
        self.shortMarkLength = 12
        self.fillerGap = 7
        self.boreholeRadius = 25

    def sizeHint(self):
        # set minimum size for the widget
        return QSize(int(164), int(360))

    def setActualDepth(self, value):
        self.actualDepth = value
        self.recalculateBounds()
        self.update()

    def setTargetDepth(self, value):
        self.targetDepth = value
        self.recalculateBounds()
        self.update()

    def setDrillPosition(self, value):
        self.drillPosition = value
        self.recalculateBounds()
        self.update()

    def setDrillDuration(self, value):
        self.drillDuration = value
        self.update()