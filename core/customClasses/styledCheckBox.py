from PyQt6 import QtWidgets, QtGui
from PyQt6.QtWidgets import (QWidget, QPushButton, QHBoxLayout, QLabel, QSizePolicy, QAbstractButton)
from PyQt6.QtCore import (Qt, QPoint, QPointF, QRect, QRectF, QTimer, QPropertyAnimation,
                          pyqtSignal, pyqtProperty, QSize, QMargins)
from PyQt6.QtGui import (QPainter, QColor, QFont, QPixmap, QPalette,
                         QPen, QBrush)
from .styledPushButton import StyledPushButton

class ToggleSlider(QWidget):
    """
    ToggleSlide - simple 2-position slider, with given color and state.
    Can be animated
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._state = False
        self._set_pinPosition(.0)
        self.pinPositionAnimation = QPropertyAnimation(self, b"_pinPosition_")
        self.pinPositionAnimation.setDuration(100)
        self.calculatePositions()
        # self.setState(False)

    def paintEvent(self, e):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        painter.setPen(Qt.PenStyle.NoPen)
        _color = self.palette().color(QPalette.ColorRole.Base)
        _brush = QBrush(_color, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)
        painter.drawRoundedRect(self.rect(),
                                int(self.height())/2, int(self.height())/2)
        if self.state():
            _color = self.palette().color(QPalette.ColorRole.Highlight)
        else:
            _color = self.palette().color(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Highlight)
        _brush = QBrush(_color, Qt.BrushStyle.SolidPattern)
        painter.setBrush(_brush)

        # Use the original center point and radius approach but with integers for sizes
        center = QPointF(self.pinFalsePosition.x()+self.pinPosition()*self.pinPath, self.pinFalsePosition.y())
        radius = int(self.pinDiameter/2)

        # Convert to QRectF for proper drawing with center and radius
        ellipseRect = QRectF(
            center.x() - radius,
            center.y() - radius,
            radius * 2,
            radius * 2
        )
        painter.drawEllipse(ellipseRect)

    def calculatePositions(self):
        # Use the original calculation but convert to integers
        self.pinDiameter = int(self.height() - 2)
        # Center the pin vertically
        self.pinFalsePosition = QPointF(int(self.pinDiameter/2 + 1), int(self.rect().center().y()))
        self.pinPath = int(self.width() - self.pinDiameter - 2)

    def resizeEvent(self, e):
        super().resizeEvent(e)
        self.calculatePositions()

    def sizeHint(self):
        return QSize(int(70), int(28))

    def pinPosition(self):
        return self._pinPosition

    def _set_pinPosition(self, pos):
        self._pinPosition = pos
        self.update()

    def state(self):
        return self._state

    def setState(self, newState):
        self.pinPositionAnimation.stop()
        self._state = newState
        self.pinPositionAnimation.setStartValue(self.pinPosition())
        if newState:
            self.pinPositionAnimation.setEndValue(1.0)
        else:
            self.pinPositionAnimation.setEndValue(0.0)

        self.pinPositionAnimation.start()

    _pinPosition_ = pyqtProperty(float, fget=pinPosition, fset=_set_pinPosition)
    __state = pyqtProperty(bool, fget=state, fset=setState)

# class StyledCheckBox(QPushButton):
class StyledCheckBox(StyledPushButton):
    """
    StyledCheckBox - custom widget with text label and iOS-style
    slider near it. Should be used like a checkbox
    """
    clicked = pyqtSignal(Qt.CheckState)
    def __init__(self, tracking=False, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setCheckable(True)
        self.tracking = tracking # if tracking == True, checkbox will not change it's state on click

        # create main Layout
        mainLayout = QHBoxLayout()
        mainLayout.setContentsMargins(QMargins(7, 4, 7, 4))
        self.setLayout(mainLayout)
        self.setAlignment(Qt.AlignmentFlag.AlignLeft)

        mainLabel = QLabel(text='')
        mainLabel.setSizePolicy(QSizePolicy.Policy.MinimumExpanding, QSizePolicy.Policy.MinimumExpanding)
        self.slider = ToggleSlider()
        self.slider.setMinimumHeight(int(28))
        self.slider.setMaximumHeight(int(35))
        self.slider.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.MinimumExpanding)

        mainLayout.addWidget(mainLabel)
        mainLayout.addWidget(self.slider)
        self.setChecked(False)

    def mousePressEvent(self, a0: QtGui.QMouseEvent) -> None:
        a0.ignore()

    def mouseReleaseEvent(self, e: QtGui.QMouseEvent) -> None:
        if self.rect().contains(e.pos()):
            if not self.tracking:
                self.setChecked(not self.isChecked())
                self.slider.setState(self.isChecked())
            else:
                # Convert boolean to Qt.CheckState
                new_state = Qt.CheckState.Checked if not self.isChecked() else Qt.CheckState.Unchecked
                self.clicked.emit(new_state)
        else:
            e.ignore()

    def resizeEvent(self, e):
        # self.slider.setFixedHeight(int(self.height())*0.8)
        # self.setPadding([2, self.slider.width(), 2, 10])
        self.setPadding([2, 2, 2, 10])
        # Do not force fixed width in resize event
        self.slider.setMinimumHeight(int(28))
        self.slider.setMaximumHeight(int(35))

    def setChecked(self, a0: bool) -> None:
        super().setChecked(a0)
        self.slider.setState(a0)


    def sizeHint(self) -> QSize:
        superSize = super().sizeHint()
        return QSize(int(superSize.width() + self.slider.width() + self._padding[1] + self._padding[3] + 5),
                     int(max(superSize.height(), self.slider.height()+6)))
