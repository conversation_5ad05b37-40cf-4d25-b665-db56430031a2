# import PyQt6
from PyQt6.QtGui import QPalette, QColor
from PyQt6.QtCore import Qt
from enum import Enum

darkPalette = QPalette()
# create dark theme
darkPalette.setColor(QPalette.ColorRole.Window, QColor('#171717'))
darkPalette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
darkPalette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.WindowText, QColor(255, 255, 255).darker(300))
darkPalette.setColor(QPalette.ColorRole.Base, QColor(30, 30, 30))
darkPalette.setColor(QPalette.ColorRole.AlternateBase, QColor(152, 152, 152))
darkPalette.setColor(QPalette.ColorRole.ToolTipBase, Qt.GlobalColor.white)
darkPalette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.black)
darkPalette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.white)
darkPalette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Text, QColor(180, 180, 180))
darkPalette.setColor(QPalette.ColorRole.Button, QColor(50, 50, 50))
darkPalette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, QColor(80, 80, 80))
darkPalette.setColor(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, QColor(80, 80, 80))
darkPalette.setColor(QPalette.ColorGroup.Inactive, QPalette.ColorRole.ButtonText, QColor(40, 40, 40))
darkPalette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.white)
darkPalette.setColor(QPalette.ColorRole.BrightText, QColor(55, 55, 55))
darkPalette.setColor(QPalette.ColorRole.Link, QColor(65, 156, 255))
darkPalette.setColor(QPalette.ColorRole.Highlight, QColor(58, 136, 209, 255))
darkPalette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Highlight, QColor(116, 139, 161, 255))
darkPalette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.black)
darkPalette.setColor(QPalette.ColorRole.Dark, QColor(191, 191, 191))
darkPalette.setColor(QPalette.ColorRole.Midlight, QColor(35, 35, 35))
darkPalette.setColor(QPalette.ColorRole.Light, QColor(20, 20, 20))


class ColorSet(Enum):
    ledGreenColor = QColor(47, 192, 44, 255)
    ledRedColor = QColor(255, 86, 86, 255)
    ledYellowColor = QColor(255, 207, 37, 255)

    buttonGreenColor = QColor(47, 192, 44, 255)
    buttonRedColor = QColor(255, 86, 86, 255)
    buttonYellowColor = QColor(255, 207, 37, 255)
    buttonBlueColor = QColor(58, 136, 209, 255)
    buttonRegularColor = QColor(50, 50, 50)
    buttonOrangeColor = QColor("#ff6700")

    textColor = QColor(255, 255, 255)
    dimmedTextColor = QColor(200, 200, 200)
    backgroundColor = QColor("#171717")

    darkRedColor = QColor("#660708")
    warningRedBlinkingColor = QColor(255, 0, 0)
