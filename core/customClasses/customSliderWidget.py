from PyQt6 import QtGui
from PyQt6.QtWidgets import (QTableWidgetItem, QWidget, QGroupBox, QSizePolicy, QMessageBox,
                             QBoxLayout, QVBoxLayout, QHBoxLayout, QPushButton, QApplication,
                             QLabel, QScrollArea, QScroller, QScrollerProperties, QStackedLayout,
                             QComboBox, QFrame, QGridLayout, QLineEdit, QCheckBox)
from PyQt6.QtCore import (Qt, QPoint, QPointF, QRect, QTimer, QPropertyAnimation,
                          QUrl, pyqtSignal, pyqtProperty, QSize, QMargins, QRectF)
from PyQt6.QtGui import (QFontMetrics, QPainter, QColor, QFont, QPixmap, QPalette,
                         QPen, QBrush, QIcon, QPainterPath, QPainterPathStroker)

from .styledPushButton import Styled<PERSON>ush<PERSON>utton
from .darkPalette import ColorSet
from time import sleep

class SliderBaseWidget(QWidget):
    """
    Bare class for slider, with scale, thumb and track only
    """
    valueChange = pyqtSignal(float)

    def __init__(self,
                minValue = 0,
                maxValue = 100,
                *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.minValue = minValue
        self.maxValue = maxValue

        self.orientation = None
        self._value = minValue
        self._actualValue = None
        self.valueChange.emit(self.minValue)
        self.actualValueWidth = 0
        self.stepWidth = 0


    def sizeHint(self):
        return QSize(int(200), int(95))

    def resizeEvent(self, a0: QtGui.QResizeEvent) -> None:
        if int(self.width()) > int(self.height()):
            self.orientation = Qt.Orientation.Horizontal
            self.thumbSize = QSize(int(31), int(45))
            self.w1 = int(self.thumbSize.width())
            self.w2 = int(self.width()) - self.w1*2
            self.h1 = 41
            self.h2 = 57
            self.h3 = 89
            self.trackWidth = 8
            self.rowWidth = 7
            self.smallMarks = 12
            self.largeMarks = 17
            self.calcThumbCenter()

            self.lastDragPosition = None
            self.fineDrag = False
            self.roughDrag = False

            self.calculateActualValueWidth() # when resize, actual value rect should be recalculated

            (self.groundMark, self.step, self.stepWidth, self.gapStep) = self.recalculateBounds()
        else:
            self.orientation = Qt.Orientation.Vertical

        return super().resizeEvent(a0)

    def calculateActualValueWidth(self):
        if self.actualValue() is not None:
            # value_to_draw is clamped between self.minValue and self.maxValue
            value_to_draw = min(max(self.actualValue(), self.minValue), self.maxValue)
            self.actualValueWidth = self.w2 * (value_to_draw - self.minValue)/(self.maxValue - self.minValue)
            if self.actualValueWidth > self.w2:
                self.actualValueWidth = self.w2
        else:
            self.actualValueWidth = 0

    def paintEvent(self, e):
        painter = QPainter(self)
        # painter.setRenderHint(QPainter.Antialiasing)
        _brush = QBrush(Qt.GlobalColor.white, Qt.BrushStyle.SolidPattern)
        _pen = QPen(Qt.GlobalColor.white, 1, Qt.PenStyle.SolidLine)
        if self.orientation == Qt.Orientation.Horizontal:
            # draw horizontal slider

            # draw track
            truckRect = QRect(int(self.w1), int(self.h2), int(self.w2), int(self.trackWidth))
            _bgcolor = self.palette().color(QPalette.ColorRole.Dark)
            _strokeColor = self.palette().color(QPalette.ColorRole.Window)
            _strokeColor.setAlpha(120)

            _brush.setColor(_bgcolor)
            _pen.setColor(_strokeColor)
            painter.setBrush(_brush)
            painter.setPen(_pen)

            painter.drawRoundedRect(truckRect, 2,2)

            # draw thumb
            _bgcolor = self.palette().color(QPalette.ColorRole.Highlight)
            _brush.setColor(_bgcolor)
            painter.setBrush(_brush)

            thumbRect = QRect(
                int(self.thumbCenter - self.thumbSize.width()/2),
                int(self.h3 - self.thumbSize.height()),
                int(self.thumbSize.width()),
                int(self.thumbSize.height())
            )
            painter.drawRoundedRect(thumbRect, 3, 3)
            # draw marks on thumb
            _pen.setWidth(3)
            painter.setPen(_pen)
            painter.drawLine(int(self.thumbCenter), int(thumbRect.top()), int(self.thumbCenter), int(self.h2))
            _strokeColor = self.palette().color(QPalette.ColorRole.Text)
            _pen.setColor(_strokeColor)
            _pen.setWidth(1)
            painter.setPen(_pen)
            # draw central top mark
            painter.drawLine(int(self.thumbCenter), int(thumbRect.top()), int(self.thumbCenter), int(self.h2))

            # draw lower touch marks
            _strokeColor.setAlpha(120)
            _pen.setColor(_strokeColor)
            painter.setPen(_pen)
            gap = 4
            bottom = thumbRect.bottom()-3
            top = thumbRect.center().y()+2
            for i in range(-2,3):
                x = self.thumbCenter+gap*i
                painter.drawLine(x, bottom, x, top )

            # draw actual value rect:
            _bgcolor = self.palette().color(QPalette.ColorRole.Highlight)
            invertedColor = QColor(255-_bgcolor.red(), 255-_bgcolor.green(), 255-_bgcolor.blue())
            _brush.setColor(invertedColor)
            painter.setBrush(_brush)
            painter.setPen(Qt.PenStyle.NoPen)
            painter.translate(self.w1, self.h1)
            if self.actualValueWidth:
                actualValueRect = QRect(int(0), int(0), int(self.actualValueWidth), int(-self.rowWidth))
                painter.drawRect(actualValueRect)
            # draw scale
            _strokeColor = self.palette().color(QPalette.ColorRole.Text)
            _pen.setColor(_strokeColor)
            painter.setPen(_pen)
            # scale line
            painter.drawLine(int(0), int(0), int(self.w2), int(0))
            painter.drawLine(0, int(-self.rowWidth), int(self.w2), int(-self.rowWidth))

            # draw start mark
            _font = QFont()
            startLabel = str(round(self.minValue, 2))
            fontWidth = QFontMetrics(_font).horizontalAdvance(startLabel)*1.5
            fontHeight = QFontMetrics(_font).height()*1.2
            startLabelRect = QRect(int(-fontWidth/2), int(-self.largeMarks-fontHeight), int(fontWidth), int(fontHeight))
            painter.drawText(startLabelRect,
                            Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop,
                            startLabel)
            painter.drawLine(0, 0, 0, int(int(-self.largeMarks)))

            # draw final mark
            finalLabel = str(round(self.maxValue, 2))
            fontWidth = QFontMetrics(_font).horizontalAdvance(finalLabel)*1.5
            fontHeight = QFontMetrics(_font).height()*1.2
            finallabelRect = QRect(int(self.w2-fontWidth/2), int(-self.largeMarks-fontHeight), int(fontWidth), int(fontHeight))
            painter.drawText(finallabelRect,
                            Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop,
                            finalLabel)
            painter.drawLine(int(self.w2), 0, int(self.w2), int(-self.largeMarks))

            # scale small marks
            groundMarkPosition = 0 - (self.stepWidth*(self.minValue-self.groundMark))/self.step
            # startMark = groundMarkPosition + self.stepWidth
            startMark = groundMarkPosition + (self.minValue - self.groundMark)
            x = 0
            n = 0
            while x < self.w2:
                x = startMark + self.stepWidth*n

                if x>0 and x<=self.w2:
                    if n % self.gapStep == 0:
                        h = -self.largeMarks
                        if n>=0:
                            value = str(round(self.groundMark+ (n)*self.step, 2))
                            fontWidth = QFontMetrics(_font).horizontalAdvance(value)
                            fontHeight = QFontMetrics(_font).height()*1.2
                            markRect = QRect(int(x-fontWidth/2), int(h-fontHeight), int(fontWidth), int(fontHeight))
                            if not (markRect.intersects(startLabelRect) or \
                                        markRect.intersects(finallabelRect)):
                                painter.drawText(markRect,
                                Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop,
                                value)
                    else:
                        h = -self.smallMarks
                    painter.drawLine(int(x), 0, int(x), int(h))
                n += 1

            painter.translate(-self.w1, -self.h1)

        else:
            # draw vertical slider
            print('Not implemented yet')

    def mouseMoveEvent(self, a0: QtGui.QMouseEvent) -> None:
        if self.lastDragPosition is not None:
            if self.orientation == Qt.Orientation.Horizontal:
                # horizontal slider move
                deltaX = a0.pos().x() - int(self.lastDragPosition.x())
                # calculate value delta from cursor movement
                delta = 0
                if self.fineDrag:
                    delta = deltaX*0.2
                elif self.roughDrag:
                    delta = deltaX
                valueDelta = (delta*(self.maxValue-self.minValue)/self.w2)
                newValue = self.value() + valueDelta
                # bounds check
                if newValue > self.maxValue:
                    newValue = self.maxValue
                elif newValue < self.minValue:
                    newValue = self.minValue
                self.setValue(newValue)
                # emit signal
                self.valueChange.emit(newValue)
            else:
                # vertical slider move
                print("Not implemented yet")

            self.lastDragPosition = a0.pos()
        else:
            self.lastDragPosition = a0.pos()

    def mousePressEvent(self, a0: QtGui.QMouseEvent) -> None:
        # hitbox
        if self.orientation == Qt.Orientation.Horizontal:
            thumbHitBox = QRect(
                int(self.thumbCenter - self.thumbSize.width()),
                int(self.h1),
                int(self.thumbSize.width() * 2),
                int(self.height() - self.h1)
            )
            if thumbHitBox.contains(a0.pos()):
                self.roughDrag = True
                self.fineDrag = False
            else:
                self.roughDrag = False
                self.fineDrag = True

            self.lastDragPosition = a0.pos()
        else:
            print('Not implemented yet')

    def mouseReleaseEvent(self, a0: QtGui.QMouseEvent) -> None:
        self.lastDragPosition = None
        self.roughDrag = False
        self.fineDrag = False

    def recalculateBounds(self):
        """
        Lower bound is a round measure value, max of which we
        show on ruler
        """
        minPixWidth = 7
        maxPixWidth = 18
        kRow = (0.1, 0.25, 0.5)
        multiplyer = 1
        _range = self.maxValue - self.minValue
        mw = .0
        k = 0
        tryNumber = 0
        found = False
        gapStep = 0
        while not found:
            tryNumber += 1
            if tryNumber > 100:
                raise ValueError(
                    "Cannot calculate slider ruler marks. Please change widget size or bounds"
                    )
            newArray = [a* multiplyer for a in kRow]
            for i in range(3):
                k = newArray[i]
                mw = (self.w2 * k)/_range
                if mw > maxPixWidth and i == 0:
                    multiplyer *= 0.1
                    break
                if minPixWidth <= mw <= maxPixWidth:
                    found = True
                    if i == 0:
                        gapStep = 10
                    elif i == 1:
                        gapStep = 4
                    elif i == 2:
                        gapStep = 10
                    else:
                        gapStep = 10
                    break

                elif mw < minPixWidth and i == 2:
                    multiplyer *= 10
                    break

        # find ground mark
        groundMark = self.minValue - (self.minValue%k)
        return(groundMark, k, mw, gapStep)

    def calcThumbCenter(self):
        value_to_draw = min(max(self.value(), self.minValue), self.maxValue)
        self.thumbCenter = round(self.w1+self.w2*(value_to_draw-self.minValue)/(self.maxValue-self.minValue)  )

    def setValue(self, value):
        self._value = value
        self.calcThumbCenter()
        self.update()

    def value(self):
        return self._value

    def setActualValue(self, value):
        self._actualValue = value
        self.calculateActualValueWidth()

    def actualValue(self):
        return self._actualValue

    sliderValue = pyqtProperty(float, fset=setValue, fget=value)
    actualValue_ = pyqtProperty(float, fset=setActualValue, fget=actualValue)


class CustomSlider(QWidget):
    """
    Slider gives you ability to set value by moving thumb alongside track.
    There is also abitity to show actual value on the scale.
    Submitting via the 'submit' button
    """
    valueSubmited = pyqtSignal(float)
    valuePushed = pyqtSignal(float)
    def __init__(self,
                 caption = '',
                 measureUnits = '%',
                 minValue = 0,
                 maxValue = 100,
                 initialValue = 0,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._caption = caption
        self._measureUnits = measureUnits
        self._value = initialValue
        self._minValue = minValue
        self._maxValue = maxValue

        # create layouts
        mainLayout = QHBoxLayout()
        sliderAndCaptionLayout = QVBoxLayout()
        captionLayout = QHBoxLayout()
        buttonsLayout = QVBoxLayout()

        # create widgets
        self.captionLabel = QLabel()

        self.submitButton = StyledPushButton(text=self.tr("Apply", "send data from this widget to system"))
        self.submitButton.pressed.connect(self.submit)

        self.pushButton = StyledPushButton(text=self.tr("Save", "Button caption that push value into settings file"))
        self.pushButton.pressed.connect(self.push)
        # equalize button and caption heights
        self.captionLabel.setMinimumHeight(int(self.submitButton.sizeHint().height()))
        self.captionLabel.setSizePolicy(QSizePolicy.Policy.MinimumExpanding,
                                        QSizePolicy.Policy.MinimumExpanding)
        self.submitButton.setSizePolicy(QSizePolicy.Policy.Minimum,
                                        QSizePolicy.Policy.MinimumExpanding)
        self.pushButton.setSizePolicy(QSizePolicy.Policy.Minimum,
                                        QSizePolicy.Policy.MinimumExpanding)

        self.slider = SliderBaseWidget(minValue=minValue,
        maxValue=maxValue)

        self.updateCaption()

        # STYLING
        for button in (self.pushButton, self.submitButton):
            button.setColor(ColorSet.buttonRegularColor.value)
            button.setFontSize(15)


        # wiring
        self.slider.valueChange.connect(self.updateValue)

        # composing
        self.setLayout(mainLayout)

        captionLayout.addWidget(self.captionLabel)
        # captionLayout.addWidget(self.submitButton)

        # mainLayout.addLayout(captionLayout)
        # mainLayout.addStretch(1)
        # mainLayout.addWidget(self.slider)

        mainLayout.addLayout(sliderAndCaptionLayout)
        mainLayout.addLayout(buttonsLayout)

        sliderAndCaptionLayout.addLayout(captionLayout)
        sliderAndCaptionLayout.addWidget(self.slider)

        buttonsLayout.addWidget(self.submitButton)
        buttonsLayout.addWidget(self.pushButton)

    def submit(self):
        self.valueSubmited.emit(self._value)
        self.submitButton.setEnabled(False)
        self.update()

    def push(self):
        self.submit()
        self.valuePushed.emit(self._value)
        self.pushButton.setEnabled(False)

    def updateValue(self, value):
        self._value = value
        self.updateCaption()
        self.submitButton.setEnabled(True)
        self.pushButton.setEnabled(True)

    def updateCaption(self):
        _font = QFont()
        _font.setPixelSize(16)  # Reduced font size from 20 to 16
        self.captionLabel.setFont(_font)
        text = '{}: <font size=4> {:.2f} {}</font>'.format(  # Reduced font size from 5 to 4
            self._caption,
            round(self._value, 2),
            self._measureUnits
        )
        if self.actualValue():
            highlightColor = self.palette().color(QPalette.ColorRole.Highlight)
            yellowColor = QColor(*tuple(255-x for x in (highlightColor.getRgb())))
            text += '<font size=4 color="{}" >  [ {:.2f} {} ]</font > '.format(  # Reduced font size from 5 to 4 and reduced spacing
                yellowColor.name(),
            round(self.actualValue(), 2),
            self._measureUnits
            )
        self.captionLabel.setText(text)

    def setActualValue(self, value):
        self.slider.setActualValue(value)
        self.updateCaption()
        self.slider.update()

    def actualValue(self):
        return self.slider.actualValue()

    def setValue(self, value):
        self.slider.setValue(value)
        self._value = value
        self.slider.update()
        self.updateCaption()

    def value(self):
        return self._value

    actualValue_ = pyqtProperty(float, fset=setActualValue, fget=actualValue)


