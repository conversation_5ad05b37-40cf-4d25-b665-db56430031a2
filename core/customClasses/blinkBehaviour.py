from PyQt6.QtCore import pyqtProperty, pyqtSignal, QObject, QTimer
from PyQt6.QtGui import QPalet<PERSON>, QColor

class BlinkingBehaviour(QObject):
    """
    This protocol give widget ability to blink by
    defining some properties and methods, changing
    main color self._color
    self.setBlinkingFrequency(msec: int) is used for changing frequency
    self.setBlinking(shouldBling: bool) is used for controlling blinking
    OBJECT SHOULD CONTENT self._color ATTRIBUTE
    """
    def __init__(self, blinking=False, blinkColor=None, blinkFreq=500, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # blinking attributes

        self._blinking = False
        self._blinkUpState = False

        # blinking timer
        self._blinkTimer = QTimer()
        self._blinkTimer.setInterval(500)
        self._blinkTimer.timeout.connect(self.toggleBlinkState)

        # colors
        self._originalColor = None
        self._blinkColor = blinkColor

        self.two_colors = False
        self._second_originalColor = None

    def setBlinkingFrequency(self, msec):
        self._blinkTimer.setInterval(msec)

    def setBlinkColor(self, newColor):
        self._blinkColor = newColor

    def toggleBlinkState(self):
        if self._originalColor is None:
            self._originalColor = self._color
        if self._second_originalColor is None and self.two_colors:
            self._second_originalColor = self._second_color
        if self._blinkColor is None:
            if sum((self._originalColor.red(),
                    self._originalColor.green(),
                    self._originalColor.blue())) < 380:
                # if it's a dark color, then should be lighter
                self._blinkColor = self._color.lighter(300)
            else:
                # otherwise should be darker
                self._blinkColor = self._color.darker(300)

        if self._blinkUpState:
            self._color = self._originalColor
            if self.two_colors:
                self._second_color = self._second_originalColor
        else:
            self._color = self._blinkColor
            if self.two_colors:
                self._second_color = self._blinkColor
        self._blinkUpState = not self._blinkUpState
        self.update()

    def setBlinking(self, value):
        # check if self has _color attribute
        if not self.isBlinking() == value:
            if not hasattr(self, "_color"):
                raise ValueError("""Object {} inherit BlinkingBehaviour,
                but it hasn't _color attribute. Pls create one""".format(
                    self
                ))
            if hasattr(self, "_second_color"):
                self.two_colors = True

            self._blinking = value
            if not value:
                self._blinkTimer.stop()
                if self._blinkUpState:
                    self.toggleBlinkState()
            else:
                self._blinkTimer.start()

    def isBlinking(self):
        return self._blinking


    blinking_ = pyqtProperty(type=bool, fget=isBlinking, fset=setBlinking)
