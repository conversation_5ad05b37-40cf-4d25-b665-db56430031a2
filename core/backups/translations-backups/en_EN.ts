<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US" sourcelanguage="en">
<context>
    <name>AngleIndicatorWidget</name>
    <message>
        <location filename="../gui_modules.py" line="944"/>
        <source>Across: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="945"/>
        <source>Along: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="951"/>
        <source>Tilt</source>
        <translation>Tilt</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="972"/>
        <source>Along</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="985"/>
        <source>Across</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AuthButton</name>
    <message>
        <location filename="../gui_modules.py" line="2786"/>
        <source>Login</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2787"/>
        <source>Logout</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2861"/>
        <source>login</source>
        <translation>Login</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2862"/>
        <source>password</source>
        <translation></translation>
    </message>
</context>
<context>
    <name>ButtonInGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="52"/>
        <source>Front</source>
        <translation>Front</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="53"/>
        <source>Rear</source>
        <translation>Rear</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="54"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="55"/>
        <source>Birdview</source>
        <translation>Birdview</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="58"/>
        <source>Drill</source>
        <translation>Drill</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="59"/>
        <source>Propel</source>
        <translation>Propel</translation>
    </message>
</context>
<context>
    <name>ConditionalTextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="194"/>
        <source>Maneuver in process</source>
        <translation>Maneuver in process</translation>
    </message>
</context>
<context>
    <name>ControllerPanel</name>
    <message>
        <location filename="../ext_translations.tri" line="3"/>
        <source>Driving</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="16"/>
        <source>Right</source>
        <translation>Right</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="15"/>
        <source>Left</source>
        <translation>Left</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="6"/>
        <source>           pull extend
     Jacks</source>
        <translation>           pull extend
     Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="7"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="8"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="24"/>
        <source>Feed</source>
        <translation>Pull Down</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="10"/>
        <source>Rotating</source>
        <translation>Rotating</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="26"/>
        <source>Force</source>
        <translation>Force</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="12"/>
        <source>Compressor</source>
        <translation>Compressor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="13"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="14"/>
        <source>Rear</source>
        <translation>Rear</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="17"/>
        <source>Tower Tilt</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="18"/>
        <source>Tilt</source>
        <translation>Tilt</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="19"/>
        <source>Upper Pin</source>
        <translation type="unfinished">Upper Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="20"/>
        <source>Lower Pin</source>
        <translation>Lower Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="21"/>
        <source>  Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="22"/>
        <source>Extention</source>
        <translation>Extention</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="23"/>
        <source>Manipulator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="25"/>
        <source>Turning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="27"/>
        <source>clamp  offset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="28"/>
        <source>   Fork</source>
        <translation>Fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="29"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="30"/>
        <source>Supply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="31"/>
        <source>Angle</source>
        <translation>Angle</translation>
    </message>
</context>
<context>
    <name>CustomSlider</name>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="354"/>
        <source>Apply</source>
        <comment>send data from this widget to system</comment>
        <translation>Apply</translation>
    </message>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="357"/>
        <source>Save</source>
        <comment>Button caption that push value into settings file</comment>
        <translation>Save</translation>
    </message>
</context>
<context>
    <name>CustomSliderWrapper</name>
    <message>
        <location filename="../ext_translations.tri" line="46"/>
        <source>Feed pressure</source>
        <translation>Pull Down Pressure</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="47"/>
        <source>bar</source>
        <translation>Bar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="48"/>
        <source>Rotation speed</source>
        <translation>Rotation speed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="49"/>
        <source>rpm</source>
        <translation>RPM</translation>
    </message>
</context>
<context>
    <name>DialIndicator</name>
    <message>
        <location filename="../ext_translations.tri" line="33"/>
        <source>Revolutions
(revs/min) x10</source>
        <translation>RPM (revs/min) x10</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="34"/>
        <source>Air
pressure (bar)</source>
        <translation>Air pressure (bar)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="36"/>
        <source>Rotating
pressure (psi)</source>
        <translation>Rotation pressure (psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="37"/>
        <source>Drill Feed
pressure (psi)</source>
        <translation>Pull down Pressure (psi)</translation>
    </message>
</context>
<context>
    <name>DrillJoystickWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="64"/>
        <source>Driving</source>
        <translation>Driving</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="65"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="66"/>
        <source>Retract</source>
        <translation>Retract</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="67"/>
        <source>Extend</source>
        <translation>extend</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="68"/>
        <source>Right track</source>
        <translation>right track</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="69"/>
        <source>Left track</source>
        <translation>left track</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="70"/>
        <source>Back</source>
        <translation>Back</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="71"/>
        <source>Forth</source>
        <translation>Forward</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="73"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="74"/>
        <source>Feed force</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="75"/>
        <source>Rotation</source>
        <translation>Rotation</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="78"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="79"/>
        <source>Open</source>
        <translation>Open</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="80"/>
        <source>Close</source>
        <translation>Close</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="110"/>
        <source>Feed</source>
        <translation>Feed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="111"/>
        <source>Up</source>
        <translation>Up</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="112"/>
        <source>Down</source>
        <translation>Down</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="113"/>
        <source>Compressor</source>
        <translation>Compressor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="114"/>
        <source>On</source>
        <translation>On</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="115"/>
        <source>Off</source>
        <translation>Off</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="88"/>
        <source>Leveling</source>
        <translation>Leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="89"/>
        <source>All jacks</source>
        <translation>All Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="90"/>
        <source>Rear</source>
        <translation>Rear</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="92"/>
        <source>Tower</source>
        <translation>Tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="93"/>
        <source>Inclined Pin</source>
        <translation>Inclined Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="94"/>
        <source>Tilt</source>
        <translation>Tilt</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="97"/>
        <source>Vertical Pin</source>
        <translation>Vertical Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="99"/>
        <source>Wrench</source>
        <translation>Wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="100"/>
        <source>Release</source>
        <translation>Release</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="101"/>
        <source>Turn</source>
        <translation>Turn</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="102"/>
        <source>Stow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="103"/>
        <source>Swing in</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="104"/>
        <source>Fork</source>
        <translation>Fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="105"/>
        <source>Stop</source>
        <translation>Stop</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="107"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="108"/>
        <source>Index</source>
        <translation>Index</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="76"/>
        <source>CCW</source>
        <translation>CCW</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="77"/>
        <source>CW</source>
        <translation>CW</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="95"/>
        <source>Raise</source>
        <translation>Raise</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="96"/>
        <source>Lower</source>
        <translation>Lower</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="46"/>
        <source>Red button</source>
        <translation>Red button</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="49"/>
        <source>Green button</source>
        <translation>Green button</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="129"/>
        <source>Red and Green Buttons</source>
        <translation>Red and Green Buttons</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="132"/>
        <source>Knob widget</source>
        <translation>Knob widget</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="135"/>
        <source>Left Joystick</source>
        <translation>Left Joystick</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="138"/>
        <source>Center Joystick</source>
        <translation>Center Joystick</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="141"/>
        <source>Right Joystick</source>
        <translation>Right Joystick</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="144"/>
        <source>Tumbler Widget</source>
        <translation></translation>
    </message>
</context>
<context>
    <name>DropActionButton</name>
    <message>
        <location filename="../gui_modules.py" line="3181"/>
        <source>Confirm current task cancelling</source>
        <translation>Confirm current task cancelling</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3190"/>
        <source>Permitted to qualified Engineers or Developers only</source>
        <translation>Permitted to qualified Engineers or Developers only</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3192"/>
        <source>Wrong password</source>
        <translation>Wrong password</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3242"/>
        <source>Unusual Action</source>
        <translation>Unusual Action</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3242"/>
        <source>
                                            Drilling in the process, can&apos;t cancel task right now.
                                            Enter password if still want to proceed
                                            </source>
        <translation>
                                            Drilling in the process, can&apos;t cancel task right now.
                                            Enter password if still want to proceed
                                            </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="39"/>
        <source>Cancel
task</source>
        <translation>Cancel task</translation>
    </message>
</context>
<context>
    <name>DropDrillError</name>
    <message>
        <location filename="../ext_translations.tri" line="40"/>
        <source>Clear
error</source>
        <translation>Clear error</translation>
    </message>
</context>
<context>
    <name>DropTailingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="41"/>
        <source>Clear
tailing</source>
        <translation>Clear boundaries</translation>
    </message>
</context>
<context>
    <name>EmergencySetter</name>
    <message>
        <location filename="../gui_modules.py" line="223"/>
        <source>DENY MOVEMENT</source>
        <translation>DENY MOVEMENT</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="230"/>
        <source>PERMIT MOVEMENT</source>
        <translation>PERMIT MOVEMENT</translation>
    </message>
</context>
<context>
    <name>FinishBuildupButton</name>
    <message>
        <location filename="../gui_modules.py" line="2198"/>
        <source>Attention!
Make sure that the new rod is extended
Fork and key is retracted</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2200"/>
        <source>Attention!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2225"/>
        <source>End
Extending</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FinishHoleButton</name>
    <message>
        <location filename="../ext_translations.tri" line="42"/>
        <source>Finish
drilling</source>
        <translation>Finish drilling</translation>
    </message>
</context>
<context>
    <name>FinishMovingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="43"/>
        <source>Finish
moving</source>
        <translation>Finish moving</translation>
    </message>
</context>
<context>
    <name>FinishStowButton</name>
    <message>
        <location filename="../gui_modules.py" line="2274"/>
        <source>Attention!
Make sure the boring rod is not in the borehole,
Fork and Key are retracted away!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2275"/>
        <source>Attention!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2300"/>
        <source>Finish
Stow</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>HardEmergencyButton</name>
    <message>
        <location filename="../gui_modules.py" line="2082"/>
        <source>Emergency
Stop</source>
        <translation>Emergency
Stop</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2091"/>
        <source>Continue
working</source>
        <translation>Continue
working</translation>
    </message>
</context>
<context>
    <name>JoystickWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="635"/>
        <source>Top Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="636"/>
        <source>Bottom Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="637"/>
        <source>0.0%</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="641"/>
        <source>Nonlinear</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>KnobViewWithCaption</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="520"/>
        <source>Nonlinear</source>
        <comment>Toggle Switch caption</comment>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LogoutWorker</name>
    <message>
        <location filename="../gui_modules.py" line="2927"/>
        <source>Can&apos;t Authorize user
errror: </source>
        <translation>Can&apos;t Authorize user
error: </translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2931"/>
        <source>Login Error</source>
        <translation>Login Error</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2939"/>
        <source>Login field should not be empty</source>
        <translation>Login field should not be empty</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2940"/>
        <source>Empty Login Field</source>
        <translation>Empty Login Field</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2946"/>
        <source>Password field should not be empty</source>
        <translation>Password field should not be empty</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2947"/>
        <source>Empty Password Field</source>
        <translation>Empty Password Field</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2981"/>
        <source>Can&apos;t logout user
errror: </source>
        <translation>Can&apos;t logout user
error: </translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2985"/>
        <source>Logout Error</source>
        <translation>Logout Error</translation>
    </message>
</context>
<context>
    <name>MachineStateManagement</name>
    <message>
        <location filename="../gui_modules.py" line="1709"/>
        <source>Unlock</source>
        <translation>Unlock</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1722"/>
        <source>Lock</source>
        <translation>Lock</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1739"/>
        <source>Commit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1746"/>
        <source>Cancel</source>
        <translation>Cancel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="142"/>
        <source>Idle</source>
        <translation>Idle</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="144"/>
        <source>Driving</source>
        <translation>Driving</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="182"/>
        <source>Leveling</source>
        <translation>Leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="146"/>
        <source>Tower tilt</source>
        <translation>Tower tilt</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="165"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="148"/>
        <source>Shaft Build-up</source>
        <translation>Add rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="149"/>
        <source>Shaft stow</source>
        <translation>Remove rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="150"/>
        <source>Grounding</source>
        <translation>Grounding</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="151"/>
        <source>Wait for Remote</source>
        <translation>Wait for Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="152"/>
        <source>Prepare for Remote</source>
        <translation>Prepare for Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="153"/>
        <source>In Remote</source>
        <translation>In remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="154"/>
        <source>Finishing Remote</source>
        <translation>Finishing Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="155"/>
        <source>Waiting after leveling</source>
        <translation>Waiting after leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="156"/>
        <source>String restoring</source>
        <translation>String restoring</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="157"/>
        <source>Waiting before leveling</source>
        <translation>Waiting before leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="158"/>
        <source>Failure</source>
        <translation>Failure</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="159"/>
        <source>Locking</source>
        <translation>Locking</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="160"/>
        <source>Unlocking</source>
        <translation>Unlocking</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="161"/>
        <source>Arm closing</source>
        <translation>Arm closing</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="162"/>
        <source>Tilt Regulation</source>
        <translation>Tilt Regulation</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="163"/>
        <source>Touchdown</source>
        <translation>Touchdown</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="164"/>
        <source>Overburden pass</source>
        <translation>Overburden pass</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="166"/>
        <source>Hard Rot</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="167"/>
        <source>Pullup</source>
        <translation>Pullup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="168"/>
        <source>After Pullup</source>
        <translation>After Pullup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="169"/>
        <source>String Raising</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="170"/>
        <source>Waiting after drill</source>
        <translation>Waiting after drill</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="171"/>
        <source>Pass soft</source>
        <translation>Pass soft</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="172"/>
        <source>Tower</source>
        <translation>Tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="173"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="174"/>
        <source>Opening</source>
        <translation>Opening</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="176"/>
        <source>Closing</source>
        <translation>Closing</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="177"/>
        <source>Close</source>
        <translation>Close</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="178"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="179"/>
        <source>Pulling</source>
        <translation>Pulling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="180"/>
        <source>Pulled</source>
        <translation>Pulled</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="181"/>
        <source>Restoring pulled</source>
        <translation>Restoring pulled</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="183"/>
        <source>Final Leveling</source>
        <translation>Final Leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="184"/>
        <source>Holding</source>
        <translation>Holding</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="185"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="186"/>
        <source>Carousel opening</source>
        <translation>Carousel opening</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="187"/>
        <source>Carousel closing</source>
        <translation>Carousel closing</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="188"/>
        <source>Carousel turning cw</source>
        <translation>Carousel turning cw</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="189"/>
        <source>Carousel turning ccw</source>
        <translation>Carousel turning ccw</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="175"/>
        <source>Оpen</source>
        <translation>Оpen</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="141"/>
        <source>Main State</source>
        <translation>Main State</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="143"/>
        <source>Init</source>
        <translation>Init</translation>
    </message>
</context>
<context>
    <name>MessageLogs</name>
    <message>
        <location filename="../ext_translations.tri" line="196"/>
        <source>100000101</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="197"/>
        <source>fucking_code_123</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MessagesWidgetMaximized</name>
    <message>
        <location filename="../gui_modules.py" line="1308"/>
        <source>Message Log</source>
        <translation>Message Log</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1323"/>
        <source>Debug Mode</source>
        <translation>Debug Mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="202"/>
        <source>ALREADY_DONE</source>
        <translation>The command was already executed</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1402"/>
        <source>From</source>
        <comment>for messaging: _from_ vehicle </comment>
        <translation>From</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1402"/>
        <source>At</source>
        <comment>for messaging: received _at_ time</comment>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="203"/>
        <source>WAIT_FINISH</source>
        <translation>Can not accept action/command while executing the previous one</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="204"/>
        <source>LOST_HAL_CONN</source>
        <translation>No connection to server</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="205"/>
        <source>CAN_NOT_SWITCH_TO_RC</source>
        <translation>Can not switch to remote control</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="206"/>
        <source>RC_ROTATE_CAROUSEL_IDX1</source>
        <translation>Use remote control to rotate carousel clockwise</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="207"/>
        <source>RC_ROTATE_CAROUSEL_IDX2</source>
        <translation>Use remote control to rotate carousel counter-clockwise</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="208"/>
        <source>RC_CLOSE_CAROUSEL</source>
        <translation>Use remote control to close carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="209"/>
        <source>RC_OPEN_CAROUSEL</source>
        <translation>Use remote control to open carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="210"/>
        <source>RC_OPEN_ARM</source>
        <translation>Use remote control to open rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="211"/>
        <source>RC_CLOSE_ARM</source>
        <translation>Use remote control to close pipe support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="212"/>
        <source>RC_MOVE</source>
        <translation>Use remote control to drive to the next target</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="213"/>
        <source>RC_UNSTUCK</source>
        <translation>Use remote control to release rod from being stuck</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="214"/>
        <source>RC_LEVELING</source>
        <translation>Use remote control to level the platform</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="215"/>
        <source>RC_LIFT_STRING</source>
        <translation>Use remote control to lift drill string</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="216"/>
        <source>RC_RODE_CHANGE</source>
        <translation>Use remote control to change rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="217"/>
        <source>RC_LOCK_TOWER</source>
        <translation>Use remote control to lock tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="218"/>
        <source>RC_UNLOCK_TOWER</source>
        <translation>Use remote control to unlock tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="219"/>
        <source>RC_TOWER_TILT</source>
        <translation>Use remote control to adjust tower angle</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="220"/>
        <source>COMPRESSOR_FAILURE</source>
        <translation>Compressor malfunctioning detected</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="221"/>
        <source>WRENCH_SWITCH_FAILURE</source>
        <translation>Wrench switch failed to work</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="222"/>
        <source>IBOX_NO_CONN</source>
        <translation>No connection to iBox</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="223"/>
        <source>IPLACE_NO_CONN</source>
        <translation>No connection to iPlace</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="224"/>
        <source>RESTORING_PULLED_JACKS</source>
        <translation>Bringing jacks back to pulled state</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="225"/>
        <source>RESTORED_STRING</source>
        <translation>String is brought back to normal position</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="226"/>
        <source>STARTING_NEW_ACT</source>
        <translation>Starting to execute a new action</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="227"/>
        <source>READY_FOR_NEW_ACT</source>
        <translation>Ready to accept a new action</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="228"/>
        <source>CAN_NOT_DRILL_CUR_HOLE</source>
        <translation>Can not drill current hole; going to the next one</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="229"/>
        <source>CAN_NOT_ACCEPT_ACTION</source>
        <translation>Can not accept a new action at the moment</translation>
    </message>
</context>
<context>
    <name>MovingDataTranslator</name>
    <message>
        <location filename="../ext_translations.tri" line="190"/>
        <source>Spindle depth: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="191"/>
        <source>m</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="192"/>
        <source>Drill feed speed: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="193"/>
        <source>m/h</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RebootButton</name>
    <message>
        <location filename="../gui_modules.py" line="2137"/>
        <source>Robot
Reset</source>
        <translation>Robot Reset</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2164"/>
        <source>Dangerous Feature!</source>
        <translation>Dangerous Feature!</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2164"/>
        <source>
                                       Dangerous Feature will be activated.
                                       Vehicle damage is possible.

                                       MAKE SURE THE ROD IS NOT IN THE BOREHOLE
                                       and that it sage to retract the jacks.

                                       Enter Password:
                                       </source>
        <translation>
                                       Dangerous Feature will be activated.
                                       Vehicle damage is possible.

                                       MAKE SURE THE ROD IS NOT IN THE BOREHOLE
                                       and that it sage to retract the jacks.

                                       Enter Password:
                                       </translation>
    </message>
</context>
<context>
    <name>RecalibAirButton</name>
    <message>
        <location filename="../ext_translations.tri" line="44"/>
        <source>Recalibrate
air pressure</source>
        <translation>Recalibrate
air pressure</translation>
    </message>
</context>
<context>
    <name>SendHoleButton</name>
    <message>
        <location filename="../gui_modules.py" line="1914"/>
        <source>Drill
here</source>
        <translation>Drill
here</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1937"/>
        <source>Depth</source>
        <translation>Depth</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1947"/>
        <source>Tilt</source>
        <translation>Tilt</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1956"/>
        <source>Hole ID</source>
        <translation>Hole ID</translation>
    </message>
</context>
<context>
    <name>SmartButton</name>
    <message>
        <location filename="../ext_translations.tri" line="199"/>
        <source>test Button Core</source>
        <translation>test Button Core</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="61"/>
        <source>Water</source>
        <translation>Water</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="62"/>
        <source>Lights</source>
        <translation>Lights</translation>
    </message>
</context>
<context>
    <name>SmartButtonGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="51"/>
        <source>Cameras mode</source>
        <translation>Cameras mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="57"/>
        <source>Operation mode</source>
        <translation>Operation mode</translation>
    </message>
</context>
<context>
    <name>TextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="1"/>
        <source>Select Vehicle</source>
        <translation>Select Vehicle</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="2"/>
        <source>Connection Mode</source>
        <translation>Connection Mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="32"/>
        <source>Remote Control Mode</source>
        <translation>Remote Control Mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="119"/>
        <source>Vertical</source>
        <translation>Vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="120"/>
        <source>Slanted</source>
        <translation>Slanted</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="118"/>
        <source>Pins</source>
        <translation>Pins</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="122"/>
        <source>Opened</source>
        <translation>Opened</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="123"/>
        <source>Closed</source>
        <translation>Closed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="121"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="125"/>
        <source>Extended</source>
        <translation>Extended</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="136"/>
        <source>Removed</source>
        <translation>Removed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="124"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="137"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="138"/>
        <source>Tower is in position</source>
        <translation>Tower is in position</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="126"/>
        <source>Retracted</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="127"/>
        <source>Index 1</source>
        <translation>Index 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="128"/>
        <source>Index 2</source>
        <translation>Index 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="129"/>
        <source>Rod in cup 1</source>
        <translation>Rod in cup 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="130"/>
        <source>Rod in cup 2</source>
        <translation>Rod in cup 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="131"/>
        <source>Wrench</source>
        <translation>Wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="132"/>
        <source>Stowed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="133"/>
        <source>Engaged</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="134"/>
        <source>Grip open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="135"/>
        <source>Grip closed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="139"/>
        <source>Work permission</source>
        <translation>Work permission</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="140"/>
        <source>Dispatcher permission</source>
        <translation>Dispatcher permission</translation>
    </message>
</context>
<context>
    <name>TumblerWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="801"/>
        <source>Tumbler On</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="802"/>
        <source>Tumbler in the Middle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="803"/>
        <source>Tumbler Off</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>VehicleCard</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="86"/>
        <source>Task: No info</source>
        <translation>Task: No info</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="88"/>
        <source>Offline</source>
        <translation>Offline</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="90"/>
        <source>No info</source>
        <translation>No info</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="102"/>
        <source>Watch</source>
        <comment>like watch what this vehicle is doing</comment>
        <translation>Watch</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="107"/>
        <source>Control</source>
        <comment>like control this vehicle remotely</comment>
        <translation>Control</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="109"/>
        <source>Disconnect</source>
        <comment>there means not watch and not control vehicle</comment>
        <translation>Disconnect</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="112"/>
        <source>Move Permission</source>
        <comment>does vehicle has permission to move?</comment>
        <translation>Move Permission</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="115"/>
        <source>RoboMode</source>
        <comment>Checkbox, if checked -&gt; vehicle is robot, else manual</comment>
        <translation>RoboMode</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="255"/>
        <source>Robot</source>
        <translation>Robot</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="258"/>
        <source>Manual</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Manual</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="272"/>
        <source>Ok</source>
        <translation>ok</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="275"/>
        <source>Need Permission</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Need Permission</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="358"/>
        <source>No task</source>
        <comment>Label in vehicle selector when no task given</comment>
        <translation>No task</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="364"/>
        <source>Online</source>
        <comment>LED indicator when vehicle is online</comment>
        <translation>Online</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="368"/>
        <source>Robot</source>
        <comment>LED when vehicle is in Robo-mode</comment>
        <translation>Robot</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="371"/>
        <source>Manual</source>
        <comment>LED when vehicle is not in Robo-mode</comment>
        <translation>Manual</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="375"/>
        <source>Offline</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Offline</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="379"/>
        <source>No Data</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>No Data</translation>
    </message>
</context>
<context>
    <name>VehicleSelectorWrapper</name>
    <message>
        <location filename="../gui_modules.py" line="2717"/>
        <source>No data</source>
        <translation>No data</translation>
    </message>
</context>
<context>
    <name>veh_selector</name>
    <message>
        <location filename="../veh_selector.py" line="32"/>
        <source>Watch</source>
        <translation>Watch</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="33"/>
        <source>Control</source>
        <translation>Control</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="34"/>
        <source>Disconnect</source>
        <translation>Disconnect</translation>
    </message>
</context>
</TS>
