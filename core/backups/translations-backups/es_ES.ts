<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="es">
<context>
    <name>AngleIndicatorWidget</name>
    <message>
        <location filename="../gui_modules.py" line="944"/>
        <source>Across: </source>
        <translation>Roll: </translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="945"/>
        <source>Along: </source>
        <translation>Pitch: </translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="951"/>
        <source>Tilt</source>
        <translation>Inclinación</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="972"/>
        <source>Along</source>
        <translation>Pitch</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="985"/>
        <source>Across</source>
        <translation>Roll</translation>
    </message>
</context>
<context>
    <name>AuthButton</name>
    <message>
        <location filename="../gui_modules.py" line="2786"/>
        <source>Login</source>
        <translation>Iniciar sesión</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2787"/>
        <source>Logout</source>
        <translation>Cerrar sesión</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2861"/>
        <source>login</source>
        <translation>Usuario</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2862"/>
        <source>password</source>
        <translation>Contraseña</translation>
    </message>
</context>
<context>
    <name>ButtonInGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="52"/>
        <source>Front</source>
        <translation>Frontal</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="53"/>
        <source>Rear</source>
        <translation>Posterior</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="54"/>
        <source>Drilling</source>
        <translation>Perforación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="55"/>
        <source>Birdview</source>
        <translatorcomment>Vista de planta</translatorcomment>
        <translation>BLABLABLA</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="58"/>
        <source>Drill</source>
        <translation>Perforación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="59"/>
        <source>Propel</source>
        <translation>Propulsión</translation>
    </message>
</context>
<context>
    <name>ConditionalTextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="194"/>
        <source>Maneuver in process</source>
        <translation>Maniobra en proceso</translation>
    </message>
</context>
<context>
    <name>ControllerPanel</name>
    <message>
        <location filename="../ext_translations.tri" line="3"/>
        <source>Driving</source>
        <translation>Impulsar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="16"/>
        <source>Right</source>
        <translation>Derecha</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="15"/>
        <source>Left</source>
        <translation>Izquierda</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="6"/>
        <source>           pull extend
     Jacks</source>
        <translation>Retraer gatos extendidos</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="7"/>
        <source>Drilling</source>
        <translation>Perforación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="8"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="24"/>
        <source>Feed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="10"/>
        <source>Rotating</source>
        <translation>Rotación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="26"/>
        <source>Force</source>
        <translation>Fuerza</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="12"/>
        <source>Compressor</source>
        <translation>Compresor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="13"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="14"/>
        <source>Rear</source>
        <translation>Posterior</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="17"/>
        <source>Tower Tilt</source>
        <translation>Inclinación del mástil</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="18"/>
        <source>Tilt</source>
        <translation>Inclinación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="19"/>
        <source>Upper Pin</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="20"/>
        <source>Lower Pin</source>
        <translation>Seguro de torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="21"/>
        <source>  Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="22"/>
        <source>Extention</source>
        <translation>Extención</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="23"/>
        <source>Manipulator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="25"/>
        <source>Turning</source>
        <translation>Girando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="27"/>
        <source>clamp  offset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="28"/>
        <source>   Fork</source>
        <translation>Llave U</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="29"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="30"/>
        <source>Supply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="31"/>
        <source>Angle</source>
        <translation>Ángulo</translation>
    </message>
</context>
<context>
    <name>CustomSlider</name>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="354"/>
        <source>Apply</source>
        <comment>send data from this widget to system</comment>
        <translation>Aplicar</translation>
    </message>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="357"/>
        <source>Save</source>
        <comment>Button caption that push value into settings file</comment>
        <translation>Guardar</translation>
    </message>
</context>
<context>
    <name>CustomSliderWrapper</name>
    <message>
        <location filename="../ext_translations.tri" line="46"/>
        <source>Feed pressure</source>
        <translation>Pull Down Presión</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="47"/>
        <source>bar</source>
        <translation>Bar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="48"/>
        <source>Rotation speed</source>
        <translation>Velocidad de rotación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="49"/>
        <source>rpm</source>
        <translation>RPM</translation>
    </message>
</context>
<context>
    <name>DialIndicator</name>
    <message>
        <location filename="../ext_translations.tri" line="33"/>
        <source>Revolutions
(revs/min) x10</source>
        <translation>Velocidad Rotación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="34"/>
        <source>Air
pressure (bar)</source>
        <translation>Presión de aire</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="36"/>
        <source>Rotating
pressure (psi)</source>
        <translation>Presión de rotación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="37"/>
        <source>Drill Feed
pressure (psi)</source>
        <translation>Pull down presión (psi)</translation>
    </message>
</context>
<context>
    <name>DrillJoystickWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="64"/>
        <source>Driving</source>
        <translation>Conducción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="65"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="66"/>
        <source>Retract</source>
        <translation>Retraer</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="67"/>
        <source>Extend</source>
        <translation>Extender</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="68"/>
        <source>Right track</source>
        <translation>Oruga derecha</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="69"/>
        <source>Left track</source>
        <translation>Oruga izquierda</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="70"/>
        <source>Back</source>
        <translation>Retroceder</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="71"/>
        <source>Forth</source>
        <translation>Adelante</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="73"/>
        <source>Drilling</source>
        <translation>Perforación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="74"/>
        <source>Feed force</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="75"/>
        <source>Rotation</source>
        <translation>Rotación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="78"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="79"/>
        <source>Open</source>
        <translation>Abrir</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="80"/>
        <source>Close</source>
        <translation type="unfinished">Cerrar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="110"/>
        <source>Feed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="111"/>
        <source>Up</source>
        <translation>Arriba</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="112"/>
        <source>Down</source>
        <translation>Bajar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="113"/>
        <source>Compressor</source>
        <translation>Compresor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="114"/>
        <source>On</source>
        <translation>Encender</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="115"/>
        <source>Off</source>
        <translation>Apagar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="88"/>
        <source>Leveling</source>
        <translation>Nivelación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="89"/>
        <source>All jacks</source>
        <translation>Todas las gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="90"/>
        <source>Rear</source>
        <translation>Trasera</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="92"/>
        <source>Tower</source>
        <translation>Torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="93"/>
        <source>Inclined Pin</source>
        <translation>Pin inclinado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="94"/>
        <source>Tilt</source>
        <translation>Inclinanción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="97"/>
        <source>Vertical Pin</source>
        <translation>Pin vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="99"/>
        <source>Wrench</source>
        <translation>Llave de quiebre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="100"/>
        <source>Release</source>
        <translation>Liberar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="101"/>
        <source>Turn</source>
        <translation type="unfinished">Girar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="102"/>
        <source>Stow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="103"/>
        <source>Swing in</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="104"/>
        <source>Fork</source>
        <translation>Llave u</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="105"/>
        <source>Stop</source>
        <translation>Parar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="107"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="108"/>
        <source>Index</source>
        <translation>Indice</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="76"/>
        <source>CCW</source>
        <translation>Antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="77"/>
        <source>CW</source>
        <translation>Horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="95"/>
        <source>Raise</source>
        <translation>Elevar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="96"/>
        <source>Lower</source>
        <translation>Bajar</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="46"/>
        <source>Red button</source>
        <translation>Boton rojo</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="49"/>
        <source>Green button</source>
        <translation>Boton verde</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="129"/>
        <source>Red and Green Buttons</source>
        <translation>Botones rojo y verde</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="132"/>
        <source>Knob widget</source>
        <translation>Perilla</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="135"/>
        <source>Left Joystick</source>
        <translation>Joystick izquierdo</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="138"/>
        <source>Center Joystick</source>
        <translation>Joystick central</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="141"/>
        <source>Right Joystick</source>
        <translation>Joystick derecho</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="144"/>
        <source>Tumbler Widget</source>
        <translation></translation>
    </message>
</context>
<context>
    <name>DropActionButton</name>
    <message>
        <location filename="../gui_modules.py" line="3181"/>
        <source>Confirm current task cancelling</source>
        <translation>Confirmar cancelación de tarea actual</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3190"/>
        <source>Permitted to qualified Engineers or Developers only</source>
        <translation>Permitido solo para ingenieros calificados </translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3192"/>
        <source>Wrong password</source>
        <translation>Contraseña incorrecta</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3242"/>
        <source>Unusual Action</source>
        <translation>Acción inusual</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="3242"/>
        <source>
                                            Drilling in the process, can&apos;t cancel task right now.
                                            Enter password if still want to proceed
                                            </source>
        <translation>Perforación en proceso, no se puede cancelar la tarea ahora mismo
Ingresar contraseña si aún quiere proceder</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="39"/>
        <source>Cancel
task</source>
        <translation>Cancelar tarea</translation>
    </message>
</context>
<context>
    <name>DropDrillError</name>
    <message>
        <location filename="../ext_translations.tri" line="40"/>
        <source>Clear
error</source>
        <translation>Borrar error</translation>
    </message>
</context>
<context>
    <name>DropTailingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="41"/>
        <source>Clear
tailing</source>
        <translation>Eliminar limites</translation>
    </message>
</context>
<context>
    <name>EmergencySetter</name>
    <message>
        <location filename="../gui_modules.py" line="223"/>
        <source>DENY MOVEMENT</source>
        <translation>Denegar movimiento</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="230"/>
        <source>PERMIT MOVEMENT</source>
        <translation>Permitir movimiento</translation>
    </message>
</context>
<context>
    <name>FinishBuildupButton</name>
    <message>
        <location filename="../gui_modules.py" line="2198"/>
        <source>Attention!
Make sure that the new rod is extended
Fork and key is retracted</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2200"/>
        <source>Attention!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2225"/>
        <source>End
Extending</source>
        <translation>Varilla
agregada</translation>
    </message>
</context>
<context>
    <name>FinishHoleButton</name>
    <message>
        <location filename="../ext_translations.tri" line="42"/>
        <source>Finish
drilling</source>
        <translation>Terminar Perforación</translation>
    </message>
</context>
<context>
    <name>FinishMovingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="43"/>
        <source>Finish
moving</source>
        <translation>Finalizar movimiento</translation>
    </message>
</context>
<context>
    <name>FinishStowButton</name>
    <message>
        <location filename="../gui_modules.py" line="2274"/>
        <source>Attention!
Make sure the boring rod is not in the borehole,
Fork and Key are retracted away!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2275"/>
        <source>Attention!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2300"/>
        <source>Finish
Stow</source>
        <translation>Varilla
removida</translation>
    </message>
</context>
<context>
    <name>HardEmergencyButton</name>
    <message>
        <location filename="../gui_modules.py" line="2082"/>
        <source>Emergency
Stop</source>
        <translation>Parada de 
emergencia</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2091"/>
        <source>Continue
working</source>
        <translation>Continuar trabajando</translation>
    </message>
</context>
<context>
    <name>JoystickWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="635"/>
        <source>Top Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="636"/>
        <source>Bottom Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="637"/>
        <source>0.0%</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="641"/>
        <source>Nonlinear</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>KnobViewWithCaption</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="520"/>
        <source>Nonlinear</source>
        <comment>Toggle Switch caption</comment>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LogoutWorker</name>
    <message>
        <location filename="../gui_modules.py" line="2927"/>
        <source>Can&apos;t Authorize user
errror: </source>
        <translation>No se puede autorizar error de usuario</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2931"/>
        <source>Login Error</source>
        <translation>Error de inicio de sesión</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2939"/>
        <source>Login field should not be empty</source>
        <translation>Campo de inicio de sesión no debe estar vacio</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2940"/>
        <source>Empty Login Field</source>
        <translation>Campo de inicio de sesión vacio</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2946"/>
        <source>Password field should not be empty</source>
        <translation>Contraseña no debe estar vacia</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2947"/>
        <source>Empty Password Field</source>
        <translation>Campo de contraseña vacio</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2981"/>
        <source>Can&apos;t logout user
errror: </source>
        <translation>No se puede cerrar sesión
error de usuario</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2985"/>
        <source>Logout Error</source>
        <translation>Error de cierre de sesión</translation>
    </message>
</context>
<context>
    <name>MachineStateManagement</name>
    <message>
        <location filename="../gui_modules.py" line="1709"/>
        <source>Unlock</source>
        <translation>Desbloquear</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1722"/>
        <source>Lock</source>
        <translation>Bloquear</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1739"/>
        <source>Commit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1746"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="142"/>
        <source>Idle</source>
        <translation>Inactivo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="144"/>
        <source>Driving</source>
        <translation>Conduciendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="182"/>
        <source>Leveling</source>
        <translation>Nivelando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="146"/>
        <source>Tower tilt</source>
        <translation>Inclinación de torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="165"/>
        <source>Drilling</source>
        <translation>Perforando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="148"/>
        <source>Shaft Build-up</source>
        <translation>Agregar barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="149"/>
        <source>Shaft stow</source>
        <translation>Sustraer barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="150"/>
        <source>Grounding</source>
        <translation>Recogiendo gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="151"/>
        <source>Wait for Remote</source>
        <translation>Esperando control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="152"/>
        <source>Prepare for Remote</source>
        <translation>Preparando para control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="153"/>
        <source>In Remote</source>
        <translation>En remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="154"/>
        <source>Finishing Remote</source>
        <translation type="unfinished">Terminando control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="155"/>
        <source>Waiting after leveling</source>
        <translation>Esperando despues de nivelación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="156"/>
        <source>String restoring</source>
        <translation type="unfinished">Movimiento de cadena</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="157"/>
        <source>Waiting before leveling</source>
        <translation>Esperando antes de nivelación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="158"/>
        <source>Failure</source>
        <translation>Falla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="159"/>
        <source>Locking</source>
        <translation>Bloqueando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="160"/>
        <source>Unlocking</source>
        <translation>Desbloqueando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="161"/>
        <source>Arm closing</source>
        <translation>Cierre de rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="162"/>
        <source>Tilt Regulation</source>
        <translation>Regulación de inclinación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="163"/>
        <source>Touchdown</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="164"/>
        <source>Overburden pass</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="166"/>
        <source>Hard Rot</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="167"/>
        <source>Pullup</source>
        <translation>Jalar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="168"/>
        <source>After Pullup</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="169"/>
        <source>String Raising</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="170"/>
        <source>Waiting after drill</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="171"/>
        <source>Pass soft</source>
        <translation>Pase suave</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="172"/>
        <source>Tower</source>
        <translation>Torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="173"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="174"/>
        <source>Opening</source>
        <translation>Abriendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="176"/>
        <source>Closing</source>
        <translation>Cerrando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="177"/>
        <source>Close</source>
        <translation>Cerrar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="178"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="179"/>
        <source>Pulling</source>
        <translation>Retrayendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="180"/>
        <source>Pulled</source>
        <translation>Retraido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="181"/>
        <source>Restoring pulled</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="183"/>
        <source>Final Leveling</source>
        <translation>Nivelación final</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="184"/>
        <source>Holding</source>
        <translation>Sosteniendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="185"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="186"/>
        <source>Carousel opening</source>
        <translation>Apertura de carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="187"/>
        <source>Carousel closing</source>
        <translation>Cierre de carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="188"/>
        <source>Carousel turning cw</source>
        <translation>Carrusel giro horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="189"/>
        <source>Carousel turning ccw</source>
        <translation>Carrusel giro antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="175"/>
        <source>Оpen</source>
        <translation>Abrir</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="141"/>
        <source>Main State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="143"/>
        <source>Init</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MessageLogs</name>
    <message>
        <location filename="../ext_translations.tri" line="196"/>
        <source>100000101</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="197"/>
        <source>fucking_code_123</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MessagesWidgetMaximized</name>
    <message>
        <location filename="../gui_modules.py" line="1308"/>
        <source>Message Log</source>
        <translation>Registro de mensajes</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1323"/>
        <source>Debug Mode</source>
        <translation>Modo de depuración de errores</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1402"/>
        <source>From</source>
        <comment>for messaging: _from_ vehicle </comment>
        <translation type="unfinished">D</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1402"/>
        <source>At</source>
        <comment>for messaging: received _at_ time</comment>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="202"/>
        <source>ALREADY_DONE</source>
        <translation>El comando fue ejecutado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="203"/>
        <source>WAIT_FINISH</source>
        <translation>No se puede aceptar tarea/comando mientras se esta ejecutando otro</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="204"/>
        <source>LOST_HAL_CONN</source>
        <translation>Sin conexión al servidor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="205"/>
        <source>CAN_NOT_SWITCH_TO_RC</source>
        <translation>No se puede cambiar a modo control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="206"/>
        <source>RC_ROTATE_CAROUSEL_IDX1</source>
        <translation>Usar control remoto para rotar carrusel en sentido horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="207"/>
        <source>RC_ROTATE_CAROUSEL_IDX2</source>
        <translation>Usar control remoto para rotar carrusel en sentido antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="208"/>
        <source>RC_CLOSE_CAROUSEL</source>
        <translation>Usar control remoto para cerrar carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="209"/>
        <source>RC_OPEN_CAROUSEL</source>
        <translation>Usar control remoto para abrir carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="210"/>
        <source>RC_OPEN_ARM</source>
        <translation>Usar control remoto para abrir el rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="211"/>
        <source>RC_CLOSE_ARM</source>
        <translation>Usar control remoto para cerrar el rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="212"/>
        <source>RC_MOVE</source>
        <translation>Usar control remoto para mover la perforadora al siguiente taladro</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="213"/>
        <source>RC_UNSTUCK</source>
        <translation>Usar control remoto para liberar la broca del atascamiento</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="214"/>
        <source>RC_LEVELING</source>
        <translation>Usar control remoto para nivelar la plataforma</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="215"/>
        <source>RC_LIFT_STRING</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="216"/>
        <source>RC_RODE_CHANGE</source>
        <translation>Usar control remoto para cambiar la broca</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="217"/>
        <source>RC_LOCK_TOWER</source>
        <translation>Usar control remoto para asegurar la torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="218"/>
        <source>RC_UNLOCK_TOWER</source>
        <translation>Usar control remoto para liberar la torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="219"/>
        <source>RC_TOWER_TILT</source>
        <translation>Usar control remoto para justar el ángulo de la torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="220"/>
        <source>COMPRESSOR_FAILURE</source>
        <translation>Falla de compresor detectada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="221"/>
        <source>WRENCH_SWITCH_FAILURE</source>
        <translation>El switch del wrench esta fallando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="222"/>
        <source>IBOX_NO_CONN</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="223"/>
        <source>IPLACE_NO_CONN</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="224"/>
        <source>RESTORING_PULLED_JACKS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="225"/>
        <source>RESTORED_STRING</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="226"/>
        <source>STARTING_NEW_ACT</source>
        <translation>Iniciando nueva tarea</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="227"/>
        <source>READY_FOR_NEW_ACT</source>
        <translation>Listo para aceptar nueva tarea</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="228"/>
        <source>CAN_NOT_DRILL_CUR_HOLE</source>
        <translation>No se puede perforar el taladro, pasar al siguiente taladro</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="229"/>
        <source>CAN_NOT_ACCEPT_ACTION</source>
        <translation>No se puede aceptar una nueva tarea por el momento</translation>
    </message>
</context>
<context>
    <name>MovingDataTranslator</name>
    <message>
        <location filename="../ext_translations.tri" line="190"/>
        <source>Spindle depth: </source>
        <translation>Posición de la cabeza: </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="191"/>
        <source>m</source>
        <translation>m</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="192"/>
        <source>Drill feed speed: </source>
        <translation>Velocidad de avance</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="193"/>
        <source>m/h</source>
        <translation>m/h</translation>
    </message>
</context>
<context>
    <name>RebootButton</name>
    <message>
        <location filename="../gui_modules.py" line="2137"/>
        <source>Robot
Reset</source>
        <translation>Reiniciar el software</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2164"/>
        <source>Dangerous Feature!</source>
        <translation>Caracteristica peligrosa!</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="2164"/>
        <source>
                                       Dangerous Feature will be activated.
                                       Vehicle damage is possible.

                                       MAKE SURE THE ROD IS NOT IN THE BOREHOLE
                                       and that it sage to retract the jacks.

                                       Enter Password:
                                       </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RecalibAirButton</name>
    <message>
        <location filename="../ext_translations.tri" line="44"/>
        <source>Recalibrate
air pressure</source>
        <translation>Recalibrar
presión de aire</translation>
    </message>
</context>
<context>
    <name>SendHoleButton</name>
    <message>
        <location filename="../gui_modules.py" line="1914"/>
        <source>Drill
here</source>
        <translation>Perforar
aquí</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1937"/>
        <source>Depth</source>
        <translation>Profundidad</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1947"/>
        <source>Tilt</source>
        <translation>Inclinanción</translation>
    </message>
    <message>
        <location filename="../gui_modules.py" line="1956"/>
        <source>Hole ID</source>
        <translation>ID taladro</translation>
    </message>
</context>
<context>
    <name>SmartButton</name>
    <message>
        <location filename="../ext_translations.tri" line="199"/>
        <source>test Button Core</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="61"/>
        <source>Water</source>
        <translation>Agua</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="62"/>
        <source>Lights</source>
        <translation>Luces</translation>
    </message>
</context>
<context>
    <name>SmartButtonGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="51"/>
        <source>Cameras mode</source>
        <translation>Cámaras</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="57"/>
        <source>Operation mode</source>
        <translation>Modo de operación</translation>
    </message>
</context>
<context>
    <name>TextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="1"/>
        <source>Select Vehicle</source>
        <translation>Seleccionar vehículo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="2"/>
        <source>Connection Mode</source>
        <translation>Modo de conexión</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="32"/>
        <source>Remote Control Mode</source>
        <translation>Modo remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="119"/>
        <source>Vertical</source>
        <translation>Vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="120"/>
        <source>Slanted</source>
        <translation>Inclinado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="118"/>
        <source>Pins</source>
        <translation>Pines</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="122"/>
        <source>Opened</source>
        <translation>Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="123"/>
        <source>Closed</source>
        <translation>Cerrado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="121"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="125"/>
        <source>Extended</source>
        <translation>Extendido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="136"/>
        <source>Removed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="124"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="137"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="138"/>
        <source>Tower is in position</source>
        <translation>Torre alineada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="126"/>
        <source>Retracted</source>
        <translation>Retraido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="127"/>
        <source>Index 1</source>
        <translation>Indice 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="128"/>
        <source>Index 2</source>
        <translation>Indice 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="129"/>
        <source>Rod in cup 1</source>
        <translation>Barra en copa 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="130"/>
        <source>Rod in cup 2</source>
        <translation>Barra en copa 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="131"/>
        <source>Wrench</source>
        <translation>Llave de quiebre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="132"/>
        <source>Stowed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="133"/>
        <source>Engaged</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="134"/>
        <source>Grip open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="135"/>
        <source>Grip closed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="139"/>
        <source>Work permission</source>
        <translation>Permiso de trabajo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="140"/>
        <source>Dispatcher permission</source>
        <translation>Permiso del despachador</translation>
    </message>
</context>
<context>
    <name>TumblerWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="801"/>
        <source>Tumbler On</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="802"/>
        <source>Tumbler in the Middle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="803"/>
        <source>Tumbler Off</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>VehicleCard</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="86"/>
        <source>Task: No info</source>
        <translation>Tarea:sin información</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="88"/>
        <source>Offline</source>
        <translation>Desconectado</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="90"/>
        <source>No info</source>
        <translation>Sin información</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="102"/>
        <source>Watch</source>
        <comment>like watch what this vehicle is doing</comment>
        <translation>Ver equipo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="107"/>
        <source>Control</source>
        <comment>like control this vehicle remotely</comment>
        <translation>Control remoto</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="109"/>
        <source>Disconnect</source>
        <comment>there means not watch and not control vehicle</comment>
        <translation>Desconectar</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="112"/>
        <source>Move Permission</source>
        <comment>does vehicle has permission to move?</comment>
        <translation>Permiso de movimiento</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="115"/>
        <source>RoboMode</source>
        <comment>Checkbox, if checked -&gt; vehicle is robot, else manual</comment>
        <translation>Modo autonomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="255"/>
        <source>Robot</source>
        <translation>Autónomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="258"/>
        <source>Manual</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Mecanico</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="272"/>
        <source>Ok</source>
        <translation>ok</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="275"/>
        <source>Need Permission</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Requiere permiso</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="358"/>
        <source>No task</source>
        <comment>Label in vehicle selector when no task given</comment>
        <translation>Sin tareas</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="364"/>
        <source>Online</source>
        <comment>LED indicator when vehicle is online</comment>
        <translation>Conectado</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="368"/>
        <source>Robot</source>
        <comment>LED when vehicle is in Robo-mode</comment>
        <translation>Autónomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="371"/>
        <source>Manual</source>
        <comment>LED when vehicle is not in Robo-mode</comment>
        <translation>Mecánico</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="375"/>
        <source>Offline</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Desconectado</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="379"/>
        <source>No Data</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Sin datos</translation>
    </message>
</context>
<context>
    <name>VehicleSelectorWrapper</name>
    <message>
        <location filename="../gui_modules.py" line="2717"/>
        <source>No data</source>
        <translation>Sin datos</translation>
    </message>
</context>
<context>
    <name>veh_selector</name>
    <message>
        <location filename="../veh_selector.py" line="32"/>
        <source>Watch</source>
        <translation>Ver equipo</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="33"/>
        <source>Control</source>
        <translation>Control remoto</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="34"/>
        <source>Disconnect</source>
        <translation>Desconectar</translation>
    </message>
</context>
</TS>
