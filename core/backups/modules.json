{"communicator": {"is_active": false, "file_name": "core", "class_name": "Communicator", "init_args": {}, "prep_args": {}}, "joystick_adapter": {"is_active": false, "file_name": "joystick_adapter", "class_name": "JoystickAdapter", "init_args": {}, "prep_args": {"port": "/dev/ttyACM0", "baudrate": 115200, "pkg_size": 49, "pkg_timeout": 0.5, "joystick_power_coef": 1, "channel_thresholds": {"pot_1_value": [0, 1.55, 2.96], "pot_2_value": [0, 1.28, 2.96], "pot_3_value": [0, 1.55, 2.96], "pot_4_value": [0, 0, 2.96]}, "switches_state_map": {"switch1": {"1": -1, "11": 0, "10": 1, "0": 0}, "switch2": {"1": -1, "11": 0, "10": 1, "0": 0}, "switch3": {"1": -1, "11": 0, "10": 1, "0": 0}, "switch4": {"1": -1, "11": 0, "10": 1, "0": 0}}, "channel_to_index_map": {"joystick1_button": 2, "joystick2_button": 3, "joystick3_button": 4, "switch1": [5, 6], "switch2": [7, 8], "switch3": [9, 10], "switch4": [11, 12], "pot_1_value": [19, 22], "pot_2_value": [23, 26], "pot_3_value": [27, 30], "pot_4_value": [31, 34]}, "joystick_ang_channel_map": {"joystick1_angle": {"value": "pot_2_value", "power": "joystick1_button"}, "joystick2_angle": {"value": "pot_1_value", "power": "joystick2_button"}, "joystick3_angle": {"value": "pot_3_value", "power": "joystick3_button"}}, "layouts": {"driving": {"cat": {"left_track": "-joystick2_angle", "right_track": "-joystick1_angle"}, "leveler": {"rear_jack": "-switch3", "right_jack": "-switch3", "left_jack": "-switch3"}}, "drilling": {"drill": {"rotation_speed": "-joystick1_angle", "feed_speed": "-joystick2_angle", "feed_pressure": "pot_4_value"}, "compressor": {"value": "switch4"}, "arm": {"move_arm": "joystick3_angle"}}, "leveling": {"leveler": {"rear_jack": "joystick3_angle", "right_jack": "joystick1_angle", "left_jack": "joystick2_angle"}}, "tower_control": {"tower": {"inclined_pin": "joystick1_angle", "vertical_pin": "joystick2_angle", "tower_tilt": "joystick3_angle"}, "arm": {"move_arm": "switch3"}}, "buildup": {"drill": {"rotation_speed": "-joystick1_angle", "feed_pressure": "pot_4_value", "feed_speed": "-joystick2_angle"}, "wrench": {"move_wrench": "joystick3_angle", "grip_wrench": "switch3"}, "fork": {"move_fork": "switch4"}}, "carousel_control": {"carousel": {"move_carousel": "joystick2_angle", "rotate_carousel": "joystick1_angle"}}}}}, "plan_reader": {"is_active": false, "file_name": "plan_reader", "class_name": "PlanReader", "init_args": {}, "prep_args": {}}, "vel_select_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Select Vehicle", "font_size": "45px", "x": 35, "y": 30, "font_color": "#D2D6E4", "screen": 0}}, "veh_mode_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Connection Mode", "font_size": "45px", "x": 740, "y": 30, "font_color": "#D2D6E4", "screen": 0}}, "veh_selector": {"is_active": false, "file_name": "veh_selector", "class_name": "VehSelector", "init_args": {}, "prep_args": {"x": 50, "y": 110, "spacer_height": 100, "btn_height_veh": 70, "btn_width_veh": 250, "btn_height_act": 70, "btn_width_act": 290, "font_size": "35px", "distance_between_veh": 50, "veh_between_act": 155, "default_position": false}}, "telemetry2markers_converter": {"is_active": false, "file_name": "core", "class_name": "Telemetry2MarkersConverter", "init_args": {}, "prep_args": {"border_line_width": 0.3, "border_line_color": "#ffffff", "route_line_width": 0.2, "route_line_color": "#ffffff"}}, "core_data_translator": {"is_active": false, "file_name": "gui_modules", "class_name": "CoreDataTranslator", "init_args": {}, "prep_args": {}}, "control_setter": {"is_active": false, "file_name": "gui_modules", "class_name": "ControlSetter", "init_args": {}, "prep_args": {}}, "smart_direct_mode_setter": {"is_active": false, "file_name": "gui_modules", "class_name": "SmartDirectModeSetter", "init_args": {}, "prep_args": {}}, "emergency_setter": {"is_active": false, "file_name": "gui_modules", "class_name": "EmergencySetter", "init_args": {}, "prep_args": {"x": 530, "y": 880, "btn_height": 75, "btn_width": 400, "btn_space": 40, "font_size": "30px"}}, "dd_setter": {"is_active": false, "file_name": "gui_modules", "class_name": "DDModeSetter", "init_args": {}, "prep_args": {"x": 50, "y": 430, "btn_height": 65}}, "dc_setter": {"is_active": false, "file_name": "gui_modules", "class_name": "DCSetter", "init_args": {}, "prep_args": {"x": 200, "y": 430, "btn_height": 65}}, "controller_panel": {"is_active": false, "file_name": "gui_modules", "class_name": "ControllerPanel", "init_args": {}, "prep_args": {"width": 1099, "height": 546, "x": 415, "y": 250, "labels_font_size": 25, "font_size": "28px", "btns_vertical_spacing": 30, "btns_in_row": 1, "btns_width": 250, "btns_height": 60, "btns_zone_h": 500, "btns_zone_w": 300, "default_mode": "driving", "modes": {"driving": {"name": "Driving", "joy_top": "", "joy_right": "Right", "joy_left": "Left", "pot": "", "switch": "           pull extend\n     Jacks", "switch_v": ""}, "drilling": {"name": "Drilling", "joy_top": "Arm", "joy_right": "Feed", "joy_left": "Rotating", "pot": "Force", "switch": "", "switch_v": "Compressor"}, "leveling": {"name": "<PERSON><PERSON>", "joy_top": "Rear", "joy_right": "Left", "joy_left": "Right", "pot": "", "switch": "", "switch_v": ""}, "tower_control": {"name": "Tower Tilt", "joy_top": "Tilt", "joy_right": "Upper Pin", "joy_left": "Lower Pin", "pot": "", "switch": "  Arm", "switch_v": ""}, "buildup": {"name": "Extention", "joy_top": "Manipulator", "joy_right": "Feed", "joy_left": "Turning", "pot": "Force", "switch": "clamp  offset", "switch_v": "   Fork"}, "carousel_control": {"name": "Carousel", "joy_top": "", "joy_right": "Supply", "joy_left": "<PERSON><PERSON>", "pot": "", "switch": "", "switch_v": ""}}}}, "control_mode_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Remote Control Mode", "font_size": "45px", "x": 940, "y": 270, "font_color": "#D2D6E4", "screen": 0}}, "map": {"is_active": false, "file_name": "gui_modules", "class_name": "Map", "init_args": {}, "prep_args": {"width": 635, "height": 440, "x": 10, "y": 10, "pointer_size": 5, "gridline_spacing": 5, "marker_font_size": 10, "vehid_font_size": 12, "coords_coeff": 10, "drill2center_dist": 2.8, "screen_num": 1}}, "rviz_map": {"is_active": false, "file_name": "gui_modules", "class_name": "RvizMap", "init_args": {}, "prep_args": {"width": 635, "height": 440, "x": 10, "y": 10, "screen_num": 1}}, "front_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 607, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 90, "screen": 2}, "drill": {"width": 705, "height": 1080, "x": 0, "y": 0, "crop": [0.1, 0.0, 0.0, 0.0], "rotation": 90, "screen": 2}}}, "prep_args": {"cam_id": "6"}}, "rear_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"rear": {"width": 1920, "height": 1080, "x": 0, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 2}}}, "prep_args": {"cam_id": "4"}}, "right_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 1920, "height": 1080, "x": 1263, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 2}, "rear": {"width": 1264, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.34], "rotation": 0, "screen": 1}, "drill": {"width": 1264, "height": 1080, "x": 0, "y": 0, "crop": [0, 0.34, 0.0, 0.0], "rotation": 0, "screen": 3}}}, "prep_args": {"cam_id": "1"}}, "drill_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"drill": {"width": 607, "height": 1080, "x": 705, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 90, "screen": 2}}}, "prep_args": {"cam_id": "2"}}, "tower_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"drill": {"width": 607, "height": 1080, "x": 1312, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": -90, "screen": 2}}}, "prep_args": {"cam_id": "3"}}, "left_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 1920, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 1}, "rear": {"width": 1264, "height": 1080, "x": 0, "y": 0, "crop": [0, 0.34, 0.0, 0.0], "rotation": 0, "screen": 3}, "drill": {"width": 1264, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.34], "rotation": 0, "screen": 1}}}, "prep_args": {"cam_id": "5"}}, "dial_indicator_rot_speed": {"is_active": false, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 250, "height": 250, "x": 10, "y": 50, "field_to_read": "drill_rotation_speed", "initial_value": 25, "ticks_font_size": 15, "scale_range": 25, "scale_step": 5, "caption": "Revolutions\n(revs/min) x10", "scale_gradient": {"green": 0.6, "red": 0.54}, "caption_font_size": 10, "coeff": 0.1, "modes_visible": ["drill"], "screen_num": 2}}, "dial_indicator_air_pressure": {"is_active": false, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 250, "height": 250, "x": 270, "y": 50, "field_to_read": "air_pressure", "initial_value": 11, "ticks_font_size": 15, "caption_font_size": 10, "scale_range": 11, "scale_step": 2, "caption": "Air\npressure (bar)", "scale_gradient": {"green": 0.55, "yellow": 0.48, "red": 0.4}, "coeff": 1, "modes_visible": ["drill"], "screen_num": 2}}, "dial_indicator_rot_pressure": {"is_active": false, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 250, "height": 250, "x": 10, "y": 350, "field_to_read": "drill_rotation_pressure", "initial_value": 7500, "ticks_font_size": 10, "scale_range": 7500, "scale_step": 1000, "caption": "Rotating\npressure (psi)", "caption_font_size": 10, "scale_gradient": {"green": 0.73, "yellow": 0.6, "red": 0.5}, "coeff": 14.6959, "modes_visible": ["drill"], "screen_num": 2}}, "dial_indicator_feed_pressure": {"is_active": false, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 250, "height": 250, "x": 270, "y": 350, "field_to_read": "drill_feed_pressure", "initial_value": 7500, "ticks_font_size": 10, "caption_font_size": 11, "scale_range": 7500, "scale_step": 1000, "caption": "Drill Feed\npressure (psi)", "scale_gradient": {"green": 0.7, "yellow": 0.6, "red": 0.58}, "coeff": 14.6959, "modes_visible": ["drill"], "screen_num": 2}}, "cam_mode_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Cameras Mode", "font_size": "45px", "x": 1570, "y": 655, "font_color": "#D2D6E4", "screen": 0}}, "cameras_control": {"is_active": false, "file_name": "gui_modules", "class_name": "CamerasControl", "init_args": {}, "prep_args": {"x": 1620, "y": 745, "btn_width": 200, "btn_height": 60, "font_size": "30px", "btn_vert_space": 20, "btn_hor_space": 30, "btns_in_row": 1, "modes": {"front": {"name": "Front"}, "rear": {"name": "Rear"}, "drill": {"name": "Drill"}}, "default_mode": "front"}}, "angle_indicator": {"is_active": false, "file_name": "gui_modules", "class_name": "AngleIndicator", "init_args": {}, "prep_args": {"width": 300, "height": 300, "x": 50, "y": 500, "label_font_size": 12, "screen_num": 1}}, "rear_jack_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 50, "x": 82, "y": 895, "screen": 1, "field_to_read": "rear_jack_pulled", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "right_jack_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 50, "x": 35, "y": 970, "screen": 1, "field_to_read": "right_jack_pulled", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "left_jack_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 50, "x": 125, "y": 970, "screen": 1, "field_to_read": "left_jack_pulled", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "vert_pin_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 550, "y": 930, "screen": 1, "field_to_read": "vert_pin", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "vert_pin_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Vertical", "font_size": "19px", "x": 405, "y": 950, "font_color": "#FFFFFF", "screen": 1}}, "incl_pin_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 550, "y": 980, "screen": 1, "field_to_read": "inclined_pin", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "incl_pin_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Slanted", "font_size": "19px", "x": 405, "y": 1000, "font_color": "#FFFFFF", "screen": 1}}, "pin_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Pins", "font_size": "22px", "x": 405, "y": 900, "font_color": "#FFFFFF", "screen": 1}}, "arm_open_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 330, "y": 930, "screen": 1, "field_to_read": "arm_open", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "arm_open_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Opened", "font_size": "19px", "x": 255, "y": 950, "font_color": "#FFFFFF", "screen": 1}}, "arm_closed_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 330, "y": 980, "screen": 1, "field_to_read": "arm_closed", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "arm_closed_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Closed", "font_size": "19px", "x": 255, "y": 1000, "font_color": "#FFFFFF", "screen": 1}}, "arm_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Arm", "font_size": "22px", "x": 255, "y": 900, "font_color": "#FFFFFF", "screen": 1}}, "carousel_open_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 550, "y": 630, "screen": 1, "field_to_read": "carousel_open", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "carousel_open_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Extended", "font_size": "19px", "x": 405, "y": 650, "font_color": "#FFFFFF", "screen": 1}}, "carousel_closed_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 550, "y": 680, "screen": 1, "field_to_read": "carousel_closed", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "carousel_closed_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Removed", "font_size": "19px", "x": 405, "y": 700, "font_color": "#FFFFFF", "screen": 1}}, "index1_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 550, "y": 730, "screen": 1, "field_to_read": "carousel_index_1", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "index1_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Cup 1", "font_size": "19px", "x": 405, "y": 750, "font_color": "#FFFFFF", "screen": 1}}, "cup1_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 35, "x": 550, "y": 780, "screen": 1, "field_to_read": "carousel_cup_1", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "cup1_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Drilling rod\nis in cap 1", "font_size": "19px", "x": 405, "y": 800, "font_color": "#FFFFFF", "screen": 1}}, "carousel_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Carousel", "font_size": "22px", "x": 405, "y": 600, "font_color": "#FFFFFF", "screen": 1}}, "permission_indicator": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 80, "x": 490, "y": 450, "screen": 1, "field_to_read": "move_permission", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "button_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "ButtonWidget", "init_args": {}, "prep_args": {"width": 200, "height": 100, "x": 300, "y": 50, "text": "<PERSON><PERSON>", "color": "#e6ebf4", "fontsize": "20px", "core_flag": "btn_field"}}, "jacks_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "<PERSON><PERSON>", "font_size": "26px", "x": 63, "y": 855, "font_color": "#FFFFFF", "screen": 1}}, "tower_ok_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 30, "x": 35, "y": 720, "screen": 1, "field_to_read": "tower_ok_to_lock", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "tower_ok_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Tower is in position", "font_size": "19px", "x": 50, "y": 780, "font_color": "#FFFFFF", "screen": 1, "left": true}}, "permission_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Work\npermission", "font_size": "22px", "x": 365, "y": 505, "font_color": "#FFFFFF", "screen": 1}}, "messages_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "MessagesWidget", "init_args": {}, "prep_args": {"width": 625, "height": 475, "x": 1280, "y": 580, "screen_num": 3, "msg_colors": {"8": "#e01705", "4": "#f0d400", "2": "#50baeb"}, "filter_str": ["angle overflow", "timeout waiting for message", "Frame processing failed with a TF error", "service", "REMOTE_WAIT", "Asking", "permission", "REMOTE_PREPARE", "REMOTE to END_REMOTE"]}}, "machine_state_management": {"is_active": false, "file_name": "gui_modules", "class_name": "MachineStateManagement", "init_args": {}, "prep_args": {"state_machine": {"MainStateMachineNode": {"Main State Machine": {"idle": "Idle", "moving": "Driving", "leveling": "Leveling", "tower_tilt": "Tower tilt", "drilling": "Drilling", "shaft_buildup": "Shaft Build-up", "shaft_stow": "Shaft stow", "grounding": "Grounding", "remote_wait": "Wait for Remote", "remote_prepare": "Prepare for Remote", "remote": "In Remote", "end_remote": "Finishing Remote", "wait_after_level": "Waiting after leveling", "restore_string": "String restoring", "wait_before_level": "Waiting before leveling", "failure": "Failure"}}, "TowerControllerNode": {"Tower": {"idle": "Idle", "locking": "Locking", "unlocking": "Unlocking", "arm_closing": "Arm closing", "tilt_regulation": "Tilt Regulation", "failure": "Failure"}}, "DrillerNode": {"Drilling": {"idle": "Idle", "touchdown": "Touchdown", "overburden_pass": "Overburden pass", "drilling": "Drilling", "hard_rot": "Hard Rot", "pullup": "<PERSON><PERSON><PERSON>", "after_pullup": "After Pullup", "raise": "String Raising", "failure": "Failure", "wait_after_drill": "Waiting after drill", "pass_soft": "Pass soft", "unstuck": "Unstucking"}}, "ArmControllerNode": {"Arm": {"opening": "Opening", "open": "<PERSON><PERSON>", "closing": "Closing", "closed": "Close", "failure": "Failure"}}, "LevelerNode": {"Jacks": {"pulling": "Pulling", "pulled": "Pulled", "restore_pulled": "Restoring pulled", "leveling": "Leveling", "final_leveling": "Final Leveling", "holding": "Holding", "failure": "Failure"}}, "CarouselControllerNode": {"Carousel": {"idle": "Idle", "opening": "Carousel opening", "closing": "Carousel closing", "turn_cw": "Carousel turning cw", "turn_ccw": "Carousel turning ccw", "failure": "Failure"}}}, "widget_params": {"x": 5120, "y": 300, "width": 500, "height": 250, "color": "#D2D6E4", "font_size": "28px", "text_transform": "none", "font_weight": "normal"}, "confirm_params": {"color": "#fff", "background": "#4E5754", "background_ok": "#009B76", "background_no": "#E32636", "text_transform": "uppercase"}}}, "nodes_switch": {"is_active": false, "file_name": "gui_modules", "class_name": "NodesSwitch", "init_args": {}, "prep_args": {"x": 50, "y": 300, "width": 200, "height": 50, "btn_title": "None", "sm_name": "None", "state_name": "None"}}, "drill_now_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "SendHoleButton", "init_args": {}, "prep_args": {"x": 1530, "y": 380, "width": 180, "height": 230, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "font_color": "#D2D6E4", "label_font_size": "20px", "screen_num": 0, "url": "/api/drill-now"}}, "telemetry_display": {"is_active": true, "file_name": "gui_modules", "class_name": "MovingDataTranslator", "init_args": {}, "prep_args": {"x": 1300, "y": 50, "width": 800, "height": 130, "screen_num": 3, "telemetry": {"spindle_depth": {"name": "Spindle depth: ", "units": "m", "scale": 1, "smooth_k": 1}, "drill_feed_speed": {"name": "Drill feed speed: ", "units": "m/h", "scale": 3600, "smooth_k": 0.07}}, "lbl_name_params": {"color": "#D2D6E4", "font_size": "32px", "text_transform": "none", "font_weight": "normal"}, "lbl_value_params": {"color": "#D2D6E4", "font_size": "32px", "text_transform": "none", "font_weight": "bold"}}}, "stop_drill_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "FinishHoleButton", "init_args": {}, "prep_args": {"x": 1530, "y": 280, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "drop_action_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropActionButton", "init_args": {}, "prep_args": {"x": 1725, "y": 280, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "drop_tailing_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropTailingButton", "init_args": {}, "prep_args": {"x": 1725, "y": 380, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "recalib_air_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "RecalibAirButton", "init_args": {}, "prep_args": {"x": 1725, "y": 480, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "drop_err_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropDrillError", "init_args": {}, "prep_args": {"x": 1530, "y": 170, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "emg_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "HardEmergencyButton", "init_args": {}, "prep_args": {"x": 75, "y": 680, "width": 250, "height": 200, "btn_width": 150, "btn_height": 137, "btn_red_fontsize": "25px", "btn_green_fontsize": "24px", "screen_num": 0}}, "reboot_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "RebootButton", "init_args": {}, "prep_args": {"x": 50, "y": 920, "width": 280, "height": 100, "btn_width": 280, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "stop_move_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "FinishMovingButton", "init_args": {}, "prep_args": {"x": 1530, "y": 65, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "finish_buildup_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "FinishBuildupButton", "init_args": {}, "prep_args": {"x": 1725, "y": 65, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "finish_stow_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "FinishStowButton", "init_args": {}, "prep_args": {"x": 1725, "y": 170, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "tgbot_module": {"is_active": false, "file_name": "tgbot", "class_name": "TgBotClass", "init_args": {}, "prep_args": {}}, "move_permission_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "move_permission", "sound_file": "sounds/move_perm_beep.wav", "from_telemetry": true, "inverse": false}}, "disconnect_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "veh_active_states", "sound_file": "sounds/disconnect_wookie.wav", "from_telemetry": false, "inverse": false}}, "connect_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "veh_active_states", "sound_file": "sounds/buttondigital.mp3", "from_telemetry": false, "inverse": true}}, "maneuver_processing_text": {"is_active": false, "file_name": "gui_modules", "class_name": "ConditionalTextWidget", "init_args": {}, "prep_args": {"text": "Maneuver in process", "font_size": "19px", "x": 1280, "y": 550, "font_color": "#00FF00", "screen": 3, "left": true, "field_to_read": "maneuver_processing", "from_telemetry": true}}, "test_slider": {"is_active": true, "file_name": "gui_modules", "class_name": "CustomSliderWrapper", "init_args": {}, "prep_args": {"width": 800, "height": 155, "x": 100, "y": 200, "caption": "Test Slider", "fieldToRead": "test_field", "dictToRead": "test_dict", "minValue": 0, "maxValue": 100, "units": "%"}}, "test_depth_meter": {"is_active": true, "file_name": "gui_modules", "class_name": "DepthWidgetWrapper", "init_args": {}, "prep_args": {"width": 200, "height": 500, "x": 100, "y": 370, "dictToRead": "telemetry", "actualDepthField": "actual_depth", "targetDepthField": "target_depth", "drillPosField": "drill_pos"}}}