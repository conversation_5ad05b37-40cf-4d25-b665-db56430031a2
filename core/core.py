#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Standard library imports
import enum
import json
import os
import socket
import datetime
import logging
import threading
import time
from collections import defaultdict
from typing import Dict, Any

# Third-party imports
from PyQt6 import QtCore
from PyQt6.QtCore import pyqtSignal, QObject
from PyQt6.QtGui import QPixmap, QIcon
from PyQt6.QtWidgets import QWidget, QLabel, QDialog, QVBoxLayout

# Local imports
import socket_communicator.communicator as communicator
from auth import Auth

# Configure logging
logging.basicConfig(level=logging.INFO)

# Load configuration files
dir_path = os.path.dirname(os.path.abspath(__file__))

# Load main settings
config_path = os.path.join(dir_path, 'settings.json')
with open(config_path, 'r') as config:
    CONFIG = json.load(config)

# Load resource settings
resources_config_path = os.path.join(dir_path, 'resources_settings.json')
with open(resources_config_path, 'r') as config:
    RESOURCES_CONFIG = json.load(config)

BACKGROUND_CONFIG = {
    "q_mode": "_4k.png",
    "h_mode": "_2k.png",
    "default": ".jpg"
}

class RequestTypes(str, enum.Enum):
    """ Message types in the request packet """
    REM_ON = 'remote_on'     # Request to enable remote control
    REM_OFF = 'remote_off'   # Request to disable remote control
    STATE = 'state'          # Request for current state
    CONTROL = 'control'      # Control command


class ResponseTypes(str, enum.Enum):
    """ Message types in the response packet """
    ACK = 'ack'              # Acknowledgment response
    TELEMETRY = 'telemetry'  # Telemetry data response


class Communicator(object):
    """
    Handles communication with vehicles via socket connections.

    This class manages socket connections to multiple vehicles, sending requests
    and processing responses. It runs a separate thread for each vehicle to maintain
    continuous communication.

    Detailed description of the class operation:
    https://bit.ly/2ItXDD6
    """

    def __init__(self, core):
        self.core = core
        self.killed = False

        self.__clients = {}
        self.__client_threads = {}
        self.__last_msg_delays = {}

    def __init_client(self, vehid, ip, port):
        try:
            cli = communicator.create_client(
                ip=ip,
                port=port,
            )
            logging.info("Connected to {}".format(vehid))
        except socket.error:
            # logging.warning(
            #     "Could not connect to {}".format(vehid)
            # )
            cli = None

            time.sleep(1)

        self.__clients[vehid] = cli

    def prepare(self):
        """
        Initialize and save ZMQ client for each vehicle
        """
        vehid2address = CONFIG['VEHID2ADDRESS']
        for vehid, _ in vehid2address.items():
            # Initialize client connections
            self.__clients[vehid] = None
            self.core.veh_active_states[vehid] = False
            self.core.output_data_remote[vehid] = {}
            self.core.output_data[vehid] = {}

    def start(self):
        """
        Start communication threads for all vehicles.

        Creates and starts a separate thread for each vehicle to handle
        continuous communication with that vehicle.
        """
        for vehid in self.__clients.keys():
            self.__client_threads[vehid] = threading.Thread(target=self.client_worker,
                                                            args=(vehid,),
                                                            daemon=True)
            self.__client_threads[vehid].start()

    def kill(self):
        self.killed = True

    def __form_req(self, vehid):
        msg_data = {"controls": {}}
        if vehid in self.core.output_data.keys() and len(self.core.output_data[vehid].keys()):
            msg_type = RequestTypes.CONTROL
            msg_data.update(self.core.output_data[vehid])
            self.core.output_data[vehid] = {}
            if not (self.core.in_rc_vehid == vehid and not self.core.turn_off_rc):
                return msg_type, msg_data

        if self.core.in_rc_vehid == vehid and not self.core.turn_off_rc:
            msg_type = RequestTypes.CONTROL
            msg_data["controls"].update(self.core.output_data_remote[vehid])

        elif self.core.turn_on_rc_vehid == vehid:
            msg_type = RequestTypes.REM_ON

        elif self.core.in_rc_vehid == vehid and self.core.turn_off_rc:
            msg_type = RequestTypes.REM_OFF
            msg_data['main_mode'] = self.core.main_mode

        else:
            msg_type = RequestTypes.STATE
            msg_data['is_free'] = self.core.in_rc_vehid is None

        return msg_type, msg_data

    def __send_req(self, vehid, cli, req):
        """
        Returns:
            success (bool):
            msgtype (str):
            data (dict):
            ts (float):
        """
        try:
            # Serialize the request
            s = communicator.serialize(req)
            try:
                cli.sendall(s.encode('latin-1'))
            except AttributeError:
                cli.sendall(s)

            # Set a timeout on the socket
            timeout = CONFIG.get('disconnect_wait_time', 5)
            cli.settimeout(timeout)

            # Receive the response
            resp_raw = communicator.recv(
                sock=cli,
                stop=lambda: False,  # Keep original behavior
                timeout=timeout
            )

            # Reset socket timeout to default
            cli.settimeout(None)

            # If the response is None, raise an error
            if not resp_raw:
                raise socket.error("Connection closed or empty response")

            # Deserialize the response
            resp = communicator.deserialize(resp_raw)
            return True, resp['msgtype'], resp['data'], resp['ts']

        except socket.timeout:
            logging.error(f"Timeout while communicating with vehicle {vehid}")
            self.__clients[vehid] = None
            return False, None, None, time.time()

        except socket.error as e:
            logging.error(f"Socket error: {e}")
            logging.error(f'No connection to vehicle {vehid}')

            self.__clients[vehid] = None
            return False, None, None, time.time()

    def client_worker(self, vehid: str):
        """
        Worker thread function that handles communication with a specific vehicle.

        Forms and sends request packets to the vehicle and processes responses.
        This function runs in a separate thread for each vehicle.

        Args:
            vehid: The vehicle ID to communicate with
        """
        while not self.killed:
            self.core.veh_active_states["local_sim"] = True
            if not self.__clients[vehid]:
                ip = CONFIG['VEHID2ADDRESS'][vehid]['ip']
                port = CONFIG['VEHID2ADDRESS'][vehid]['port']
                self.__init_client(vehid, ip, port)

            # Работаем если установлено соединение с машиной
            client = self.__clients[vehid]
            if client:
                msg_type, msg_data = self.__form_req(vehid)

                req = dict(
                    client_id=CONFIG['iplace_id'],
                    msgtype=msg_type,
                    data=msg_data,
                    ts=time.time(),
                    delay=self.__last_msg_delays.get(vehid, 0)
                )
                success, resp_type, resp_data, prev_ts = self.__send_req(vehid, client, req)

                veh_was_responding = self.core.veh_active_states[vehid]
                if not success:
                    if veh_was_responding:
                        logging.warning(
                            'Vehicle #{} stopped responding'.format(vehid)
                        )
                        self.core.veh_active_states[vehid] = False
                        if self.core.selected_vehid == vehid:
                            self.core.selected_vehid = None
                        else:
                            print(self.core.selected_vehid)

                        if vehid in self.core.need_rc_vehids:
                            del self.core.need_rc_vehids[self.core.need_rc_vehids.index(vehid)]
                        if self.core.in_rc_vehid == vehid:
                            self.core.in_rc_vehid = None
                        elif self.core.watched_vehid == vehid:
                            self.core.watched_vehid = None


                elif success and not veh_was_responding:
                    logging.info(
                        'Vehicle #{} has responded'.format(vehid)
                    )
                    self.core.veh_active_states[vehid] = True

                    self.__last_msg_delays[vehid] = time.time() - prev_ts

                self.__handle_response(vehid, msg_type, resp_type, resp_data)
                time.sleep(0.1)

    def __handle_response(self,
                          vehid: str,
                          last_req_type: str,
                          resp_type: str,
                          resp_data: Dict[str, Any]):
        """
        Process the received response based on its type and the request type.

        This method updates the core state with the received data and handles
        different response types appropriately.

        Args:
            vehid: The vehicle ID that sent the response
            last_req_type: The type of request that triggered this response
            resp_type: The type of the received response
            resp_data: The content of the response
        """

        def handle_telemetry():
            if last_req_type == RequestTypes.CONTROL:
                self.core.telemetry[vehid] = resp_data['telemetry']
                self.core.cameras[vehid] = resp_data['cameras']
                try:
                    self.core.log_msgs[vehid].extend(resp_data['log_msgs'])
                except KeyError:
                    self.core.log_msgs[vehid] = resp_data['log_msgs']


            elif last_req_type == RequestTypes.STATE:
                self.core.telemetry[vehid] = resp_data['telemetry']
                self.core.cameras[vehid] = resp_data['cameras']
                self.core.mov_err[vehid] = resp_data['mov_err']
                self.core.sys_err[vehid] = resp_data['sys_err']
                try:
                    self.core.log_msgs[vehid].extend(resp_data['log_msgs'])
                except KeyError:
                    self.core.log_msgs[vehid] = resp_data['log_msgs']

                if resp_data['need_rem_ctrl']:
                    if vehid not in self.core.need_rc_vehids:
                        self.core.need_rc_vehids.append(vehid)

            if self.core.in_rc_vehid == vehid and not resp_data['in_remote']:
                self.core.in_rc_vehid = None
                self.core.watched_vehid = vehid

        def handle_ack():
            print(last_req_type)
            if last_req_type == RequestTypes.REM_ON:
                if resp_data['ack']:
                    self.core.in_rc_vehid = vehid
                    self.core.turn_on_rc_vehid = None
                    # Если от этой машины приходил запрос ДУ -
                    # отмечаем его обработанным
                    if vehid in self.core.need_rc_vehids:
                        del self.core.need_rc_vehids[self.core.need_rc_vehids.index(vehid)]
                else:
                    logging.warning('REM_ON-type request was declined')
                    self.core.turn_on_rc_vehid = None

            elif last_req_type == RequestTypes.REM_OFF:
                if resp_data['ack']:
                    self.core.turn_off_rc = False
                    self.core.in_rc_vehid = None
                else:
                    logging.warning('REM_OFF-type request was declined')
                    self.core.turn_off_rc = None

        handlers_map = {
            ResponseTypes.TELEMETRY: handle_telemetry,
            ResponseTypes.ACK: handle_ack,
        }
        response_handler = handlers_map.get(resp_type, None)
        if response_handler is None:
            logging.warning(
                'Unknown response type ({}) from veh #{}'.format(resp_type, vehid)
            )
            self.core.veh_active_states[vehid] = False
            return
        response_handler()


class ThreadSafeDict(defaultdict):
    """
    Thread-safe dictionary implementation.

    This class extends defaultdict to provide thread-safe access to dictionary
    operations by using a lock for all read and write operations.
    """

    def __init__(self, lock: threading.RLock, *args, **kwargs):
        """
        Initialize the thread-safe dictionary.

        Args:
            lock: Thread synchronization object
            *args: Additional positional arguments for defaultdict
            **kwargs: Additional keyword arguments for defaultdict
        """
        super(ThreadSafeDict, self).__init__(*args, **kwargs)
        self._lock = lock

    def __setitem__(self, key, item):
        """Thread-safe implementation of dictionary item assignment."""
        with self._lock:
            super(ThreadSafeDict, self).__setitem__(key, item)

    def __getitem__(self, key):
        """Thread-safe implementation of dictionary item access."""
        with self._lock:
            return super(ThreadSafeDict, self).__getitem__(key)

    def update(self, *args, **kwargs):
        """Thread-safe implementation of dictionary update method."""
        with self._lock:
            return super(ThreadSafeDict, self).update(*args, **kwargs)


class Core(QObject):
    """
    Data class used for exchanging data between modules.

    Detailed description of the class operation:
    https://bit.ly/2ItXDD6
    """
    newJoystickDataReady = pyqtSignal(dict)
    vehicle_changed = pyqtSignal()  # Сигнал для оповещения о смене активного vehicle

    def __init__(self, dev_mode=False, h_mode=False, q_mode=False, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create core_lock first to avoid infinite recursion
        super(Core, self).__setattr__("core_lock", threading.RLock())
        print('dev_mode is : %s' % dev_mode)
        self.dev_mode = dev_mode
        # Set screen resolution based on mode arguments
        self._screen_width = 3840 if q_mode else (2560 if h_mode else 1920)
        self._screen_height = 2160 if q_mode else (1440 if h_mode else 1080)

        self.hal_connection_status = False
        self.initialize_screens(dev_mode, q_mode, h_mode)
        self.log_file_name = 'log/' + datetime.datetime.today().strftime('%Y-%m-%d') + '_rmo_log.csv'

        self.first_row = ['start_alert', 'veh_id', 'msg_alert', 'category_alert'] if not \
            os.path.exists(self.log_file_name) else None

        self.veh_active_states = ThreadSafeDict(self.core_lock)
        self.telemetry = ThreadSafeDict(self.core_lock)
        # создадим пустые словари для всех vehid из настроек
        for single_vehid in CONFIG['VEHID2ADDRESS'].keys():
            self.telemetry[single_vehid] = {}
        self.cameras = ThreadSafeDict(self.core_lock)
        self.markers = ThreadSafeDict(self.core_lock)

        self.mov_err = ThreadSafeDict(self.core_lock)
        self.sys_err = ThreadSafeDict(self.core_lock)

        self.log_msgs = ThreadSafeDict(self.core_lock)
        self.camera_lock = threading.Lock()
        self.marker_lock = threading.Lock()

        self.config = CONFIG

        # Команды на отправку в ДУ  -
        # отправляется непрерывно
        self.output_data_remote = ThreadSafeDict(self.core_lock)
        # Объекты на отправку в любом режиме  -
        # отправляется единожды и очищается
        self.output_data = ThreadSafeDict(self.core_lock)

        # Машина, выбранная для просмотра
        self.selected_vehid = None
        # Машина, которую сейчас просматриваем
        self.watched_vehid = None
        # Машина, которой сейчас управляем
        self.in_rc_vehid = None

        # Машина, на которую рмо запросило ДУ
        self.turn_on_rc_vehid = None
        # Машина, которая сама запросила ДУ
        self.need_rc_vehids = []

        self.turn_off_rc = False

        self.main_mode = None
        self.is_smart_control = False

        self.cameras_mode = "front"

        self.current_layout_name = None
        self.nonlin_enabled = {'joystick1': True,
                               'joystick2': True,
                               'joystick3': True,
                               'pot_value': True, }
        self.current_layout = ThreadSafeDict(self.core_lock)

        self.auth = Auth(hal_ip=CONFIG.get("hal_ip"))
        self.cur_user = None
        self.cur_access_jwt = None
        self.cur_refresh_jwt = None
        self.next_hole_location = None

        self.joy_mode = {'joystick1': None, 'joystick2': None, 'joystick3': None}

    @property
    def screen_width(self) -> int:
        """Get the screen width based on the current mode."""
        return self._screen_width

    @property
    def screen_height(self) -> int:
        """Get the screen height based on the current mode."""
        return self._screen_height

    def getJoystickNonlinearity(self, target: str) -> bool:
        """
        Get the nonlinearity setting for a specific joystick or potentiometer.

        Args:
            target: The target joystick or potentiometer ('joystick1', 'joystick2', 'joystick3', or 'pot_value')

        Returns:
            The current nonlinearity setting (True or False)

        Raises:
            ValueError: If the target is not a valid joystick or potentiometer
        """
        valueToRead = self.nonlin_enabled.get(target)
        if valueToRead is not None:
            return self.nonlin_enabled[target]
        else:
            valid_targets = ['joystick1', 'joystick2', 'joystick3', 'pot_value']
            raise ValueError(
                f"Invalid target '{target}'. Valid targets are: {', '.join(valid_targets)}"
            )

    def get_background_image_path(self, q_mode: bool, h_mode: bool) -> str:
        """
        Get the path to the background image based on the screen resolution mode.

        Args:
            q_mode: Whether QHD mode (3840x2160) is enabled
            h_mode: Whether HD mode (2560x1440) is enabled

        Returns:
            The full path to the appropriate background image file
        """
        # Select the appropriate suffix based on the mode
        suffix = BACKGROUND_CONFIG["q_mode"] if q_mode else (
            BACKGROUND_CONFIG["h_mode"] if h_mode else BACKGROUND_CONFIG["default"]
        )

        # Construct and return the full path
        return os.path.join(RESOURCES_CONFIG["images_folder"], f"dark_bg{suffix}")

    def setJoystickNonlinearity(self, target: str, value: bool) -> None:
        """
        Set nonlinearity parameter for a joystick or potentiometer.

        Args:
            target: Target joystick or potentiometer ('joystick1', 'joystick2', 'joystick3', or 'pot_value')
            value: Boolean value indicating whether the target should use nonlinear response

        Raises:
            ValueError: If the target is not a valid joystick or potentiometer
        """
        logging.debug(f"Setting joystick nonlinearity - target: {target}, value: {value}")

        valueToChange = self.nonlin_enabled.get(target)
        if valueToChange is not None:
            self.nonlin_enabled[target] = value
        else:
            valid_targets = ['joystick1', 'joystick2', 'joystick3', 'pot_value']
            raise ValueError(
                f"Invalid target '{target}'. Valid targets are: {', '.join(valid_targets)}"
            )

    def initialize_screens(self, dev_mode: bool, q_mode: bool, h_mode: bool) -> None:
        """
        Initialize the main and sensor screens based on the current mode.

        This method creates and configures the main application windows with
        appropriate sizes, positions, and background images based on the
        current display mode.

        Args:
            dev_mode: Whether development mode is enabled
            q_mode: Whether QHD mode (3840x2160) is enabled
            h_mode: Whether HD mode (2560x1440) is enabled
        """
        # Load background images
        background_image_file_path = self.get_background_image_path(q_mode, h_mode)
        bg = QPixmap(background_image_file_path)
        bgl = QPixmap(background_image_file_path)

        if not dev_mode:
            base_width = self.screen_width
            base_height = self.screen_height

            self.main_screen = QWidget()
            self.main_screen.setAttribute(QtCore.Qt.WidgetAttribute.WA_DeleteOnClose, True)
            self.main_screen.move(int(base_width), int(0))
            self.main_screen.setFixedSize(int(base_width * 3), int(base_height))
            self.main_screen.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
            self.main_screen.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
            self.main_screen.setWindowTitle("Zyfra Drill Operator App")

            bgl1 = QLabel(self.main_screen)
            bgl1.setPixmap(bgl)
            bgl1.show()
            bgl2 = QLabel(self.main_screen)
            bgl2.setPixmap(bg)
            bgl2.move(int(base_width), int(0))
            bgl2.show()
            bgl3 = QLabel(self.main_screen)
            bgl3.setPixmap(bg)
            bgl3.move(int(base_width * 2), int(0))
            bgl3.show()

            self.sensor_screen = QWidget()
            self.sensor_screen.setAttribute(QtCore.Qt.WA_DeleteOnClose, True)
            self.sensor_screen.move(int(0), int(0))
            self.sensor_screen.setFixedSize(int(1920), int(1050))
            self.sensor_screen.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
            self.sensor_screen.setAttribute(QtCore.Qt.WA_TranslucentBackground)
            self.sensor_screen.setWindowTitle("Zyfra Drill Operator App")

            bgl = QLabel(self.sensor_screen)
            bgl.setStyleSheet("background-color: black;")
            bgl.setFixedSize(int(1920), int(1080))
            bgl.show()
        else:
            self.main_screen = QWidget()
            self.main_screen.move(int(0), int(0))
            self.main_screen.setFixedSize(int(1220), int(1080))
            # self.main_screen.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
            self.main_screen.setWindowFlags(QtCore.Qt.WindowType.Window)
            # self.main_screen.setAttribute(QtCore.Qt.WA_TranslucentBackground)
            self.main_screen.setWindowTitle("Zyfra Drill Operator App")

            bgl1 = QLabel(self.main_screen)
            bgl1.setPixmap(bgl)
            bgl1.show()
            # bgl2 = QLabel(self.main_screen)
            # bgl2.setPixmap(bg)
            # bgl2.move(int(1920), int(0))
            # bgl2.show()
            # bgl3 = QLabel(self.main_screen)
            # bgl3.setPixmap(bg)
            # bgl3.move(int(1920 * 2), int(0))
            # bgl3.show()

            self.sensor_screen = QWidget()
            self.sensor_screen.move(int(700), int(0))
            self.sensor_screen.setFixedSize(int(1610), int(940))
            # self.sensor_screen.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)
            self.sensor_screen.setWindowFlags(QtCore.Qt.WindowType.Window)
            self.sensor_screen.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
            self.sensor_screen.setWindowTitle("Zyfra Drill Operator App")

            bgl = QLabel(self.sensor_screen)
            # bgl.setPixmap(sensor_bg)
            bgl.setStyleSheet("background-color: black;")
            bgl.setFixedSize(int(1920), int(1080))
            bgl.show()

        logo_icon_path = os.path.join(RESOURCES_CONFIG["images_folder"], "zyfra_logo.png")
        self.main_screen.setWindowIcon(QIcon(logo_icon_path))
        self.sensor_screen.setWindowIcon(QIcon(logo_icon_path))

    def setSelectedVehid(self, vehid):
        """Set the selected vehicle ID."""
        old_vehid = self.selected_vehid
        self.selected_vehid = vehid
        # Если сменили выбранный автомобиль, эмитим сигнал
        if old_vehid != vehid:
            self.vehicle_changed.emit()

    def setWatchedVehid(self, vehid):
        """"
        Set new watched vehicle. Calls blocking function if
        some vehicle is connected right now (should be modal window).
        """
        logging.debug('Got WATCH request for vehid {}'.format(vehid))
        old_vehid = self.watched_vehid
        
        if self.in_rc_vehid is not None:
            currentVehid = self.in_rc_vehid
            # try to disconnect from another vehicle
            if self.tryToStopRemote():
                self.watched_vehid = vehid
            else:
                err = "failed to disconnect from {}".format(currentVehid)
                logging.warning(err)
        else:
            self.watched_vehid = vehid

        # Если сменили просматриваемый автомобиль, эмитим сигнал
        if old_vehid != vehid:
            self.vehicle_changed.emit()
            
        return True

    def setControlledVehid(self, vehid):
        """
        Set new controlled vehicle ID.
        
        Args:
            vehid: The vehicle ID to control
            
        Returns:
            True if successful, False otherwise
        """
        old_vehid = self.in_rc_vehid
        self.in_rc_vehid = vehid
        
        # Если сменили контролируемый автомобиль, эмитим сигнал
        if old_vehid != vehid:
            self.vehicle_changed.emit()
            
        return True

    def tryToStopRemote(self) -> bool:
        """
        Stop remote control for the currently controlled vehicle.

        Returns:
            bool: True if remote control was successfully stopped, False otherwise
        """
        # Set the flag to stop remote control
        self.turn_off_rc = True
        disconnectTime = CONFIG.get('disconnect_wait_time', 3)

        # Create a dialog to show the stoppage is processing
        dialog = QDialog()
        dialog.setWindowTitle("Stop remote control")
        dialog.setFixedSize(int(230), int(50))
        label = QLabel("Stoppage processing...")
        layout = QVBoxLayout()
        layout.addWidget(label)
        dialog.setLayout(layout)

        # Position the dialog in the center of the screen
        dialog.setGeometry(
            int(self.sensor_screen.geometry().center().x() - int(dialog.width()) / 2),
            int(self.sensor_screen.geometry().center().y() - int(dialog.height()) / 2),
            int(dialog.width()),
            int(dialog.height())
        )

        # Set a timer to automatically close the dialog after timeout
        timer = QtCore.QTimer(dialog)
        timer.setSingleShot(True)
        timer.timeout.connect(dialog.reject)
        timer.start(disconnectTime * 1000)  # Convert to milliseconds

        # Function to check if remote control was stopped
        def stop_remote():
            timeClicked = time.time()
            while time.time() - timeClicked < disconnectTime:
                # Check if remote control was stopped
                if not self.turn_off_rc and self.in_rc_vehid is None:
                    try:
                        dialog.accept()
                    except Exception as e:
                        logging.error(f"Error accepting dialog: {e}")
                    return True

                # Check if dialog was closed
                if not dialog.isVisible():
                    return False

                time.sleep(0.01)

            # Timeout occurred
            try:
                if dialog.isVisible():
                    dialog.reject()
            except Exception as e:
                logging.error(f"Error rejecting dialog: {e}")

            # Reset the flag
            self.turn_off_rc = False

            logging.warning(f"Failed to stop remote control (timeout)")
            return False

        # Start the thread to check for remote control stoppage
        stop_remote_thread = threading.Thread(target=stop_remote, daemon=True)
        stop_remote_thread.start()

        # Show the dialog and wait for it to close
        try:
            result = dialog.exec()
            return result == QDialog.DialogCode.Accepted and self.in_rc_vehid is None
        except Exception as e:
            logging.error(f"Dialog execution error: {e}")
            return False



    def disconnectFromAll(self) -> bool:
        """Disconnect from all vehicles.

        Returns:
            bool: True if successfully disconnected, False otherwise
        """
        # Check if we're controlling a vehicle
        currentVehid = self.in_rc_vehid
        if currentVehid is not None:
            logging.debug(f'Got DISCONNECT click for vehid {currentVehid}')
            if self.tryToStopRemote():
                logging.info('Stopped remote control, can disconnect')
                return True
            else:
                err = f"Failed to disconnect from {currentVehid}"
                logging.warning(err)
                return False
        else:
            # Check if we're watching a vehicle
            currentVehid = self.watched_vehid
            if currentVehid is not None:
                # Store old value to check if we need to emit the signal
                old_vehid = self.watched_vehid
                
                # Set watched_vehid to None
                self.watched_vehid = None
                
                # Emit signal if the value changed
                if old_vehid != self.watched_vehid:
                    logging.debug(f'Vehicle changed from {old_vehid} to None, emitting signal')
                    self.vehicle_changed.emit()
                
                logging.info('Unwatched vehicle, can disconnect')
                return True
            else:
                print('Already disconnected')
                return True

    def setParameter(self, vehid: str, parameter: str, value: Any) -> None:
        """
        Set a parameter value for a specific vehicle.

        Args:
            vehid: The vehicle ID to set the parameter for
            parameter: The name of the parameter to set
            value: The value to set for the parameter
        """
        newParam = {parameter: value}
        self.output_data[vehid].update(newParam)

    def setMovePermission(self, vehid: str, permission: bool) -> None:
        """
        Set movement permission for a specific vehicle.

        Args:
            vehid: The vehicle ID to set permission for
            permission: Whether the vehicle is permitted to move
        """
        mov_perm = {'move_permission': permission}
        self.output_data[vehid].update(mov_perm)

    def __getattribute__(self, name):
        """
        Thread-safe implementation of attribute access.

        Reads and returns values from the core in a locked thread to ensure
        thread safety when accessing attributes.

        Args:
            name: The name of the attribute to access

        Returns:
            The value of the requested attribute
        """
        with super(Core, self).__getattribute__("core_lock"):
            return super(Core, self).__getattribute__(name)

    def __setattr__(self, name, value):
        """
        Thread-safe implementation of attribute assignment.

        Sets the value of a core attribute in a locked thread to ensure
        thread safety when modifying attributes.

        Args:
            name: The name of the attribute to set
            value: The value to assign to the attribute
        """
        with super(Core, self).__getattribute__("core_lock"):
            super(Core, self).__setattr__(name, value)
