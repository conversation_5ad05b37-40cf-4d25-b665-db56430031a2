<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS><TS version="2.0" language="es" sourcelanguage="">
<context>
    <name>AngleIndicatorWidget</name>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="195"/>
        <source>Across: </source>
        <translation type="unfinished">Inclinación:</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="196"/>
        <source>Along: </source>
        <translation type="unfinished">Elevación:</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="202"/>
        <source>Tilt</source>
        <translation type="unfinished">Inclinación:</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="223"/>
        <source>Along</source>
        <translation type="unfinished">Elevación</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="236"/>
        <source>Across</source>
        <translation type="unfinished">Inclinación</translation>
    </message>
</context>
<context>
    <name>AuthButton</name>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="46"/>
        <source>Login</source>
        <translation type="unfinished">Iniciar</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="47"/>
        <source>Logout</source>
        <translation type="unfinished">Cerrar</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="119"/>
        <source>login</source>
        <translation type="unfinished">Usuario</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="120"/>
        <source>password</source>
        <translation type="unfinished">Contraseña</translation>
    </message>
</context>
<context>
    <name>AuthWidget</name>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="258"/>
        <source>No connection with server...</source>
        <translation type="unfinished">Sin conexión con el servidor...</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="216"/>
        <source>Can&apos;t log in</source>
        <translation type="unfinished">No puedo iniciar sesión</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="207"/>
        <source>Password field should not be empty</source>
        <translation type="unfinished">Contraseña no debe estar vacia</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="208"/>
        <source>Empty Password Field</source>
        <translation type="unfinished">Campo de contraseña vacio</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="188"/>
        <source>Can&apos;t Authorize user
error: </source>
        <translation type="unfinished">No se puede autorizar
error:</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="192"/>
        <source>Login Error</source>
        <translation type="unfinished">Error de inicio de sesión</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="200"/>
        <source>Login field should not be empty</source>
        <translation type="unfinished">Campo de inicio de sesión no debe estar vacio</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="201"/>
        <source>Empty Login Field</source>
        <translation type="unfinished">Campo de inicio de sesión vacio</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="250"/>
        <source>Can&apos;t logout user
error: </source>
        <translation type="unfinished">No se puede cerrar sesión
error de usuario:</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="254"/>
        <source>Logout Error</source>
        <translation type="unfinished">Error de cierre de sesión</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="259"/>
        <source>Can&apos;t log out</source>
        <translation type="unfinished">No puedo cerrar sesión</translation>
    </message>
</context>
<context>
    <name>ButtonInGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="64"/>
        <source>Front</source>
        <translation>Frontal</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="65"/>
        <source>Rear</source>
        <translation>Posterior</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="66"/>
        <source>Drilling</source>
        <translation>Perforación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="67"/>
        <source>Birdview</source>
        <translation>Vista de planta</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="80"/>
        <source>Drill</source>
        <translation>Perf.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="81"/>
        <source>Propel</source>
        <translation>Propulsión</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="72"/>
        <source>Normal</source>
        <translation>Roca competente</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="73"/>
        <source>Cracked</source>
        <translation>Roca fracturada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="74"/>
        <source>Hard</source>
        <translation>Roca dura </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="75"/>
        <source>Flooded</source>
        <translation>Mojada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="76"/>
        <source>Dry</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CategoryContainer</name>
    <message>
        <location filename="../ext_translations.tri" line="192"/>
        <source>U-Fork</source>
        <translation type="unfinished">Llave U</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="193"/>
        <source>Carousel</source>
        <translation type="unfinished">Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="194"/>
        <source>Pins</source>
        <translation type="unfinished">Pines</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="195"/>
        <source>Arm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="196"/>
        <source>Wrench</source>
        <translation type="unfinished">Caiman</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="197"/>
        <source>Jacks</source>
        <translation type="unfinished">Gatas</translation>
    </message>
</context>
<context>
    <name>ConditionalTextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="304"/>
        <source>Maneuver in process</source>
        <translation>Calculando maniobra</translation>
    </message>
</context>
<context>
    <name>ControllerPanel</name>
    <message>
        <location filename="../ext_translations.tri" line="3"/>
        <source>Driving</source>
        <translation>Conducción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="6"/>
        <source>           pull extend
     Jacks</source>
        <translation>Retraer gatos extendidos</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="7"/>
        <source>Drilling</source>
        <translation>Perforando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="8"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="24"/>
        <source>Feed</source>
        <translation>Cabezal</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="10"/>
        <source>Rotating</source>
        <translation>Rotación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="26"/>
        <source>Force</source>
        <translation>Fuerza</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="12"/>
        <source>Compressor</source>
        <translation>Compresor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="13"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="14"/>
        <source>Rear</source>
        <translation>Posterior</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="17"/>
        <source>Tower Tilt</source>
        <translation>Inclinación del mástil</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="18"/>
        <source>Tilt</source>
        <translation>Inclinación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="19"/>
        <source>Upper Pin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="20"/>
        <source>Lower Pin</source>
        <translation>Pin de torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="21"/>
        <source>  Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="22"/>
        <source>Extention</source>
        <translation>Extención</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="23"/>
        <source>Manipulator</source>
        <translation>Manipulador</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="25"/>
        <source>Turning</source>
        <translation>Girando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="27"/>
        <source>clamp  offset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="28"/>
        <source>   Fork</source>
        <translation>Llave U</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="29"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="30"/>
        <source>Supply</source>
        <translation>Alimentar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="31"/>
        <source>Angle</source>
        <translation>Ángulo</translation>
    </message>
</context>
<context>
    <name>CustomSlider</name>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="357"/>
        <source>Apply</source>
        <comment>send data from this widget to system</comment>
        <translation>Aplicar</translation>
    </message>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="360"/>
        <source>Save</source>
        <comment>Button caption that push value into settings file</comment>
        <translation>Guardar</translation>
    </message>
</context>
<context>
    <name>CustomSliderWrapper</name>
    <message>
        <location filename="../ext_translations.tri" line="58"/>
        <source>Feed pressure</source>
        <translation>Pull Down Presión</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="59"/>
        <source>bar</source>
        <translation>Bar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="60"/>
        <source>Rotation speed</source>
        <translation>RPM</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="61"/>
        <source>rpm</source>
        <translation>RPM</translation>
    </message>
</context>
<context>
    <name>DangerousFeature</name>
    <message>
        <location filename="../ext_translations.tri" line="55"/>
        <source>Dangerous feature!</source>
        <translation>¡Característica peligrosa!</translation>
    </message>
</context>
<context>
    <name>DangerousFeatureDescription</name>
    <message>
        <location filename="../ext_translations.tri" line="56"/>
        <source>Description!</source>
        <translation>Esta función supone una restricción en el control del equipo de máquina</translation>
    </message>
</context>
<context>
    <name>DialIndicator</name>
    <message>
        <location filename="../ext_translations.tri" line="33"/>
        <source>Revolutions
(revs/min) x10</source>
        <translation>RPM
(revs/min) x10</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="34"/>
        <source>Air
pressure (bar)</source>
        <translation>Presión de aire
(bar)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="38"/>
        <source>Drill Feed
pressure (psi)</source>
        <translation>Pull down presión
(psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="36"/>
        <source>Rotation
pressure (psi)</source>
        <translation>Rotación presión
(psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="35"/>
        <source>Air
pressure (psi)</source>
        <translation>Presión de aire
(psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="37"/>
        <source>Rotation torque
(klb*ft)</source>
        <translation>Presión de rotación
(klb*ft)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="39"/>
        <source>Drill Feed
pressure (klbf)</source>
        <translation>Pull down presión
(klbf)</translation>
    </message>
</context>
<context>
    <name>Distance2HoleWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="408"/>
        <source>Hole distance </source>
        <translation>Distancia al taladro</translation>
    </message>
</context>
<context>
    <name>DowntimesButton</name>
    <message>
        <location filename="../downtimes.py" line="552"/>
        <source>Has active downtime!</source>
        <translation>¡Hay una demora activa!</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="565"/>
        <source>Has downtime to finish!</source>
        <translation>¡Hay una demora para terminar!</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="573"/>
        <source>Create Downtime</source>
        <translation>Crear demora</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="283"/>
        <source>Authorization Needed</source>
        <translation>Autorización requerida</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="283"/>
        <source>You need to log in to perform this action.</source>
        <translation>Deben iniciar sesión para realizar esta acción.</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="319"/>
        <source>None</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="341"/>
        <source>Vehicle Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="341"/>
        <source>No valid vehicle to save downtime for.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="353"/>
        <source>Switched to New Downtime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="353"/>
        <source>A new active downtime was detected.
Your entered comment and type are now associated with the new downtime.
Please press &apos;Save downtime&apos; again to finalize these changes.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="385"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="381"/>
        <source>Failed to save downtime. Please try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="430"/>
        <source>Downtime Changed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="409"/>
        <source>A new active downtime was created automatically while you were editing.

Do you want to edit the newly created downtime instead?

If you choose &apos;Yes&apos;, your currently entered comment and downtime type will now be applied to the new downtime. The form will remain open with your input unchanged.
Press &apos;Save downtime&apos; again to finalize.

If you choose &apos;No&apos;, we will attempt to create a new downtime anyway.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="430"/>
        <source>The downtime you were editing has changed on the server.

Do you want to reload the latest downtime data?

If you choose &apos;Yes&apos;, your current inputs will be replaced with the server&apos;s data.
If you choose &apos;No&apos;, we will attempt to save your current changes anyway.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DowntimesFormDialog</name>
    <message>
        <location filename="../downtimes.py" line="24"/>
        <source>Downtime Edit</source>
        <translation type="unfinished">Editar demora</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="53"/>
        <source>End time</source>
        <translation type="unfinished">Hora de finalización</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="34"/>
        <source>I want to finish downtime record</source>
        <translation type="unfinished">Quiero terminar el registro de tiempo de demora</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="44"/>
        <source>Apply auto end time</source>
        <translation type="unfinished">Aplicar hora de finalización automática</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="45"/>
        <source>Set Current Time</source>
        <translation type="unfinished">Establecer hora actual</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="133"/>
        <source>Save downtime</source>
        <translation type="unfinished">Guardar</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="55"/>
        <source>Auto end time</source>
        <translation type="unfinished">Hora de finalización automática</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="63"/>
        <source>Downtime ID</source>
        <translation type="unfinished">ID de demora</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="64"/>
        <source>Vehicle ID</source>
        <translation type="unfinished">ID de máquina</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="65"/>
        <source>Type</source>
        <translation type="unfinished">Tipo</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="66"/>
        <source>Description</source>
        <translation type="unfinished">Descripción</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="67"/>
        <source>Start time</source>
        <translation type="unfinished">Hora de inicio</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="195"/>
        <source>Not available</source>
        <translation type="unfinished">No disponible</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="99"/>
        <source>End time must be later than start time and before the current time.</source>
        <translation type="unfinished">La hora de finalización debe ser posterior a la hora de inicio y anterior a la hora actual.</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="115"/>
        <source>Close downtime record</source>
        <translation type="unfinished">Cerrar registro de demora</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="137"/>
        <source>Not set</source>
        <translation type="unfinished">No seleccionado</translation>
    </message>
</context>
<context>
    <name>DowntimesTypes</name>
    <message>
        <location filename="../ext_translations.tri" line="410"/>
        <source>drill_maintenance</source>
        <translation>Mantenimiento de máquina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="411"/>
        <source>drill_maintenance_description</source>
        <translation>Mantenimiento de máquina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="412"/>
        <source>no_operator</source>
        <translation>Sin operador</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="413"/>
        <source>no_operator_description</source>
        <translation>Operador no asignado a la máquina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="414"/>
        <source>no_task</source>
        <translation>Sin tarea</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="415"/>
        <source>no_task_description</source>
        <translation>No hay tarea asignada a la máquina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="416"/>
        <source>robot_malfunction</source>
        <translation>Mal funcionamiento del robot</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="417"/>
        <source>robot_malfunction_description</source>
        <translation>El robot no funciona. Posibles motivos: sensores, cableado, fallo mecánico, software</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="418"/>
        <source>software_update</source>
        <translation>Actualización de software</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="419"/>
        <source>software_update_description</source>
        <translation>El software de la máquina se está actualizando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="420"/>
        <source>area_not_ready</source>
        <translation>El área no está lista</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="421"/>
        <source>area_not_ready_description</source>
        <translation>El área no está listo para trabajar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="422"/>
        <source>refueling</source>
        <translation>Recarga</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="423"/>
        <source>refueling_description</source>
        <translation>Esperando que se repongan los materiales</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="424"/>
        <source>robot_maintenance</source>
        <translation>Mantenimiento de robots</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="425"/>
        <source>robot_maintenance_description</source>
        <translation>Mantenimiento del robot (calibración, configuración)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="426"/>
        <source>manual_operation</source>
        <translation>Modo manual</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="427"/>
        <source>manual_operation_description</source>
        <translation>La máquina funciona en modo manual (el control de un conductor a bordo)</translation>
    </message>
</context>
<context>
    <name>DrillJoystickWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="86"/>
        <source>Driving</source>
        <translation>Conducción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="87"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="88"/>
        <source>Retract</source>
        <translation>Retraer</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="89"/>
        <source>Extend</source>
        <translation>Extender</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="90"/>
        <source>Right track</source>
        <translation>Lado cabina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="91"/>
        <source>Left track</source>
        <translation>Lado no cabina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="92"/>
        <source>Back</source>
        <translation>Retroceder</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="93"/>
        <source>Forth</source>
        <translation>Avanzar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="95"/>
        <source>Drilling</source>
        <translation>Perf.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="96"/>
        <source>Feed force</source>
        <translation>Pull down</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="97"/>
        <source>Rotation</source>
        <translation>Rotación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="100"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="101"/>
        <source>Open</source>
        <translation>Abrir</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="102"/>
        <source>Close</source>
        <translation>Cerrar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="133"/>
        <source>Feed</source>
        <translation>Cabezal</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="134"/>
        <source>Up</source>
        <translation>Subir</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="135"/>
        <source>Down</source>
        <translation>Bajar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="136"/>
        <source>Compressor</source>
        <translation>Compresor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="137"/>
        <source>On</source>
        <translation>Encender</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="138"/>
        <source>Off</source>
        <translation>Apagar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="110"/>
        <source>Leveling</source>
        <translation>Nivelación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="111"/>
        <source>All jacks</source>
        <translation>Todas las gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="112"/>
        <source>Rear</source>
        <translation>Trasera</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="114"/>
        <source>Tower</source>
        <translation>Torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="115"/>
        <source>Inclined Pin</source>
        <translation>Pin inclinado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="116"/>
        <source>Tilt</source>
        <translation>Inclinanción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="119"/>
        <source>Vertical Pin</source>
        <translation>Pin vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="121"/>
        <source>Wrench</source>
        <translation>Caiman</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="122"/>
        <source>Release</source>
        <translation>Liberar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="123"/>
        <source>Turn</source>
        <translation>Girar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="124"/>
        <source>Stow</source>
        <translation>Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="125"/>
        <source>Swing in</source>
        <translation>Cerrado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="126"/>
        <source>Fork</source>
        <translation>Llave u</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="127"/>
        <source>Stop</source>
        <translation>Parar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="129"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="130"/>
        <source>Index</source>
        <translation>Indice</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="98"/>
        <source>CCW</source>
        <translation>Antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="99"/>
        <source>CW</source>
        <translation>Horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="117"/>
        <source>Raise</source>
        <translation>Elevar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="118"/>
        <source>Lower</source>
        <translation>Bajar</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="56"/>
        <source>Red button</source>
        <translation>Boton rojo</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="59"/>
        <source>Green button</source>
        <translation>Boton verde</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="139"/>
        <source>Red and Green Buttons</source>
        <translation>Botones rojo y verde</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="142"/>
        <source>Knob widget</source>
        <translation>Perilla</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="145"/>
        <source>Left Joystick</source>
        <translation>Joystick izquierdo</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="148"/>
        <source>Center Joystick</source>
        <translation>Joystick central</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="151"/>
        <source>Right Joystick</source>
        <translation>Joystick derecho</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="154"/>
        <source>Tumbler Widget</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="15"/>
        <source>Left</source>
        <translation>Lado no cabina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="16"/>
        <source>Right</source>
        <translation>Lado cabina</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="131"/>
        <source>Dust flaps</source>
        <translation>Cortina de polvo</translation>
    </message>
</context>
<context>
    <name>DropActionButton</name>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="16"/>
        <source>Confirm current task cancelling</source>
        <translation type="unfinished">Confirmar cancelación de tarea actual</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="25"/>
        <source>Permitted to qualified Engineers or Developers only</source>
        <translation type="unfinished">Permitido solo para ingenieros calificados </translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="27"/>
        <source>Wrong password</source>
        <translation type="unfinished">Contraseña incorrecta</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="84"/>
        <source>Unusual Action</source>
        <translation type="unfinished">Acción inusual</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="84"/>
        <source>
                                            Drilling in the process, can&apos;t cancel task right now.
                                            Enter password if still want to proceed
                                            </source>
        <translation type="unfinished">Perforación en proceso, no se puede cancelar la tarea ahora mismo
Ingresar contraseña si aún quiere proceder</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="41"/>
        <source>Cancel
task</source>
        <translation>Cancelar
tarea</translation>
    </message>
</context>
<context>
    <name>DropDrillError</name>
    <message>
        <location filename="../ext_translations.tri" line="42"/>
        <source>Clear
error</source>
        <translation>Borrar
error</translation>
    </message>
</context>
<context>
    <name>DropTailingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="43"/>
        <source>Clear
tailing</source>
        <translation>Eliminar
limites</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="44"/>
        <source>Detected tailing will be cleared</source>
        <translation>Obstáculos serán eliminados</translation>
    </message>
</context>
<context>
    <name>DynamicActionButton</name>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="66"/>
        <source>Finish
Action</source>
        <translation type="unfinished">Finalizar
Acción</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="48"/>
        <source>Finish
Drilling</source>
        <translation type="unfinished">Finalizar
Perforación</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="53"/>
        <source>Finish
Stow</source>
        <translation type="unfinished">Varilla
removida</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="58"/>
        <source>Finish
Buildup</source>
        <translation type="unfinished">Finalizar
Ensamblaje</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="63"/>
        <source>Finish
Moving</source>
        <translation type="unfinished">Finalizar
Movimiento</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="16"/>
        <source>WARNING: Before finishing rod buildup, make sure you have completed the process manually!
Incorrect completion may damage the drilling rig.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="17"/>
        <source>Are you sure you want to finish rod buildup?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="18"/>
        <source>Finish Rod Buildup</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="24"/>
        <source>WARNING: Before finishing rod stow, make sure you have completed the process manually!
Incorrect completion may damage the drilling rig.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="25"/>
        <source>Are you sure you want to finish rod stow?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="26"/>
        <source>Finish Rod Stow</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DynamicLabel</name>
    <message>
        <location filename="../ext_translations.tri" line="176"/>
        <source>Opened</source>
        <translation type="unfinished">Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="177"/>
        <source>Closed</source>
        <translation type="unfinished">Cerrado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="178"/>
        <source>Extended</source>
        <translation type="unfinished">Extendido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="179"/>
        <source>Retracted</source>
        <translation type="unfinished">Retraido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="180"/>
        <source>Index 1</source>
        <translation type="unfinished">Indice 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="181"/>
        <source>Index 2</source>
        <translation type="unfinished">Indice 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="182"/>
        <source>Stowed</source>
        <translation type="unfinished">Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="183"/>
        <source>Engaged</source>
        <translation type="unfinished">Cerrado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="186"/>
        <source>Removed</source>
        <translation type="unfinished">Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="188"/>
        <source>No data</source>
        <translation type="unfinished">Sin datos</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="184"/>
        <source>Gripped</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="185"/>
        <source>Released</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="189"/>
        <source>Locked</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="187"/>
        <source>Moving</source>
        <translation type="unfinished">Conducción</translation>
    </message>
</context>
<context>
    <name>EliminateFirstLevelRestrictionsButton</name>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="123"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="123"/>
        <source>Wrong password</source>
        <translation type="unfinished">Contraseña incorrecta</translation>
    </message>
</context>
<context>
    <name>EliminateFullRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="52"/>
        <source>All
Restrictions
off</source>
        <translation>Eliminar
todoas las
restricciones</translation>
    </message>
</context>
<context>
    <name>EliminateFullRestrictionsButton</name>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="192"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="192"/>
        <source>Wrong password</source>
        <translation type="unfinished">Contraseña incorrecta</translation>
    </message>
</context>
<context>
    <name>EliminateRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="51"/>
        <source>Eliminate
Restrictions</source>
        <translation>Eliminar
restricciones</translation>
    </message>
</context>
<context>
    <name>EmergencyDialog</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="757"/>
        <source>Emergency</source>
        <translation>Parada de emergencia</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="772"/>
        <source>&lt;b&gt;Emergency was set for the {} vehicle.&lt;/b&gt;&lt;br&gt;&lt;br&gt;</source>
        <translation>&amp;lt;b&amp;gt; La parada de emergencia se estableció para el vehículo {}.&amp;lt;/b&amp;gt;&amp;lt;br&amp;gt;&amp;lt;br&amp;gt;</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="773"/>
        <source>Operator can activate the emergency mode with a button on the control panel or remotely with a radio remote control button. &lt;br&gt;&lt;br&gt;Motor would be automatically stopped in this mode. &lt;br&gt;&lt;br&gt;Please identify the problem that caused the emergency and eliminate it. Verify with the cameras that the operation is safe. &lt;br&gt;&lt;br&gt;Afterwards, you can reset the emergency with the button below. &lt;br&gt;&lt;br&gt;To hide this window, press the cancel button. You can return to this window later by pressing the emergency button in the vehicle selection window.</source>
        <translation>El operador puede activar la parada de emergencia con el boton en el panel del RMO o con la parada de emergencia E Stop, El motor se detendra inmediatamente en este modo

Identificar el problema que causo esta parada, informelo y verifique por las camaras si la operacion es segura

Despues se podra reiniciar las operaciones desactivando el boton fisicamente

Para ocultar esta ventana presional el boton ¨Cancelar&amp;quot;</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="784"/>
        <source>Reset emergency</source>
        <translation>Restablecer la emergencia</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="785"/>
        <source>Hide this dialog</source>
        <translation>Ocultar este diálogo</translation>
    </message>
</context>
<context>
    <name>EmergencySetter</name>
    <message>
        <location filename="../gui_modules/controls/emergency_setter.py" line="25"/>
        <source>DENY MOVEMENT</source>
        <translation type="unfinished">Denegar movimiento</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/emergency_setter.py" line="32"/>
        <source>PERMIT MOVEMENT</source>
        <translation type="unfinished">Permitir movimiento</translation>
    </message>
</context>
<context>
    <name>FinishBuildupButton</name>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="25"/>
        <source>Attention!
Make sure that the new rod is extended
Fork and key is retracted</source>
        <translation type="unfinished">Atencion
Verificar que la nueva barra esta extendida
Y la llave U esta retraida</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="27"/>
        <source>Attention!</source>
        <translation type="unfinished">Atención</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="51"/>
        <source>End
Extending</source>
        <translation type="unfinished">Varilla
agregada</translation>
    </message>
</context>
<context>
    <name>FinishHoleButton</name>
    <message>
        <location filename="../ext_translations.tri" line="45"/>
        <source>Finish
drilling</source>
        <translation>Terminar
Perf.</translation>
    </message>
</context>
<context>
    <name>FinishMovingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="46"/>
        <source>Finish
moving</source>
        <translation>Finalizar
movimiento</translation>
    </message>
</context>
<context>
    <name>FinishStowButton</name>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="26"/>
        <source>Attention!
Make sure the boring rod is not in the borehole,
Fork and Key are retracted away!</source>
        <translation type="unfinished">Atencion
Verificar que la nueva barra esta extendida
Y la llave U esta retraida</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="28"/>
        <source>Attention!</source>
        <translation type="unfinished">Atención!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="52"/>
        <source>Finish
Stow</source>
        <translation type="unfinished">Varilla
removida</translation>
    </message>
</context>
<context>
    <name>HardEmergencyButton</name>
    <message>
        <location filename="../gui_modules/buttons/hard_emergency_button.py" line="82"/>
        <source>Emergency
Stop</source>
        <translation type="unfinished">Parada de
emergencia</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/hard_emergency_button.py" line="91"/>
        <source>Continue
working</source>
        <translation type="unfinished">Continuar trabajando</translation>
    </message>
</context>
<context>
    <name>InputDialog</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="230"/>
        <source> description</source>
        <translation type="unfinished">descripción</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="239"/>
        <source>Event description</source>
        <translation type="unfinished">Descripción del evento</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="325"/>
        <source>From</source>
        <comment>for messaging: _from_ vehicle </comment>
        <translation type="unfinished">De</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="325"/>
        <source>At</source>
        <comment>for messaging: received _at_ time</comment>
        <translation type="unfinished">Recibido a tiempo</translation>
    </message>
</context>
<context>
    <name>JoystickWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="652"/>
        <source>Top Action</source>
        <translation>Accion superior</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="653"/>
        <source>Bottom Action</source>
        <translation>Accion inferior</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="654"/>
        <source>0.0%</source>
        <translation>0.0%</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="659"/>
        <source>Nonlinear</source>
        <translation>No lineal</translation>
    </message>
</context>
<context>
    <name>KnobViewWithCaption</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="535"/>
        <source>Nonlinear</source>
        <comment>Toggle Switch caption</comment>
        <translation>No lineal</translation>
    </message>
</context>
<context>
    <name>MachineStateManagement</name>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="80"/>
        <source>Unlock</source>
        <translation type="unfinished">Desbloquear</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="112"/>
        <source>Lock</source>
        <translation type="unfinished">Bloquear</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="128"/>
        <source>Commit</source>
        <translation type="unfinished">Aceptar</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="136"/>
        <source>Cancel</source>
        <translation type="unfinished">Cancelar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="200"/>
        <source>Idle</source>
        <translation>Inactivo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="202"/>
        <source>Driving</source>
        <translation>Conduciendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="245"/>
        <source>Leveling</source>
        <translation>Nivelando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="204"/>
        <source>Tower tilt</source>
        <translation>Inclinacion de torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="226"/>
        <source>Drilling</source>
        <translation>Perforando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="206"/>
        <source>Shaft Build-up</source>
        <translation>Agregar barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="207"/>
        <source>Shaft stow</source>
        <translation>Sustraer barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="208"/>
        <source>Grounding</source>
        <translation>Recogiendo gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="209"/>
        <source>Wait for Remote</source>
        <translation>Esperando control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="210"/>
        <source>Prepare for Remote</source>
        <translation>Preparando para control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="211"/>
        <source>In Remote</source>
        <translation>En control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="212"/>
        <source>Finishing Remote</source>
        <translation>Terminando control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="213"/>
        <source>Waiting after leveling</source>
        <translation>Esperando despues de nivelacion</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="214"/>
        <source>String restoring</source>
        <translation>Movimiento de cadena</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="215"/>
        <source>Waiting before leveling</source>
        <translation>Esperando antes de nivelacion</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="216"/>
        <source>Failure</source>
        <translation>Falla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="217"/>
        <source>Locking</source>
        <translation>Bloqueando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="218"/>
        <source>Unlocking</source>
        <translation>Desbloqueando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="219"/>
        <source>Arm closing</source>
        <translation>Cierre de rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="220"/>
        <source>Tilt Regulation</source>
        <translation>Regulacion de inclinacion</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="221"/>
        <source>Touchdown</source>
        <translation>Emboquillando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="225"/>
        <source>Overburden pass</source>
        <translation>Pasando relleno</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="227"/>
        <source>Hard Rot</source>
        <translation>Atascandose</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="228"/>
        <source>Pullup</source>
        <translation>Subiendo cabezal</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="229"/>
        <source>After Pullup</source>
        <translation>Despues de jalar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="230"/>
        <source>String Raising</source>
        <translation>Levantando cabezal</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="231"/>
        <source>Waiting after drill</source>
        <translation>Esperando despues de perforar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="232"/>
        <source>Pass soft</source>
        <translation>Pase suave</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="233"/>
        <source>Tower</source>
        <translation>Torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="234"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="235"/>
        <source>Opening</source>
        <translation>Abriendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="237"/>
        <source>Closing</source>
        <translation>Cerrando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="238"/>
        <source>Close</source>
        <translation>Cerrado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="239"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="241"/>
        <source>Pulling</source>
        <translation>Retrayendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="242"/>
        <source>Pulled</source>
        <translation>Retraido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="243"/>
        <source>Restoring pulled</source>
        <translation>Recogiendo gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="247"/>
        <source>Holding</source>
        <translation>Nivelado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="248"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="249"/>
        <source>Carousel opening</source>
        <translation>Apertura de carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="250"/>
        <source>Carousel closing</source>
        <translation>Cierre de carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="251"/>
        <source>Carousel turning cw</source>
        <translation>Carrusel giro horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="252"/>
        <source>Carousel turning ccw</source>
        <translation>Carrusel giro antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="199"/>
        <source>Main State</source>
        <translation>Estatus equip.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="201"/>
        <source>Init</source>
        <translation>Iniciando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="244"/>
        <source>Lowering jacks</source>
        <translation>Extendiendo las gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="240"/>
        <source>Dust flaps</source>
        <translation>Cor. de polvo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="236"/>
        <source>Open</source>
        <translation>Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="223"/>
        <source>Lifting up</source>
        <translation>Recogiendo gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="246"/>
        <source>Final leveling</source>
        <translation>Terminando nivelacion</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="224"/>
        <source>Unstucking</source>
        <translation>Destrabando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="253"/>
        <source>Planner</source>
        <translation>Planificador de conducción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="254"/>
        <source>Moving</source>
        <translation>Conducción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="255"/>
        <source>Approach</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="256"/>
        <source>Computing</source>
        <translation>Cálculo</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="88"/>
        <source>Dangerous feature</source>
        <translation type="unfinished">Función peligrosa</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="89"/>
        <source>You are activating a dangerous function.
If used incorrectly, there is a risk of damaging the machine.
Continue only if you know what you are doing.

Enter your password:</source>
        <translation type="unfinished">Estás activando una función peligrosa.
Si se utiliza incorrectamente, existe el riesgo de dañar la máquina.
Continúe sólo si sabe lo que está haciendo.

Ingresa tu contraseña:</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="92"/>
        <source>Ok</source>
        <translation type="unfinished">ok</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="257"/>
        <source>Initial Check</source>
        <translation>Revisión inicial</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="258"/>
        <source>Unstuck Down</source>
        <translation>Desatascar hacia abajo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="259"/>
        <source>Unstuck Spin</source>
        <translation>Desatascar girando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="260"/>
        <source>Unstuck Up</source>
        <translation>Desatascar hacia arriba</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="261"/>
        <source>Rod changer</source>
        <translation>Cambiador de varillas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="262"/>
        <source>Aligning rod for cup</source>
        <translation>Alineación de varilla para copa</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="263"/>
        <source>Aligning rod for fork</source>
        <translation>Alineación de varilla para llave u</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="264"/>
        <source>Approaching rod for carousel</source>
        <translation>Acercamiento a varilla para carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="265"/>
        <source>Approaching rod for fork</source>
        <translation>Acercamiento a varilla para llave u</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="266"/>
        <source>Apply wrench</source>
        <translation>Aplicar llave</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="267"/>
        <source>Brakeout</source>
        <translation>Desacople</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="268"/>
        <source>Closing arm</source>
        <translation>Cierre del soporte de varilla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="269"/>
        <source>Closing carousel</source>
        <translation>Cierre del carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="270"/>
        <source>Closing wrench</source>
        <translation>Cierre de la llave</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="271"/>
        <source>Detaching rod</source>
        <translation>Desacople de varilla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="272"/>
        <source>Detaching rod in cup</source>
        <translation>Desacople de varilla en copa</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="273"/>
        <source>Lift head to pull depth</source>
        <translation>Elevar rotador a profundidad de extracción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="274"/>
        <source>Finish</source>
        <translation>Finalizar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="275"/>
        <source>Inserting rod in cup</source>
        <translation>Inserción de varilla en copa</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="276"/>
        <source>Lifting head to carousel</source>
        <translation>Elevación del rotador al carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="277"/>
        <source>Closing fork</source>
        <translation>Cierre de la llave u</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="278"/>
        <source>Screwing new rod</source>
        <translation>Atornillado de nueva varilla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="279"/>
        <source>Opening arm</source>
        <translation>Apertura del soporte de varilla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="280"/>
        <source>Opening carousel</source>
        <translation>Apertura del carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="281"/>
        <source>Opening fork and arm</source>
        <translation>Apertura de llave u y soporte de varilla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="282"/>
        <source>Opening wrench</source>
        <translation>Apertura de la llave</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="283"/>
        <source>Pull out</source>
        <translation>Extracción</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="284"/>
        <source>Screwing</source>
        <translation>Atornillado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="285"/>
        <source>Turn shaft ccw</source>
        <translation>Giro del eje en sentido antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="286"/>
        <source>Turn shaft cw</source>
        <translation>Giro del eje en sentido horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="287"/>
        <source>Turn shaft cw in carousel cup</source>
        <translation>Giro del eje en sentido horario en copa del carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="288"/>
        <source>Opening fork</source>
        <translation>Apertura de la llave u</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="289"/>
        <source>Fork</source>
        <translation>Llave u</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="290"/>
        <source>Missed rod</source>
        <translation>Fallo al enganchar las caras planas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="291"/>
        <source>Turning CW</source>
        <translation>Giro en sentido horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="292"/>
        <source>Turning CCW</source>
        <translation>Giro en sentido antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="293"/>
        <source>Turning wrench</source>
        <translation>Giro de la llave</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="294"/>
        <source>Releasing wrench</source>
        <translation>Liberación de la llave</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="295"/>
        <source>Wrench</source>
        <translation>Caiman</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="296"/>
        <source>Wrench is open</source>
        <translation>Llave abierta</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="297"/>
        <source>Wrench is closed</source>
        <translation>Caiman cerrada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="298"/>
        <source>Init Check</source>
        <translation>Inicialización</translation>
    </message>
</context>
<context>
    <name>MapWidget</name>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="80"/>
        <source>Auto Center: ON</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="88"/>
        <source>Reset View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>Auto Center: {0}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>ON</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>OFF</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="759"/>
        <source>Distance to hole: -- m</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="786"/>
        <source>Distance to hole: {0:.2f} m</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MessageLogs</name>
    <message>
        <location filename="../ext_translations.tri" line="306"/>
        <source>100000101</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="307"/>
        <source>fucking_code_123</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MessageTypeColour</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="279"/>
        <source>Red</source>
        <translation type="unfinished">Rojo</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="281"/>
        <source>Yellow</source>
        <translation type="unfinished">Amarillo</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="283"/>
        <source>Orange</source>
        <translation type="unfinished">Naranja</translation>
    </message>
</context>
<context>
    <name>MessagesWidgetMaximized</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="147"/>
        <source>Message Log</source>
        <translation type="unfinished">Registro de mensajes</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="165"/>
        <source>Debug Mode</source>
        <translation type="unfinished">Modo de depuración de errores</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="312"/>
        <source>ALREADY_DONE</source>
        <translation>El comando fue ejecutado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="313"/>
        <source>WAIT_FINISH</source>
        <translation>No se puede aceptar tarea/comando mientras se esta ejecutando otro</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="314"/>
        <source>LOST_HAL_CONN</source>
        <translation>Sin conexión al servidor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="315"/>
        <source>CAN_NOT_SWITCH_TO_RC</source>
        <translation>No se puede cambiar a modo control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="318"/>
        <source>RC_ROTATE_CAROUSEL_IDX1</source>
        <translation>Usar control remoto para rotar carrusel en sentido horario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="319"/>
        <source>RC_ROTATE_CAROUSEL_IDX2</source>
        <translation>Usar control remoto para rotar carrusel en sentido antihorario</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="320"/>
        <source>RC_CLOSE_CAROUSEL</source>
        <translation>Usar control remoto para cerrar carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="321"/>
        <source>RC_OPEN_CAROUSEL</source>
        <translation>Usar control remoto para abrir carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="322"/>
        <source>RC_OPEN_ARM</source>
        <translation>Usar control remoto para abrir el rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="323"/>
        <source>RC_CLOSE_ARM</source>
        <translation>Usar control remoto para cerrar el rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="326"/>
        <source>RC_MOVE</source>
        <translation>Usar control remoto para mover la perforadora al siguiente taladro</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="327"/>
        <source>RC_UNSTUCK</source>
        <translation>Usar control remoto para liberar la broca del atascamiento</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="328"/>
        <source>RC_LEVELING</source>
        <translation>Usar control remoto para nivelar/retraer gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="330"/>
        <source>RC_LIFT_STRING</source>
        <translation>Usar control remoto para levantar columna de perforación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="332"/>
        <source>RC_RODE_CHANGE</source>
        <translation>Usar control remoto para agregar una barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="333"/>
        <source>RC_LOCK_TOWER</source>
        <translation>Usar control remoto para asegurar la torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="334"/>
        <source>RC_UNLOCK_TOWER</source>
        <translation>Usar control remoto para liberar la torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="335"/>
        <source>RC_TOWER_TILT</source>
        <translation>Usar control remoto para ajustar el ángulo de la torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="336"/>
        <source>COMPRESSOR_FAILURE</source>
        <translation>Falla de compresor detectada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="338"/>
        <source>WRENCH_SWITCH_FAILURE</source>
        <translation>Falta de data en el wrench esta fallando LTP</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="340"/>
        <source>IBOX_NO_CONN</source>
        <translation>Sin conexicon con el Zyfrabox</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="341"/>
        <source>IPLACE_NO_CONN</source>
        <translation>Sin conección con el control room</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="348"/>
        <source>RESTORING_PULLED_JACKS</source>
        <translation>Se requiere corregir nivelacion manualmente</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="351"/>
        <source>RESTORED_STRING</source>
        <translation>Columna de perforación recogida</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="352"/>
        <source>STARTING_NEW_ACT</source>
        <translation>Iniciando nueva tarea</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="353"/>
        <source>READY_FOR_NEW_ACT</source>
        <translation>Listo para aceptar nueva tarea</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="354"/>
        <source>CAN_NOT_DRILL_CUR_HOLE</source>
        <translation>No se puede perforar el taladro, pasar al siguiente taladro</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="355"/>
        <source>CAN_NOT_ACCEPT_ACTION</source>
        <translation>No se puede aceptar una nueva tarea por el momento</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="342"/>
        <source>LEFT_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>El sensor de la gata izquierda retraida esta fallando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="343"/>
        <source>RIGHT_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>El sensor de la gata derecha retraida esta fallando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="344"/>
        <source>REAR_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>Los sensores de las gatas traseras retraidas estan fallando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="345"/>
        <source>CLOSING_ARM_PULLING_JACKS</source>
        <translation>Nueva tarrea, cerrando rod support y extendiendo gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="346"/>
        <source>WRONG_ARM_STATE</source>
        <translation>Valor equivocado en Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="347"/>
        <source>HEAD_TOO_LOW</source>
        <translation>Posicion de la broca muy baja, subir con teleremoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="324"/>
        <source>RC_OPEN_DUST_FLAPS</source>
        <translation>Error abriendo cortina de polvo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="325"/>
        <source>RC_CLOSE_DUST_FLAPS</source>
        <translation>Error cerrando cortina de polvo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="329"/>
        <source>RC_LEVEL_TOO_LOW</source>
        <translation>Nivelar la maquina en teleremoto correctamente.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="356"/>
        <source>COLLISION_PREVENTER_STOPPAGE</source>
        <translation>Objeto cercano parando la perforadora</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="215"/>
        <source>Save recent onboard bags for further analysis?</source>
        <translation type="unfinished">¿Guardar los logs recientes para un análisis posterior?</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="216"/>
        <source>Save event?</source>
        <translation type="unfinished">¿Salvar el evento?</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="316"/>
        <source>DEPTH_CORRECTION_FAILED</source>
        <translation>Calculo de profundidad errado, revisar coordenadas o gps</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="337"/>
        <source>LEVEL_SENSOR_FAILURE</source>
        <translation>Datos de nivel de plataforma no válidos</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="349"/>
        <source>NO_RTK</source>
        <translation>Datos GPS no válidos (sin RTK)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="350"/>
        <source>ANGLES_ABOVE_LIMITS</source>
        <translation>Ángulos de inclinación fuera del rango permitido </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="357"/>
        <source>NO_FRONT_LIDAR</source>
        <translation>No hay datos del lidar frontal</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="358"/>
        <source>NO_REAR_LIDAR</source>
        <translation>No hay datos del lidar trasero</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="317"/>
        <source>UNEVEN_TERRAIN</source>
        <translation>Superficie irregular, reduciendo velocidad</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="359"/>
        <source>SLOW_MAST_TILT_MSG</source>
        <translation>¡Reducción de la inclinación del mástil debido a la posición actual del mástil!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="360"/>
        <source>FORBID_MOVING_ROD_MSG</source>
        <translation>Esta prohibido mover la barra debido a la posicion actual del Rod Support, revisar y mover en teleremoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="361"/>
        <source>FORBID_MOVING_JACKS_MSG</source>
        <translation>Prohibido mover gatas debido a la posicion actual de la barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="331"/>
        <source>RC_STRING_TOO_HIGH</source>
        <translation>Posicion de la barra muy alta, bajarla con teleremoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="339"/>
        <source>LASER_FAILURE</source>
        <translation>Falla de láser detectada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="362"/>
        <source>FORBID_MOVING_TRACKS_MSG</source>
        <translation>Está prohibido mover las gatas debido a la posición actual de los gatos!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="364"/>
        <source>SENSOR_TIMEOUT</source>
        <translation type="unfinished">Tiempo de espera del sensor excedido. Por favor, revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="365"/>
        <source>SENSOR_OOR</source>
        <translation type="unfinished">Sensor fuera de rango. Verifique las conexiones del sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="366"/>
        <source>SENSOR_STALL</source>
        <translation type="unfinished">Atasco del sensor detectado. Verifique el funcionamiento del sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="367"/>
        <source>ROLL_CRITICAL</source>
        <translation type="unfinished">Valor crítico de balanceo alcanzado. Se requiere atención inmediata.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="368"/>
        <source>PITCH_CRITICAL</source>
        <translation type="unfinished">Valor crítico de cabeceo alcanzado. Se requiere atención inmediata.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="369"/>
        <source>WRENCH_SENSOR1_FAILURE</source>
        <translation type="unfinished">Fallo del sensor de llave 1. Por favor, revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="370"/>
        <source>WRENCH_SENSOR2_FAILURE</source>
        <translation type="unfinished">Fallo del sensor de llave 2. Por favor, revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="371"/>
        <source>WRENCH_SENSOR3_FAILURE</source>
        <translation type="unfinished">Fallo del sensor de llave 3. Por favor, revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="372"/>
        <source>RPM_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor de RPM. Inspeccione el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="373"/>
        <source>FORK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal de la llave-U. Verifique el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="374"/>
        <source>LJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal del gato izquierdo. Revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="375"/>
        <source>RJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal del gato derecho. Revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="377"/>
        <source>RRJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal del gato trasero derecho. Revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="378"/>
        <source>ROT_PRESS_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor de presión de rotación. Verifique el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="379"/>
        <source>FEED_PRESS_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor de presión de alimentación. Inspeccione el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="380"/>
        <source>AIR_PRESS_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor de presión de aire. Verifique el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="381"/>
        <source>CAROUSEL_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal del carrusel. Revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="382"/>
        <source>ARM_LINEAR_SENSOR1_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal del brazo 1. Por favor, revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="383"/>
        <source>ARM_LINEAR_SENSOR2_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal del brazo 2. Por favor, revise el sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="376"/>
        <source>RLJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Fallo del sensor lineal del gato trasero izquierdo. Revise el sensor.</translation>
    </message>
</context>
<context>
    <name>MovingDataTranslator</name>
    <message>
        <location filename="../ext_translations.tri" line="300"/>
        <source>Spindle depth: </source>
        <translation>Porfundidad de taladro: </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="301"/>
        <source>m</source>
        <translation>m</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="302"/>
        <source>Drill feed speed: </source>
        <translation>Vel de penetracion: </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="303"/>
        <source>m/h</source>
        <translation>m/h</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="403"/>
        <source>Engine</source>
        <translation>RPM motor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="404"/>
        <source>Battery</source>
        <translation>Bateria</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="405"/>
        <source>Coolant temp</source>
        <translation>Temp motor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="406"/>
        <source>Water level</source>
        <translation>Nivel de agua</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="407"/>
        <source>Fuel level</source>
        <translation>Nivel de combustible</translation>
    </message>
</context>
<context>
    <name>PhotoButton</name>
    <message>
        <location filename="../ext_translations.tri" line="50"/>
        <source>Save
images</source>
        <translation>Guardar
imagenes</translation>
    </message>
</context>
<context>
    <name>PullupButton</name>
    <message>
        <location filename="../ext_translations.tri" line="49"/>
        <source>Force
pullup</source>
        <translation>Limp.
Taladro</translation>
    </message>
</context>
<context>
    <name>RebootButton</name>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="43"/>
        <source>Robot
Reset</source>
        <translation type="unfinished">Reiniciar el software</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="68"/>
        <source>Dangerous Feature!</source>
        <translation type="unfinished">Caracteristica peligrosa!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="68"/>
        <source>
                                       Dangerous Feature will be activated.
                                       Vehicle damage is possible.

                                       MAKE SURE THE ROD IS NOT IN THE BOREHOLE
                                       and that it sage to retract the jacks.

                                       Enter Password:
                                       </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RecalibAirButton</name>
    <message>
        <location filename="../ext_translations.tri" line="47"/>
        <source>Recalibrate
air pressure</source>
        <translation>Recalibrar
presión de aire</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="48"/>
        <source>Current air pressure will be set as nominal</source>
        <translation>La presión de aire actual se establecerá en nominal</translation>
    </message>
</context>
<context>
    <name>ResetShaftCounterButton</name>
    <message>
        <location filename="../gui_modules/buttons/reset_shaft_counter_button.py" line="12"/>
        <source>Reset
Rods</source>
        <translation type="unfinished">Reiniciar
barras</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="429"/>
        <source>WARNING: Resetting the rod counter while the rod is in the ground can cause severe damage to the rod and the drilling rig!</source>
        <translation>¡ADVERTENCIA: Reiniciar el contador de barras mientras la barra está en el suelo puede causar daños graves a la barra y al equipo de perforación!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="430"/>
        <source>Are you sure you want to reset the rod counter?</source>
        <translation>¿Está seguro de que desea reiniciar el contador de barras?</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="431"/>
        <source>Reset Rod Counter</source>
        <translation>Reiniciar contador de barras</translation>
    </message>
</context>
<context>
    <name>RestoreRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="53"/>
        <source>Restore
Control
Restrictions</source>
        <translation>Restablecer
restricciones</translation>
    </message>
</context>
<context>
    <name>SendHoleButton</name>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="48"/>
        <source>Drill
here</source>
        <translation type="unfinished">Perforar
aquí</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="70"/>
        <source>Depth</source>
        <translation type="unfinished">Profundidad</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="80"/>
        <source>Tilt</source>
        <translation type="unfinished">Inclinanción</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="89"/>
        <source>Hole ID</source>
        <translation type="unfinished">ID taladro</translation>
    </message>
</context>
<context>
    <name>SmartButton</name>
    <message>
        <location filename="../ext_translations.tri" line="309"/>
        <source>test Button Core</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="83"/>
        <source>Water</source>
        <translation>Agua</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="84"/>
        <source>Lights</source>
        <translation>Luces</translation>
    </message>
</context>
<context>
    <name>SmartButtonGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="63"/>
        <source>Cameras mode</source>
        <translation>Cámaras</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="79"/>
        <source>Operation mode</source>
        <translation>Modo de operación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="69"/>
        <source>Drill mode</source>
        <translation>Tipo de roca</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="70"/>
        <source>Wet mode</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>StaticLabel</name>
    <message>
        <location filename="../ext_translations.tri" line="166"/>
        <source>Position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="167"/>
        <source>Index</source>
        <translation>Indice</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="174"/>
        <source>Dust flaps</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="169"/>
        <source>Grip</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="170"/>
        <source>Vertical</source>
        <translation>Vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="171"/>
        <source>Slanted</source>
        <translation>Inclinado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="172"/>
        <source>U-Fork</source>
        <translation>Llave U</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="173"/>
        <source>Rod support</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Switch2MovingDialog</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="676"/>
        <source>Switch to Autonomous Moving</source>
        <translation>Pasar al movimiento autónomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="692"/>
        <source>&lt;b&gt;Drill with vehicle id {} could move after switch to autonomous mode.&lt;/b&gt;&lt;br&gt;</source>
        <translation>&amp;lt;b&amp;gt;Taladro con id de vehículo {} podría moverse después de cambiar a modo autónomo.&amp;lt;/b&amp;gt;&amp;lt;br&amp;gt;</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="693"/>
        <source>Drill could move after switch to autonomous mode, because planner is in Moving, Approach or Computing mode. &lt;br&gt;&lt;br&gt;If this is what you expect, check the goal point and just press &lt;b&gt;Continue&lt;/b&gt; button. &lt;br&gt;&lt;br&gt;If you finished moving manually and want robot to continue with drilling, press &lt;b&gt;Finish moving and continue autonomous&lt;/b&gt;.</source>
        <translation>El taladro podría moverse después de cambiar al modo autónomo, porque el planificador está en modo Movimiento, Aproximación o Cálculo. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt; Si esto es lo que espera, compruebe el punto de destino y pulse el botón &amp;lt;b&amp;gt;Continuar&amp;lt;/b&amp;gt;. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;Si ha finalizado el movimiento manual y desea que el robot continúe con la perforación, pulse &amp;lt;b&amp;gt;Finalizar movimiento y continúe en modo autónomo&amp;lt;/b&amp;gt;.</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="699"/>
        <source>Continue</source>
        <translation>Continuar</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="700"/>
        <source>Finish moving and continue autonomous</source>
        <translation>Finalizar movimiento y continúe en modo autónomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="701"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
</context>
<context>
    <name>Task</name>
    <message>
        <location filename="../ext_translations.tri" line="385"/>
        <source>drilling</source>
        <translation>Perforando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="386"/>
        <source>idle</source>
        <translation>Inactivo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="388"/>
        <source>moving</source>
        <translation>Conduciendo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="389"/>
        <source>leveling</source>
        <translation>Nivelando</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="390"/>
        <source>tower_tilt</source>
        <translation>Inclinación de torre</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="391"/>
        <source>shaft_buildup</source>
        <translation>Agregar barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="392"/>
        <source>shaft_stow</source>
        <translation>Sustraer barra</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="393"/>
        <source>grounding</source>
        <translation>Recogiendo gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="394"/>
        <source>remote_wait</source>
        <translation>Esperando control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="396"/>
        <source>remote_prepare</source>
        <translation>Preparando para control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="397"/>
        <source>end_remote</source>
        <translation>Terminando control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="398"/>
        <source>wait_after_level</source>
        <translation>Esperando despues de nivelación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="399"/>
        <source>restore_string</source>
        <translation>Columna de perforación recogida</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="400"/>
        <source>wait_before_level</source>
        <translation>Esperando antes de nivelación</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="387"/>
        <source>failure</source>
        <translation>Falla</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="395"/>
        <source>remote</source>
        <translation>Control remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="401"/>
        <source>init_check</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TextComplicatedWidget</name>
    <message>
        <location filename="../gui_modules/text/text_complicated_widget.py" line="95"/>
        <source> no version</source>
        <translation type="unfinished"> sin versión</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/text_complicated_widget.py" line="97"/>
        <source> HAL unreachable</source>
        <translation type="unfinished"> HAL sin conexión</translation>
    </message>
</context>
<context>
    <name>TextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="1"/>
        <source>Select Vehicle</source>
        <translation>Seleccionar vehículo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="2"/>
        <source>Connection Mode</source>
        <translation>Modo de conexión</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="32"/>
        <source>Remote Control Mode</source>
        <translation>Modo remoto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="142"/>
        <source>Vertical</source>
        <translation>Vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="143"/>
        <source>Slanted</source>
        <translation>Inclinado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="141"/>
        <source>Pins</source>
        <translation>Pines</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="146"/>
        <source>Opened</source>
        <translation>Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="147"/>
        <source>Closed</source>
        <translation>Cerrado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="144"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="149"/>
        <source>Extended</source>
        <translation>Extendido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="161"/>
        <source>Removed</source>
        <translation>Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="148"/>
        <source>Carousel</source>
        <translation>Carrusel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="162"/>
        <source>Jacks</source>
        <translation>Gatas</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="163"/>
        <source>Tower is in position</source>
        <translation>Torre alineada</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="150"/>
        <source>Retracted</source>
        <translation>Retraido</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="151"/>
        <source>Index 1</source>
        <translation>Indice 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="152"/>
        <source>Index 2</source>
        <translation>Indice 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="153"/>
        <source>Rod in cup 1</source>
        <translation>Barra en copa 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="154"/>
        <source>Rod in cup 2</source>
        <translation>Barra en copa 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="155"/>
        <source>Wrench</source>
        <translation>Caiman</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="157"/>
        <source>Stowed</source>
        <translation>Abierto</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="158"/>
        <source>Engaged</source>
        <translation>Cerrado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="159"/>
        <source>Grip open</source>
        <translation>Enganchado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="160"/>
        <source>Grip closed</source>
        <translation>Desenganchado</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="164"/>
        <source>Work permission</source>
        <translation>Perm. de trabajo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="145"/>
        <source>Dust flaps</source>
        <translation>Cor. de polvo</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="156"/>
        <source>U-Fork</source>
        <translation>Llave U</translation>
    </message>
</context>
<context>
    <name>TumblerWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="869"/>
        <source>Tumbler On</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="870"/>
        <source>Tumbler in the Middle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="871"/>
        <source>Tumbler Off</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>VehicleCard</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="92"/>
        <source>Task: No info</source>
        <translation>Tarea:sin información</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="94"/>
        <source>Offline</source>
        <translation>Desconectado</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="96"/>
        <source>No info</source>
        <translation>Sin información</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="108"/>
        <source>Watch</source>
        <comment>like watch what this vehicle is doing</comment>
        <translation>Autonomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="113"/>
        <source>Control</source>
        <comment>like control this vehicle remotely</comment>
        <translation>Control remoto</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="117"/>
        <source>Disconnect</source>
        <comment>there means not watch and not control vehicle</comment>
        <translation>Desconectar</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="120"/>
        <source>Move Permission</source>
        <comment>does vehicle has permission to move?</comment>
        <translation>Permiso de Trabajo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="123"/>
        <source>RoboMode</source>
        <comment>Checkbox, if checked -&gt; vehicle is robot, else manual</comment>
        <translation>Modo autonomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="265"/>
        <source>Robot</source>
        <translation>Autónomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="268"/>
        <source>Manual</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Mecanico</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="282"/>
        <source>Ok</source>
        <translation>ok</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="285"/>
        <source>Need Permission</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Requiere permiso</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="385"/>
        <source>No task</source>
        <comment>Label in vehicle selector when no task given</comment>
        <translation>Sin tareas</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="391"/>
        <source>Online</source>
        <comment>LED indicator when vehicle is online</comment>
        <translation>Conectado</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="395"/>
        <source>Robot</source>
        <comment>LED when vehicle is in Robo-mode</comment>
        <translation>Autónomo</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="398"/>
        <source>Manual</source>
        <comment>LED when vehicle is not in Robo-mode</comment>
        <translation>Mecánico</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="402"/>
        <source>Offline</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Desconectado</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="406"/>
        <source>No Data</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Sin datos</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="115"/>
        <source>Emergency</source>
        <comment>reset emergency</comment>
        <translation>Emergencia</translation>
    </message>
</context>
<context>
    <name>VehicleSelectorWrapper</name>
    <message>
        <location filename="../gui_modules/controls/vehicle_selector_wrapper.py" line="94"/>
        <source>No data</source>
        <translation type="unfinished">Sin datos</translation>
    </message>
</context>
<context>
    <name>veh_selector</name>
    <message>
        <location filename="../veh_selector.py" line="32"/>
        <source>Watch</source>
        <translation>Ver equipo</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="33"/>
        <source>Control</source>
        <translation>Control remoto</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="34"/>
        <source>Disconnect</source>
        <translation>Desconectar</translation>
    </message>
</context>
</TS>
