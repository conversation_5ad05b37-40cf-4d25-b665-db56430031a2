<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS><TS version="2.0" language="en_US" sourcelanguage="en">
<context>
    <name>AngleIndicatorWidget</name>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="195"/>
        <source>Across: </source>
        <translation type="unfinished">Roll:</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="196"/>
        <source>Along: </source>
        <translation type="unfinished">Pitch:</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="202"/>
        <source>Tilt</source>
        <translation type="unfinished">Tilt</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="223"/>
        <source>Along</source>
        <translation type="unfinished">Along</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="236"/>
        <source>Across</source>
        <translation type="unfinished">Roll</translation>
    </message>
</context>
<context>
    <name>AuthButton</name>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="47"/>
        <source>Logout</source>
        <translation type="unfinished">Logout</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="119"/>
        <source>login</source>
        <translation type="unfinished">Login</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="46"/>
        <source>Login</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="120"/>
        <source>password</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AuthWidget</name>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="258"/>
        <source>No connection with server...</source>
        <translation type="unfinished">No connection with server...</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="216"/>
        <source>Can&apos;t log in</source>
        <translation type="unfinished">Can&apos;t log in</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="207"/>
        <source>Password field should not be empty</source>
        <translation type="unfinished">Password field should not be empty</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="208"/>
        <source>Empty Password Field</source>
        <translation type="unfinished">Empty Password Field</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="188"/>
        <source>Can&apos;t Authorize user
error: </source>
        <translation type="unfinished">Can&apos;t Authorize user
error: </translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="192"/>
        <source>Login Error</source>
        <translation type="unfinished">Login error</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="200"/>
        <source>Login field should not be empty</source>
        <translation type="unfinished">Login field should not be empty</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="201"/>
        <source>Empty Login Field</source>
        <translation type="unfinished">Empty Login Field</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="250"/>
        <source>Can&apos;t logout user
error: </source>
        <translation type="unfinished">Can&apos;t logout user
error: </translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="254"/>
        <source>Logout Error</source>
        <translation type="unfinished">Logout error</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="259"/>
        <source>Can&apos;t log out</source>
        <translation type="unfinished">Can&apos;t log out</translation>
    </message>
</context>
<context>
    <name>ButtonInGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="64"/>
        <source>Front</source>
        <translation>Front</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="65"/>
        <source>Rear</source>
        <translation>Rear</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="66"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="67"/>
        <source>Birdview</source>
        <translation>Birdview</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="80"/>
        <source>Drill</source>
        <translation>Drill</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="81"/>
        <source>Propel</source>
        <translation>Propel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="72"/>
        <source>Normal</source>
        <translation>Normal rocks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="73"/>
        <source>Cracked</source>
        <translation>Cracked rocks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="74"/>
        <source>Hard</source>
        <translation>Hard rock</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="75"/>
        <source>Flooded</source>
        <translation>Flooded</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="76"/>
        <source>Dry</source>
        <translation>Dry</translation>
    </message>
</context>
<context>
    <name>CategoryContainer</name>
    <message>
        <location filename="../ext_translations.tri" line="192"/>
        <source>U-Fork</source>
        <translation>U-Fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="193"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="194"/>
        <source>Pins</source>
        <translation>Pins</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="195"/>
        <source>Arm</source>
        <translation>Rod supp.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="196"/>
        <source>Wrench</source>
        <translation>Wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="197"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
</context>
<context>
    <name>ConditionalTextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="304"/>
        <source>Maneuver in process</source>
        <translation>Maneuver in process</translation>
    </message>
</context>
<context>
    <name>ControllerPanel</name>
    <message>
        <location filename="../ext_translations.tri" line="3"/>
        <source>Driving</source>
        <translation>Driving</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="6"/>
        <source>           pull extend
     Jacks</source>
        <translation>           pull extend
     Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="7"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="8"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="24"/>
        <source>Feed</source>
        <translation>Pull Down</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="10"/>
        <source>Rotating</source>
        <translation>Rotating</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="26"/>
        <source>Force</source>
        <translation>Force</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="12"/>
        <source>Compressor</source>
        <translation>Compressor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="13"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="14"/>
        <source>Rear</source>
        <translation>Rear</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="17"/>
        <source>Tower Tilt</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="18"/>
        <source>Tilt</source>
        <translation>Tilt</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="19"/>
        <source>Upper Pin</source>
        <translation>Upper Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="20"/>
        <source>Lower Pin</source>
        <translation>Lower Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="21"/>
        <source>  Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="22"/>
        <source>Extention</source>
        <translation>Extention</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="23"/>
        <source>Manipulator</source>
        <translation>Manipulator</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="25"/>
        <source>Turning</source>
        <translation>Turning</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="27"/>
        <source>clamp  offset</source>
        <translation>clamp  offset</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="28"/>
        <source>   Fork</source>
        <translation>Fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="29"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="30"/>
        <source>Supply</source>
        <translation>Supply</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="31"/>
        <source>Angle</source>
        <translation>Angle</translation>
    </message>
</context>
<context>
    <name>CustomSlider</name>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="357"/>
        <source>Apply</source>
        <comment>send data from this widget to system</comment>
        <translation>Apply</translation>
    </message>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="360"/>
        <source>Save</source>
        <comment>Button caption that push value into settings file</comment>
        <translation>Save</translation>
    </message>
</context>
<context>
    <name>CustomSliderWrapper</name>
    <message>
        <location filename="../ext_translations.tri" line="58"/>
        <source>Feed pressure</source>
        <translation>Pull Down Pressure</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="59"/>
        <source>bar</source>
        <translation>Bar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="60"/>
        <source>Rotation speed</source>
        <translation>Rotation speed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="61"/>
        <source>rpm</source>
        <translation>RPM</translation>
    </message>
</context>
<context>
    <name>DangerousFeature</name>
    <message>
        <location filename="../ext_translations.tri" line="55"/>
        <source>Dangerous feature!</source>
        <translation>Dangerous feature!</translation>
    </message>
</context>
<context>
    <name>DangerousFeatureDescription</name>
    <message>
        <location filename="../ext_translations.tri" line="56"/>
        <source>Description!</source>
        <translation>This feature eliminates resctrictions of control of drilling machine </translation>
    </message>
</context>
<context>
    <name>DialIndicator</name>
    <message>
        <location filename="../ext_translations.tri" line="33"/>
        <source>Revolutions
(revs/min) x10</source>
        <translation>RPM
(revs/min) x10</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="34"/>
        <source>Air
pressure (bar)</source>
        <translation>Air pressure
(bar)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="38"/>
        <source>Drill Feed
pressure (psi)</source>
        <translation>Pull down pressure
(psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="36"/>
        <source>Rotation
pressure (psi)</source>
        <translation>Rotation
pressure (psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="35"/>
        <source>Air
pressure (psi)</source>
        <translation>Air
pressure (psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="37"/>
        <source>Rotation torque
(klb*ft)</source>
        <translation>Rotation torque
(klb*ft)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="39"/>
        <source>Drill Feed
pressure (klbf)</source>
        <translation>Drill Feed
pressure (klbf)</translation>
    </message>
</context>
<context>
    <name>Distance2HoleWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="408"/>
        <source>Hole distance </source>
        <translation>Hole distance</translation>
    </message>
</context>
<context>
    <name>DowntimesButton</name>
    <message>
        <location filename="../downtimes.py" line="552"/>
        <source>Has active downtime!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="565"/>
        <source>Has downtime to finish!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="573"/>
        <source>Create Downtime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="283"/>
        <source>Authorization Needed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="283"/>
        <source>You need to log in to perform this action.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="319"/>
        <source>None</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="341"/>
        <source>Vehicle Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="341"/>
        <source>No valid vehicle to save downtime for.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="353"/>
        <source>Switched to New Downtime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="353"/>
        <source>A new active downtime was detected.
Your entered comment and type are now associated with the new downtime.
Please press &apos;Save downtime&apos; again to finalize these changes.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="385"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="381"/>
        <source>Failed to save downtime. Please try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="430"/>
        <source>Downtime Changed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="409"/>
        <source>A new active downtime was created automatically while you were editing.

Do you want to edit the newly created downtime instead?

If you choose &apos;Yes&apos;, your currently entered comment and downtime type will now be applied to the new downtime. The form will remain open with your input unchanged.
Press &apos;Save downtime&apos; again to finalize.

If you choose &apos;No&apos;, we will attempt to create a new downtime anyway.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="430"/>
        <source>The downtime you were editing has changed on the server.

Do you want to reload the latest downtime data?

If you choose &apos;Yes&apos;, your current inputs will be replaced with the server&apos;s data.
If you choose &apos;No&apos;, we will attempt to save your current changes anyway.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DowntimesFormDialog</name>
    <message>
        <location filename="../downtimes.py" line="24"/>
        <source>Downtime Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="53"/>
        <source>End time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="34"/>
        <source>I want to finish downtime record</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="44"/>
        <source>Apply auto end time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="45"/>
        <source>Set Current Time</source>
        <translation type="unfinished">Set current time</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="133"/>
        <source>Save downtime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="55"/>
        <source>Auto end time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="63"/>
        <source>Downtime ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="64"/>
        <source>Vehicle ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="65"/>
        <source>Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="66"/>
        <source>Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="67"/>
        <source>Start time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="195"/>
        <source>Not available</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="99"/>
        <source>End time must be later than start time and before the current time.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="115"/>
        <source>Close downtime record</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="137"/>
        <source>Not set</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DowntimesTypes</name>
    <message>
        <location filename="../ext_translations.tri" line="410"/>
        <source>drill_maintenance</source>
        <translation>Drill maintenance</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="411"/>
        <source>drill_maintenance_description</source>
        <translation>Drilling machine maintenance</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="412"/>
        <source>no_operator</source>
        <translation>No operator</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="413"/>
        <source>no_operator_description</source>
        <translation>Operator is not assigned to the machine</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="414"/>
        <source>no_task</source>
        <translation>No task</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="415"/>
        <source>no_task_description</source>
        <translation>No task assigned to the machine</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="416"/>
        <source>robot_malfunction</source>
        <translation>Robot malfunction</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="417"/>
        <source>robot_malfunction_description</source>
        <translation>The robot is not functioning. Possible reasons: sensors, wiring, mechanical failure, software</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="418"/>
        <source>software_update</source>
        <translation>Software update</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="419"/>
        <source>software_update_description</source>
        <translation>The software on the machine is being updated</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="420"/>
        <source>area_not_ready</source>
        <translation>Area is not ready</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="421"/>
        <source>area_not_ready_description</source>
        <translation>Area is not ready for work</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="422"/>
        <source>refueling</source>
        <translation>Refueling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="423"/>
        <source>refueling_description</source>
        <translation>Waiting for materials to be refilled</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="424"/>
        <source>robot_maintenance</source>
        <translation>Robot maintenance</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="425"/>
        <source>robot_maintenance_description</source>
        <translation>Robot maintenance (calibration, setup)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="426"/>
        <source>manual_operation</source>
        <translation>Manual mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="427"/>
        <source>manual_operation_description</source>
        <translation>The machine operates in manual mode (under the control of an operator on board)</translation>
    </message>
</context>
<context>
    <name>DrillJoystickWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="86"/>
        <source>Driving</source>
        <translation>Driving</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="87"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="88"/>
        <source>Retract</source>
        <translation>Retract</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="89"/>
        <source>Extend</source>
        <translation>extend</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="90"/>
        <source>Right track</source>
        <translation>right track</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="91"/>
        <source>Left track</source>
        <translation>left track</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="92"/>
        <source>Back</source>
        <translation>Back</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="93"/>
        <source>Forth</source>
        <translation>Forward</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="95"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="96"/>
        <source>Feed force</source>
        <translation>Feed force</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="97"/>
        <source>Rotation</source>
        <translation>Rotation</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="100"/>
        <source>Arm</source>
        <translation>Rod Support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="101"/>
        <source>Open</source>
        <translation>Open</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="102"/>
        <source>Close</source>
        <translation>Close</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="133"/>
        <source>Feed</source>
        <translation>Feed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="134"/>
        <source>Up</source>
        <translation>Up</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="135"/>
        <source>Down</source>
        <translation>Down</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="136"/>
        <source>Compressor</source>
        <translation>Compressor</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="137"/>
        <source>On</source>
        <translation>On</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="138"/>
        <source>Off</source>
        <translation>Off</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="110"/>
        <source>Leveling</source>
        <translation>Leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="111"/>
        <source>All jacks</source>
        <translation>All Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="112"/>
        <source>Rear</source>
        <translation>Rear</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="114"/>
        <source>Tower</source>
        <translation>Tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="115"/>
        <source>Inclined Pin</source>
        <translation>Inclined Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="116"/>
        <source>Tilt</source>
        <translation>Tilt</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="119"/>
        <source>Vertical Pin</source>
        <translation>Vertical Pin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="121"/>
        <source>Wrench</source>
        <translation>Wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="122"/>
        <source>Release</source>
        <translation>Release</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="123"/>
        <source>Turn</source>
        <translation>Turn</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="124"/>
        <source>Stow</source>
        <translation>Stow</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="125"/>
        <source>Swing in</source>
        <translation>Swing in</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="126"/>
        <source>Fork</source>
        <translation>Fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="127"/>
        <source>Stop</source>
        <translation>Stop</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="129"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="130"/>
        <source>Index</source>
        <translation>Index</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="98"/>
        <source>CCW</source>
        <translation>CCW</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="99"/>
        <source>CW</source>
        <translation>CW</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="117"/>
        <source>Raise</source>
        <translation>Raise</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="118"/>
        <source>Lower</source>
        <translation>Lower</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="56"/>
        <source>Red button</source>
        <translation>Red button</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="59"/>
        <source>Green button</source>
        <translation>Green button</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="139"/>
        <source>Red and Green Buttons</source>
        <translation>Red and Green Buttons</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="142"/>
        <source>Knob widget</source>
        <translation>Knob widget</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="145"/>
        <source>Left Joystick</source>
        <translation>Left Joystick</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="148"/>
        <source>Center Joystick</source>
        <translation>Center Joystick</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="151"/>
        <source>Right Joystick</source>
        <translation>Right Joystick</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="154"/>
        <source>Tumbler Widget</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="15"/>
        <source>Left</source>
        <translation>Left</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="16"/>
        <source>Right</source>
        <translation>Right</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="131"/>
        <source>Dust flaps</source>
        <translation>Dust flaps</translation>
    </message>
</context>
<context>
    <name>DropActionButton</name>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="16"/>
        <source>Confirm current task cancelling</source>
        <translation type="unfinished">Confirm current task cancelling</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="25"/>
        <source>Permitted to qualified Engineers or Developers only</source>
        <translation type="unfinished">Permitted to qualified Engineers or Developers only</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="27"/>
        <source>Wrong password</source>
        <translation type="unfinished">Wrong password</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="84"/>
        <source>Unusual Action</source>
        <translation type="unfinished">Unusual Action</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="84"/>
        <source>
                                            Drilling in the process, can&apos;t cancel task right now.
                                            Enter password if still want to proceed
                                            </source>
        <translation type="unfinished">
                                            Drilling in the process, can&apos;t cancel task right now.
                                            Enter password if still want to proceed
                                            </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="41"/>
        <source>Cancel
task</source>
        <translation>Cancel task</translation>
    </message>
</context>
<context>
    <name>DropDrillError</name>
    <message>
        <location filename="../ext_translations.tri" line="42"/>
        <source>Clear
error</source>
        <translation>Clear error</translation>
    </message>
</context>
<context>
    <name>DropTailingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="43"/>
        <source>Clear
tailing</source>
        <translation>Clear 
boundaries</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="44"/>
        <source>Detected tailing will be cleared</source>
        <translation>Detected tailing will be cleared</translation>
    </message>
</context>
<context>
    <name>DynamicActionButton</name>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="66"/>
        <source>Finish
Action</source>
        <translation type="unfinished">Finish
Action</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="48"/>
        <source>Finish
Drilling</source>
        <translation type="unfinished">Finish
Drilling</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="53"/>
        <source>Finish
Stow</source>
        <translation type="unfinished">Finish
Stow</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="58"/>
        <source>Finish
Buildup</source>
        <translation type="unfinished">Finish
Buildup</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="63"/>
        <source>Finish
Moving</source>
        <translation type="unfinished">Finish
Moving</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="16"/>
        <source>WARNING: Before finishing rod buildup, make sure you have completed the process manually!
Incorrect completion may damage the drilling rig.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="17"/>
        <source>Are you sure you want to finish rod buildup?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="18"/>
        <source>Finish Rod Buildup</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="24"/>
        <source>WARNING: Before finishing rod stow, make sure you have completed the process manually!
Incorrect completion may damage the drilling rig.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="25"/>
        <source>Are you sure you want to finish rod stow?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="26"/>
        <source>Finish Rod Stow</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DynamicLabel</name>
    <message>
        <location filename="../ext_translations.tri" line="176"/>
        <source>Opened</source>
        <translation type="unfinished">Opened</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="177"/>
        <source>Closed</source>
        <translation type="unfinished">Closed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="178"/>
        <source>Extended</source>
        <translation type="unfinished">Extended</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="179"/>
        <source>Retracted</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="180"/>
        <source>Index 1</source>
        <translation type="unfinished">Index 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="181"/>
        <source>Index 2</source>
        <translation type="unfinished">Index 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="182"/>
        <source>Stowed</source>
        <translation type="unfinished">Stowed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="183"/>
        <source>Engaged</source>
        <translation type="unfinished">Engaged</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="186"/>
        <source>Removed</source>
        <translation type="unfinished">Removed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="188"/>
        <source>No data</source>
        <translation type="unfinished">No data</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="184"/>
        <source>Gripped</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="185"/>
        <source>Released</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="189"/>
        <source>Locked</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="187"/>
        <source>Moving</source>
        <translation type="unfinished">Moving</translation>
    </message>
</context>
<context>
    <name>EliminateFirstLevelRestrictionsButton</name>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="123"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="123"/>
        <source>Wrong password</source>
        <translation type="unfinished">Wrong password</translation>
    </message>
</context>
<context>
    <name>EliminateFullRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="52"/>
        <source>All
Restrictions
off</source>
        <translation>All
restrictions
off</translation>
    </message>
</context>
<context>
    <name>EliminateFullRestrictionsButton</name>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="192"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="192"/>
        <source>Wrong password</source>
        <translation type="unfinished">Wrong password</translation>
    </message>
</context>
<context>
    <name>EliminateRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="51"/>
        <source>Eliminate
Restrictions</source>
        <translation>Eliminate
Restrictions</translation>
    </message>
</context>
<context>
    <name>EmergencyDialog</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="757"/>
        <source>Emergency</source>
        <translation>Emergency stop</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="772"/>
        <source>&lt;b&gt;Emergency was set for the {} vehicle.&lt;/b&gt;&lt;br&gt;&lt;br&gt;</source>
        <translation>&amp;lt;b&amp;gt;Emergency was set for the {} vehicle.&amp;lt;/b&amp;gt;&amp;lt;br&amp;gt;&amp;lt;br&amp;gt;</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="773"/>
        <source>Operator can activate the emergency mode with a button on the control panel or remotely with a radio remote control button. &lt;br&gt;&lt;br&gt;Motor would be automatically stopped in this mode. &lt;br&gt;&lt;br&gt;Please identify the problem that caused the emergency and eliminate it. Verify with the cameras that the operation is safe. &lt;br&gt;&lt;br&gt;Afterwards, you can reset the emergency with the button below. &lt;br&gt;&lt;br&gt;To hide this window, press the cancel button. You can return to this window later by pressing the emergency button in the vehicle selection window.</source>
        <translation>Operator can activate the emergency mode with a button on the control panel or remotely with a radio remote control button. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;Motor would be automatically stopped in this mode. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;Please identify the problem that caused the emergency and eliminate it. Verify with the cameras that the operation is safe. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;Afterwards, you can reset the emergency with the button below. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;To hide this window, press the &amp;quot;Cancel&amp;quot; button. You can return to this window later by pressing the emergency button in the vehicle selection window.</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="784"/>
        <source>Reset emergency</source>
        <translation>Reset emergency</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="785"/>
        <source>Hide this dialog</source>
        <translation>Hide this dialog</translation>
    </message>
</context>
<context>
    <name>EmergencySetter</name>
    <message>
        <location filename="../gui_modules/controls/emergency_setter.py" line="25"/>
        <source>DENY MOVEMENT</source>
        <translation type="unfinished">DENY MOVEMENT</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/emergency_setter.py" line="32"/>
        <source>PERMIT MOVEMENT</source>
        <translation type="unfinished">PERMIT MOVEMENT</translation>
    </message>
</context>
<context>
    <name>FinishBuildupButton</name>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="25"/>
        <source>Attention!
Make sure that the new rod is extended
Fork and key is retracted</source>
        <translation type="unfinished">Attention!
Make sure that the new rod is extended
Fork and key is retracted</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="27"/>
        <source>Attention!</source>
        <translation type="unfinished">Attention!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="51"/>
        <source>End
Extending</source>
        <translation type="unfinished">End
Extending</translation>
    </message>
</context>
<context>
    <name>FinishHoleButton</name>
    <message>
        <location filename="../ext_translations.tri" line="45"/>
        <source>Finish
drilling</source>
        <translation>Finish drilling</translation>
    </message>
</context>
<context>
    <name>FinishMovingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="46"/>
        <source>Finish
moving</source>
        <translation>Finish moving</translation>
    </message>
</context>
<context>
    <name>FinishStowButton</name>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="26"/>
        <source>Attention!
Make sure the boring rod is not in the borehole,
Fork and Key are retracted away!</source>
        <translation type="unfinished">Attention!
Make sure the boring rod is not in the borehole,
Fork and Key are retracted away!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="28"/>
        <source>Attention!</source>
        <translation type="unfinished">Attention!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="52"/>
        <source>Finish
Stow</source>
        <translation type="unfinished">Finish
Stow</translation>
    </message>
</context>
<context>
    <name>HardEmergencyButton</name>
    <message>
        <location filename="../gui_modules/buttons/hard_emergency_button.py" line="82"/>
        <source>Emergency
Stop</source>
        <translation type="unfinished">Emergency
Stop</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/hard_emergency_button.py" line="91"/>
        <source>Continue
working</source>
        <translation type="unfinished">Continue
working</translation>
    </message>
</context>
<context>
    <name>InputDialog</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="230"/>
        <source> description</source>
        <translation type="unfinished">description</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="239"/>
        <source>Event description</source>
        <translation type="unfinished">Event description</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="325"/>
        <source>From</source>
        <comment>for messaging: _from_ vehicle </comment>
        <translation type="unfinished">From</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="325"/>
        <source>At</source>
        <comment>for messaging: received _at_ time</comment>
        <translation type="unfinished">On</translation>
    </message>
</context>
<context>
    <name>JoystickWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="652"/>
        <source>Top Action</source>
        <translation>Top Action</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="653"/>
        <source>Bottom Action</source>
        <translation>Bottom Action</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="654"/>
        <source>0.0%</source>
        <translation>0.0%</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="659"/>
        <source>Nonlinear</source>
        <translation>Nonlinear</translation>
    </message>
</context>
<context>
    <name>KnobViewWithCaption</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="535"/>
        <source>Nonlinear</source>
        <comment>Toggle Switch caption</comment>
        <translation>Nonlinear</translation>
    </message>
</context>
<context>
    <name>MachineStateManagement</name>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="80"/>
        <source>Unlock</source>
        <translation type="unfinished">Unlock</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="112"/>
        <source>Lock</source>
        <translation type="unfinished">Lock</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="128"/>
        <source>Commit</source>
        <translation type="unfinished">Commit</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="136"/>
        <source>Cancel</source>
        <translation type="unfinished">Cancel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="200"/>
        <source>Idle</source>
        <translation>Idle</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="202"/>
        <source>Driving</source>
        <translation>Driving</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="245"/>
        <source>Leveling</source>
        <translation>Leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="204"/>
        <source>Tower tilt</source>
        <translation>Tower tilt</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="226"/>
        <source>Drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="206"/>
        <source>Shaft Build-up</source>
        <translation>Add rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="207"/>
        <source>Shaft stow</source>
        <translation>Remove rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="208"/>
        <source>Grounding</source>
        <translation>Grounding</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="209"/>
        <source>Wait for Remote</source>
        <translation>Wait for Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="210"/>
        <source>Prepare for Remote</source>
        <translation>Prepare for Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="211"/>
        <source>In Remote</source>
        <translation>In remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="212"/>
        <source>Finishing Remote</source>
        <translation>Finishing Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="213"/>
        <source>Waiting after leveling</source>
        <translation>Waiting after leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="214"/>
        <source>String restoring</source>
        <translation>String restoring</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="215"/>
        <source>Waiting before leveling</source>
        <translation>Waiting before leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="216"/>
        <source>Failure</source>
        <translation>Failure</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="217"/>
        <source>Locking</source>
        <translation>Locking</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="218"/>
        <source>Unlocking</source>
        <translation>Unlocking</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="219"/>
        <source>Arm closing</source>
        <translation>Arm closing</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="220"/>
        <source>Tilt Regulation</source>
        <translation>Tilt Regulation</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="221"/>
        <source>Touchdown</source>
        <translation>Touchdown</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="225"/>
        <source>Overburden pass</source>
        <translation>Overburden pass</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="227"/>
        <source>Hard Rot</source>
        <translation>Hard Rot</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="228"/>
        <source>Pullup</source>
        <translation>Pullup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="229"/>
        <source>After Pullup</source>
        <translation>After Pullup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="230"/>
        <source>String Raising</source>
        <translation>String Raising</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="231"/>
        <source>Waiting after drill</source>
        <translation>Waiting after drill</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="232"/>
        <source>Pass soft</source>
        <translation>Pass soft</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="233"/>
        <source>Tower</source>
        <translation>Tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="234"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="235"/>
        <source>Opening</source>
        <translation>Opening</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="237"/>
        <source>Closing</source>
        <translation>Closing</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="238"/>
        <source>Close</source>
        <translation>Close</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="239"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="241"/>
        <source>Pulling</source>
        <translation>Pulling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="242"/>
        <source>Pulled</source>
        <translation>Pulled</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="243"/>
        <source>Restoring pulled</source>
        <translation>Retracting jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="247"/>
        <source>Holding</source>
        <translation>Holding</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="248"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="249"/>
        <source>Carousel opening</source>
        <translation>Carousel opening</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="250"/>
        <source>Carousel closing</source>
        <translation>Carousel closing</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="251"/>
        <source>Carousel turning cw</source>
        <translation>Carousel turning cw</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="252"/>
        <source>Carousel turning ccw</source>
        <translation>Carousel turning ccw</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="199"/>
        <source>Main State</source>
        <translation>Main State</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="201"/>
        <source>Init</source>
        <translation>Init</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="244"/>
        <source>Lowering jacks</source>
        <translation>Lowering jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="240"/>
        <source>Dust flaps</source>
        <translation>Dust flaps</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="236"/>
        <source>Open</source>
        <translation>Open</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="223"/>
        <source>Lifting up</source>
        <translation>Lifting up jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="246"/>
        <source>Final leveling</source>
        <translation>Final leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="224"/>
        <source>Unstucking</source>
        <translation>Unstucking</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="253"/>
        <source>Planner</source>
        <translation>Motion Planner</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="254"/>
        <source>Moving</source>
        <translation>Moving</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="255"/>
        <source>Approach</source>
        <translation>Approach</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="256"/>
        <source>Computing</source>
        <translation>Computing</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="88"/>
        <source>Dangerous feature</source>
        <translation type="unfinished">Dangerous feature</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="89"/>
        <source>You are activating a dangerous function.
If used incorrectly, there is a risk of damaging the machine.
Continue only if you know what you are doing.

Enter your password:</source>
        <translation type="unfinished">You are activating a dangerous function.
If used incorrectly, there is a risk of damaging the machine.
Continue only if you know what you are doing.

Enter your password:</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="92"/>
        <source>Ok</source>
        <translation type="unfinished">ok</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="257"/>
        <source>Initial Check</source>
        <translation>Initialization Check</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="258"/>
        <source>Unstuck Down</source>
        <translation>Unstuck Down</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="259"/>
        <source>Unstuck Spin</source>
        <translation>Unstuck Spin</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="260"/>
        <source>Unstuck Up</source>
        <translation>Unstuck Up</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="261"/>
        <source>Rod changer</source>
        <translation>Rod changer</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="262"/>
        <source>Aligning rod for cup</source>
        <translation>Aligning rod for cup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="263"/>
        <source>Aligning rod for fork</source>
        <translation>Aligning rod for fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="264"/>
        <source>Approaching rod for carousel</source>
        <translation>Approaching rod for carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="265"/>
        <source>Approaching rod for fork</source>
        <translation>Approaching rod for fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="266"/>
        <source>Apply wrench</source>
        <translation>Apply wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="267"/>
        <source>Brakeout</source>
        <translation>Brakeout</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="268"/>
        <source>Closing arm</source>
        <translation>Closing arm</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="269"/>
        <source>Closing carousel</source>
        <translation>Closing carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="270"/>
        <source>Closing wrench</source>
        <translation>Closing wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="271"/>
        <source>Detaching rod</source>
        <translation>Detaching rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="272"/>
        <source>Detaching rod in cup</source>
        <translation>Detaching rod in cup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="273"/>
        <source>Lift head to pull depth</source>
        <translation>Lift head to pull depth</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="274"/>
        <source>Finish</source>
        <translation>Finish</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="275"/>
        <source>Inserting rod in cup</source>
        <translation>Inserting rod in cup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="276"/>
        <source>Lifting head to carousel</source>
        <translation>Lifting head to carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="277"/>
        <source>Closing fork</source>
        <translation>Closing fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="278"/>
        <source>Screwing new rod</source>
        <translation>Screwing new rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="279"/>
        <source>Opening arm</source>
        <translation>Opening arm</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="280"/>
        <source>Opening carousel</source>
        <translation>Opening carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="281"/>
        <source>Opening fork and arm</source>
        <translation>Opening fork and arm</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="282"/>
        <source>Opening wrench</source>
        <translation>Opening wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="283"/>
        <source>Pull out</source>
        <translation>Pull out</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="284"/>
        <source>Screwing</source>
        <translation>Screwing</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="285"/>
        <source>Turn shaft ccw</source>
        <translation>Turn shaft ccw</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="286"/>
        <source>Turn shaft cw</source>
        <translation>Turn shaft cw</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="287"/>
        <source>Turn shaft cw in carousel cup</source>
        <translation>Turn shaft cw in carousel cup</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="288"/>
        <source>Opening fork</source>
        <translation>Opening fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="289"/>
        <source>Fork</source>
        <translation>Fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="290"/>
        <source>Missed rod</source>
        <translation>Missed rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="291"/>
        <source>Turning CW</source>
        <translation>Turning CW</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="292"/>
        <source>Turning CCW</source>
        <translation>Turning CCW</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="293"/>
        <source>Turning wrench</source>
        <translation>Turning wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="294"/>
        <source>Releasing wrench</source>
        <translation>Releasing wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="295"/>
        <source>Wrench</source>
        <translation>Wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="296"/>
        <source>Wrench is open</source>
        <translation>Wrench is open</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="297"/>
        <source>Wrench is closed</source>
        <translation>Wrench is closed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="298"/>
        <source>Init Check</source>
        <translation>Init Check</translation>
    </message>
</context>
<context>
    <name>MapWidget</name>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="80"/>
        <source>Auto Center: ON</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="88"/>
        <source>Reset View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>Auto Center: {0}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>ON</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>OFF</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="759"/>
        <source>Distance to hole: -- m</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="786"/>
        <source>Distance to hole: {0:.2f} m</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MessageLogs</name>
    <message>
        <location filename="../ext_translations.tri" line="306"/>
        <source>100000101</source>
        <translation>100000101</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="307"/>
        <source>fucking_code_123</source>
        <translation>fucking_code_123</translation>
    </message>
</context>
<context>
    <name>MessageTypeColour</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="279"/>
        <source>Red</source>
        <translation type="unfinished">Red</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="281"/>
        <source>Yellow</source>
        <translation type="unfinished">Yellow</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="283"/>
        <source>Orange</source>
        <translation type="unfinished">Orange</translation>
    </message>
</context>
<context>
    <name>MessagesWidgetMaximized</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="147"/>
        <source>Message Log</source>
        <translation type="unfinished">Message Log</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="165"/>
        <source>Debug Mode</source>
        <translation type="unfinished">Debug Mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="312"/>
        <source>ALREADY_DONE</source>
        <translation>The command was already executed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="313"/>
        <source>WAIT_FINISH</source>
        <translation>Can not accept action/command while executing the previous one</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="314"/>
        <source>LOST_HAL_CONN</source>
        <translation>No connection to server</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="315"/>
        <source>CAN_NOT_SWITCH_TO_RC</source>
        <translation>Can not switch to remote control</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="318"/>
        <source>RC_ROTATE_CAROUSEL_IDX1</source>
        <translation>Use remote control to rotate carousel clockwise</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="319"/>
        <source>RC_ROTATE_CAROUSEL_IDX2</source>
        <translation>Use remote control to rotate carousel counter-clockwise</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="320"/>
        <source>RC_CLOSE_CAROUSEL</source>
        <translation>Use remote control to close carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="321"/>
        <source>RC_OPEN_CAROUSEL</source>
        <translation>Use remote control to open carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="322"/>
        <source>RC_OPEN_ARM</source>
        <translation>Use remote control to open rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="323"/>
        <source>RC_CLOSE_ARM</source>
        <translation>Use remote control to close pipe support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="326"/>
        <source>RC_MOVE</source>
        <translation>Use remote control to drive to the next target</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="327"/>
        <source>RC_UNSTUCK</source>
        <translation>Use remote control to release rod from being stuck</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="328"/>
        <source>RC_LEVELING</source>
        <translation>Use remote control to level the platform</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="330"/>
        <source>RC_LIFT_STRING</source>
        <translation>Use remote control to lift drill string</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="332"/>
        <source>RC_RODE_CHANGE</source>
        <translation>Use remote control to add one rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="333"/>
        <source>RC_LOCK_TOWER</source>
        <translation>Use remote control to lock tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="334"/>
        <source>RC_UNLOCK_TOWER</source>
        <translation>Use remote control to unlock tower</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="335"/>
        <source>RC_TOWER_TILT</source>
        <translation>Use remote control to adjust tower angle</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="336"/>
        <source>COMPRESSOR_FAILURE</source>
        <translation>Compressor malfunctioning detected</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="338"/>
        <source>WRENCH_SWITCH_FAILURE</source>
        <translation>Wrench switch failed to work</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="340"/>
        <source>IBOX_NO_CONN</source>
        <translation>No connection to iBox</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="341"/>
        <source>IPLACE_NO_CONN</source>
        <translation>No connection to iPlace</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="348"/>
        <source>RESTORING_PULLED_JACKS</source>
        <translation>Bringing jacks back to pulled state</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="351"/>
        <source>RESTORED_STRING</source>
        <translation>String is brought back to normal position</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="352"/>
        <source>STARTING_NEW_ACT</source>
        <translation>Starting to execute a new action</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="353"/>
        <source>READY_FOR_NEW_ACT</source>
        <translation>Ready to accept a new action</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="354"/>
        <source>CAN_NOT_DRILL_CUR_HOLE</source>
        <translation>Can not drill current hole; going to the next one</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="355"/>
        <source>CAN_NOT_ACCEPT_ACTION</source>
        <translation>Can not accept a new action at the moment</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="342"/>
        <source>LEFT_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>Left jack limit switch failed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="343"/>
        <source>RIGHT_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>Right jack limit switch failed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="344"/>
        <source>REAR_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>Rear jack limit switch failed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="345"/>
        <source>CLOSING_ARM_PULLING_JACKS</source>
        <translation>New task, closing arm and pulling jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="346"/>
        <source>WRONG_ARM_STATE</source>
        <translation>Wrong arm state</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="347"/>
        <source>HEAD_TOO_LOW</source>
        <translation>Can&apos;t close rod support: head is too low</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="324"/>
        <source>RC_OPEN_DUST_FLAPS</source>
        <translation>Open dust flaps with remote control</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="325"/>
        <source>RC_CLOSE_DUST_FLAPS</source>
        <translation>Open dust flaps with remote control</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="329"/>
        <source>RC_LEVEL_TOO_LOW</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="356"/>
        <source>COLLISION_PREVENTER_STOPPAGE</source>
        <translation>Stop moving due to detected obstacle</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="216"/>
        <source>Save event?</source>
        <translation type="unfinished">Save event?</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="316"/>
        <source>DEPTH_CORRECTION_FAILED</source>
        <translation>Depth correction failed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="337"/>
        <source>LEVEL_SENSOR_FAILURE</source>
        <translation>Invalid level sensor data</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="349"/>
        <source>NO_RTK</source>
        <translation>Invalid GPS data (no RTK)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="350"/>
        <source>ANGLES_ABOVE_LIMITS</source>
        <translation>Inclination angles are out of limits</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="357"/>
        <source>NO_FRONT_LIDAR</source>
        <translation>No data from front lidar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="358"/>
        <source>NO_REAR_LIDAR</source>
        <translation>No data from back lidar</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="317"/>
        <source>UNEVEN_TERRAIN</source>
        <translation>Uneven terrain, slowing down</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="359"/>
        <source>SLOW_MAST_TILT_MSG</source>
        <translation>Slowing down mast tilt due to current mast position!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="360"/>
        <source>FORBID_MOVING_ROD_MSG</source>
        <translation>Moving rod is forbidden due to rod support current state!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="361"/>
        <source>FORBID_MOVING_JACKS_MSG</source>
        <translation>Moving jacks is forbidden due to current string position!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="331"/>
        <source>RC_STRING_TOO_HIGH</source>
        <translation>String is too high</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="339"/>
        <source>LASER_FAILURE</source>
        <translation>Laser malfunctioning detected</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="362"/>
        <source>FORBID_MOVING_TRACKS_MSG</source>
        <translation>Moving tracks is forbidden due to current jacks position!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="364"/>
        <source>SENSOR_TIMEOUT</source>
        <translation>Sensor timeout detected. Please inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="365"/>
        <source>SENSOR_OOR</source>
        <translation type="unfinished">Sensor out of range. Check sensor connections.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="366"/>
        <source>SENSOR_STALL</source>
        <translation type="unfinished">Sensor stall detected. Check sensor or controller.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="367"/>
        <source>ROLL_CRITICAL</source>
        <translation type="unfinished">Critical roll value reached. Immediate attention required.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="368"/>
        <source>PITCH_CRITICAL</source>
        <translation type="unfinished">Critical pitch value reached. Immediate attention required.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="369"/>
        <source>WRENCH_SENSOR1_FAILURE</source>
        <translation type="unfinished">Wrench Sensor 1 failure. Please check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="370"/>
        <source>WRENCH_SENSOR2_FAILURE</source>
        <translation type="unfinished">Wrench Sensor 2 failure. Please check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="371"/>
        <source>WRENCH_SENSOR3_FAILURE</source>
        <translation type="unfinished">Wrench Sensor 3 failure. Please check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="372"/>
        <source>RPM_SENSOR_FAILURE</source>
        <translation type="unfinished">RPM Sensor failure. Inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="373"/>
        <source>FORK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">U-Fork linear sensor failure. Check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="374"/>
        <source>LJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Left jack linear sensor failure. Inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="375"/>
        <source>RJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Right jack linear sensor failure. Inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="377"/>
        <source>RRJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Rear right jack linear sensor failure. Inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="378"/>
        <source>ROT_PRESS_SENSOR_FAILURE</source>
        <translation type="unfinished">Rotation pressure sensor failure. Check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="379"/>
        <source>FEED_PRESS_SENSOR_FAILURE</source>
        <translation type="unfinished">Feed pressure sensor failure. Inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="380"/>
        <source>AIR_PRESS_SENSOR_FAILURE</source>
        <translation type="unfinished">Air pressure sensor failure. Check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="381"/>
        <source>CAROUSEL_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Carousel linear sensor failure. Inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="382"/>
        <source>ARM_LINEAR_SENSOR1_FAILURE</source>
        <translation type="unfinished">Rod support linear sensor 1 failure. Check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="383"/>
        <source>ARM_LINEAR_SENSOR2_FAILURE</source>
        <translation type="unfinished">Rod support linear sensor 2 failure. Check the sensor.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="376"/>
        <source>RLJACK_LINEAR_SENSOR_FAILURE</source>
        <translation type="unfinished">Rear left jack linear sensor failure. Inspect the sensor.</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="215"/>
        <source>Save recent onboard bags for further analysis?</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MovingDataTranslator</name>
    <message>
        <location filename="../ext_translations.tri" line="300"/>
        <source>Spindle depth: </source>
        <translation>Spindle depth: </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="301"/>
        <source>m</source>
        <translation>m</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="302"/>
        <source>Drill feed speed: </source>
        <translation>Drill feed speed: </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="303"/>
        <source>m/h</source>
        <translation>m/h</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="403"/>
        <source>Engine</source>
        <translation>RPM</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="404"/>
        <source>Battery</source>
        <translation>Battery</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="405"/>
        <source>Coolant temp</source>
        <translation>Coolant temp</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="406"/>
        <source>Water level</source>
        <translation>Water level</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="407"/>
        <source>Fuel level</source>
        <translation>Fuel level</translation>
    </message>
</context>
<context>
    <name>PhotoButton</name>
    <message>
        <location filename="../ext_translations.tri" line="50"/>
        <source>Save
images</source>
        <translation>Save
images</translation>
    </message>
</context>
<context>
    <name>PullupButton</name>
    <message>
        <location filename="../ext_translations.tri" line="49"/>
        <source>Force
pullup</source>
        <translation>Force
pullup</translation>
    </message>
</context>
<context>
    <name>RebootButton</name>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="43"/>
        <source>Robot
Reset</source>
        <translation type="unfinished">Robot Reset</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="68"/>
        <source>Dangerous Feature!</source>
        <translation type="unfinished">Dangerous Feature!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="68"/>
        <source>
                                       Dangerous Feature will be activated.
                                       Vehicle damage is possible.

                                       MAKE SURE THE ROD IS NOT IN THE BOREHOLE
                                       and that it sage to retract the jacks.

                                       Enter Password:
                                       </source>
        <translation type="unfinished">
                                       Dangerous Feature will be activated.
                                       Vehicle damage is possible.

                                       MAKE SURE THE ROD IS NOT IN THE BOREHOLE
                                       and that it sage to retract the jacks.

                                       Enter Password:
                                       </translation>
    </message>
</context>
<context>
    <name>RecalibAirButton</name>
    <message>
        <location filename="../ext_translations.tri" line="47"/>
        <source>Recalibrate
air pressure</source>
        <translation>Recalibrate
air pressure</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="48"/>
        <source>Current air pressure will be set as nominal</source>
        <translation>Current air pressure will be set as nominal</translation>
    </message>
</context>
<context>
    <name>ResetShaftCounterButton</name>
    <message>
        <location filename="../gui_modules/buttons/reset_shaft_counter_button.py" line="12"/>
        <source>Reset
Rods</source>
        <translation type="unfinished">Reset
Rods</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="429"/>
        <source>WARNING: Resetting the rod counter while the rod is in the ground can cause severe damage to the rod and the drilling rig!</source>
        <translation>WARNING: Resetting the rod counter while the rod is in the ground can cause severe damage to the rod and the drilling rig!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="430"/>
        <source>Are you sure you want to reset the rod counter?</source>
        <translation>Are you sure you want to reset the rod counter?</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="431"/>
        <source>Reset Rod Counter</source>
        <translation>Reset Rod Counter</translation>
    </message>
</context>
<context>
    <name>RestoreRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="53"/>
        <source>Restore
Control
Restrictions</source>
        <translation>Restore
control
restrictions</translation>
    </message>
</context>
<context>
    <name>SendHoleButton</name>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="48"/>
        <source>Drill
here</source>
        <translation type="unfinished">Drill
here</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="70"/>
        <source>Depth</source>
        <translation type="unfinished">Depth</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="80"/>
        <source>Tilt</source>
        <translation type="unfinished">Tilt</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="89"/>
        <source>Hole ID</source>
        <translation type="unfinished">Hole ID</translation>
    </message>
</context>
<context>
    <name>SmartButton</name>
    <message>
        <location filename="../ext_translations.tri" line="309"/>
        <source>test Button Core</source>
        <translation>test Button Core</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="83"/>
        <source>Water</source>
        <translation>Water</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="84"/>
        <source>Lights</source>
        <translation>Lights</translation>
    </message>
</context>
<context>
    <name>SmartButtonGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="63"/>
        <source>Cameras mode</source>
        <translation>Cameras mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="79"/>
        <source>Operation mode</source>
        <translation>Operation mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="69"/>
        <source>Drill mode</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="70"/>
        <source>Wet mode</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>StaticLabel</name>
    <message>
        <location filename="../ext_translations.tri" line="166"/>
        <source>Position</source>
        <translation>Position</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="167"/>
        <source>Index</source>
        <translation>Index</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="174"/>
        <source>Dust flaps</source>
        <translation>Dust flaps</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="169"/>
        <source>Grip</source>
        <translation>Grip</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="170"/>
        <source>Vertical</source>
        <translation>Vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="171"/>
        <source>Slanted</source>
        <translation>Slanted</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="172"/>
        <source>U-Fork</source>
        <translation>U-Fork</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="173"/>
        <source>Rod support</source>
        <translation>Rod supp.</translation>
    </message>
</context>
<context>
    <name>Switch2MovingDialog</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="676"/>
        <source>Switch to Autonomous Moving</source>
        <translation>Switch to Autonomous Moving</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="692"/>
        <source>&lt;b&gt;Drill with vehicle id {} could move after switch to autonomous mode.&lt;/b&gt;&lt;br&gt;</source>
        <translation>&amp;lt;b&amp;gt;Drill with vehicle id {} could move after switch to autonomous mode.&amp;lt;/b&amp;gt;&amp;lt;br&amp;gt;</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="693"/>
        <source>Drill could move after switch to autonomous mode, because planner is in Moving, Approach or Computing mode. &lt;br&gt;&lt;br&gt;If this is what you expect, check the goal point and just press &lt;b&gt;Continue&lt;/b&gt; button. &lt;br&gt;&lt;br&gt;If you finished moving manually and want robot to continue with drilling, press &lt;b&gt;Finish moving and continue autonomous&lt;/b&gt;.</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="699"/>
        <source>Continue</source>
        <translation>Continue</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="700"/>
        <source>Finish moving and continue autonomous</source>
        <translation>Finish moving and continue autonomous</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="701"/>
        <source>Cancel</source>
        <translation>Cancel</translation>
    </message>
</context>
<context>
    <name>Task</name>
    <message>
        <location filename="../ext_translations.tri" line="385"/>
        <source>drilling</source>
        <translation>Drilling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="386"/>
        <source>idle</source>
        <translation>Idle</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="388"/>
        <source>moving</source>
        <translation>Driving</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="389"/>
        <source>leveling</source>
        <translation>Leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="390"/>
        <source>tower_tilt</source>
        <translation>Tower tilt</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="391"/>
        <source>shaft_buildup</source>
        <translation>Add rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="392"/>
        <source>shaft_stow</source>
        <translation>Remove rod</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="393"/>
        <source>grounding</source>
        <translation>Grounding</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="394"/>
        <source>remote_wait</source>
        <translation>Wait for Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="396"/>
        <source>remote_prepare</source>
        <translation>Prepare for Remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="397"/>
        <source>end_remote</source>
        <translation>Finishing remote</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="398"/>
        <source>wait_after_level</source>
        <translation>Waiting after leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="399"/>
        <source>restore_string</source>
        <translation>String restoring</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="400"/>
        <source>wait_before_level</source>
        <translation>Waiting before leveling</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="387"/>
        <source>failure</source>
        <translation>Failure</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="395"/>
        <source>remote</source>
        <translation>Remote control</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="401"/>
        <source>init_check</source>
        <translation>Initialization Check</translation>
    </message>
</context>
<context>
    <name>TextComplicatedWidget</name>
    <message>
        <location filename="../gui_modules/text/text_complicated_widget.py" line="95"/>
        <source> no version</source>
        <translation type="unfinished"> no version</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/text_complicated_widget.py" line="97"/>
        <source> HAL unreachable</source>
        <translation type="unfinished"> HAL unreachable</translation>
    </message>
</context>
<context>
    <name>TextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="1"/>
        <source>Select Vehicle</source>
        <translation>Select Vehicle</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="2"/>
        <source>Connection Mode</source>
        <translation>Connection Mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="32"/>
        <source>Remote Control Mode</source>
        <translation>Remote Control Mode</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="142"/>
        <source>Vertical</source>
        <translation>Vertical</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="143"/>
        <source>Slanted</source>
        <translation>Slanted</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="141"/>
        <source>Pins</source>
        <translation>Pins</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="146"/>
        <source>Opened</source>
        <translation>Opened</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="147"/>
        <source>Closed</source>
        <translation>Closed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="144"/>
        <source>Arm</source>
        <translation>Rod support</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="149"/>
        <source>Extended</source>
        <translation>Extended</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="161"/>
        <source>Removed</source>
        <translation>Removed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="148"/>
        <source>Carousel</source>
        <translation>Carousel</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="162"/>
        <source>Jacks</source>
        <translation>Jacks</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="163"/>
        <source>Tower is in position</source>
        <translation>Tower is in position</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="150"/>
        <source>Retracted</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="151"/>
        <source>Index 1</source>
        <translation>Index 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="152"/>
        <source>Index 2</source>
        <translation>Index 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="153"/>
        <source>Rod in cup 1</source>
        <translation>Rod in cup 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="154"/>
        <source>Rod in cup 2</source>
        <translation>Rod in cup 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="155"/>
        <source>Wrench</source>
        <translation>Wrench</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="157"/>
        <source>Stowed</source>
        <translation>Stowed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="158"/>
        <source>Engaged</source>
        <translation>Engaged</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="159"/>
        <source>Grip open</source>
        <translation>Grip open</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="160"/>
        <source>Grip closed</source>
        <translation>Grip closed</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="164"/>
        <source>Work permission</source>
        <translation>Work permission</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="145"/>
        <source>Dust flaps</source>
        <translation>Dust flaps</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="156"/>
        <source>U-Fork</source>
        <translation>U-Fork</translation>
    </message>
</context>
<context>
    <name>TumblerWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="869"/>
        <source>Tumbler On</source>
        <translation>Tumbler On</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="870"/>
        <source>Tumbler in the Middle</source>
        <translation>Tumbler in the Middle</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="871"/>
        <source>Tumbler Off</source>
        <translation>Tumbler Off</translation>
    </message>
</context>
<context>
    <name>VehicleCard</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="92"/>
        <source>Task: No info</source>
        <translation>Task: No info</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="94"/>
        <source>Offline</source>
        <translation>Offline</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="96"/>
        <source>No info</source>
        <translation>No info</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="108"/>
        <source>Watch</source>
        <comment>like watch what this vehicle is doing</comment>
        <translation>Watch</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="113"/>
        <source>Control</source>
        <comment>like control this vehicle remotely</comment>
        <translation>Control</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="117"/>
        <source>Disconnect</source>
        <comment>there means not watch and not control vehicle</comment>
        <translation>Disconnect</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="120"/>
        <source>Move Permission</source>
        <comment>does vehicle has permission to move?</comment>
        <translation>Move Permission</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="123"/>
        <source>RoboMode</source>
        <comment>Checkbox, if checked -&gt; vehicle is robot, else manual</comment>
        <translation>RoboMode</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="265"/>
        <source>Robot</source>
        <translation>Robot</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="268"/>
        <source>Manual</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Manual</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="282"/>
        <source>Ok</source>
        <translation>ok</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="285"/>
        <source>Need Permission</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Need Permission</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="385"/>
        <source>No task</source>
        <comment>Label in vehicle selector when no task given</comment>
        <translation>No task</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="391"/>
        <source>Online</source>
        <comment>LED indicator when vehicle is online</comment>
        <translation>Online</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="395"/>
        <source>Robot</source>
        <comment>LED when vehicle is in Robo-mode</comment>
        <translation>Robot</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="398"/>
        <source>Manual</source>
        <comment>LED when vehicle is not in Robo-mode</comment>
        <translation>Manual</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="402"/>
        <source>Offline</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Offline</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="406"/>
        <source>No Data</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>No Data</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="115"/>
        <source>Emergency</source>
        <comment>reset emergency</comment>
        <translation>Emergency</translation>
    </message>
</context>
<context>
    <name>VehicleSelectorWrapper</name>
    <message>
        <location filename="../gui_modules/controls/vehicle_selector_wrapper.py" line="94"/>
        <source>No data</source>
        <translation type="unfinished">No data</translation>
    </message>
</context>
<context>
    <name>veh_selector</name>
    <message>
        <location filename="../veh_selector.py" line="32"/>
        <source>Watch</source>
        <translation>Watch</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="33"/>
        <source>Control</source>
        <translation>Control</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="34"/>
        <source>Disconnect</source>
        <translation>Disconnect</translation>
    </message>
</context>
</TS>
