<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="ru">
<context>
    <name>AngleIndicatorWidget</name>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="195"/>
        <source>Across: </source>
        <translation type="unfinished">Поперек: </translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="196"/>
        <source>Along: </source>
        <translation type="unfinished">Вдоль: </translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="202"/>
        <source>Tilt</source>
        <translation type="unfinished">Наклон</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="223"/>
        <source>Along</source>
        <translation type="unfinished">Вдоль</translation>
    </message>
    <message>
        <location filename="../gui_modules/indicators/angle_indicator_widget.py" line="236"/>
        <source>Across</source>
        <translation type="unfinished">Поперек</translation>
    </message>
</context>
<context>
    <name>AuthButton</name>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="46"/>
        <source>Login</source>
        <translation type="unfinished">Войти</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="47"/>
        <source>Logout</source>
        <translation type="unfinished">Выйти</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="119"/>
        <source>login</source>
        <translation type="unfinished">Имя пользователя</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="120"/>
        <source>password</source>
        <translation type="unfinished">Пароль</translation>
    </message>
</context>
<context>
    <name>AuthWidget</name>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="258"/>
        <source>No connection with server...</source>
        <translation type="unfinished">Нет подключения к серверу...</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="216"/>
        <source>Can&apos;t log in</source>
        <translation type="unfinished">Не могу авторизоваться</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="207"/>
        <source>Password field should not be empty</source>
        <translation type="unfinished">Поле &amp;quot;Пароль&amp;quot; не должно быть пустым</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="208"/>
        <source>Empty Password Field</source>
        <translation type="unfinished">Поле &amp;quot;Пароль&amp;quot; пустое</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="188"/>
        <source>Can&apos;t Authorize user
error: </source>
        <translation type="unfinished">Невозможно войти
ошибка:</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="192"/>
        <source>Login Error</source>
        <translation type="unfinished">Ошибка авторизации</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="200"/>
        <source>Login field should not be empty</source>
        <translation type="unfinished">Поле &amp;quot;Имя пользователя&amp;quot; не должно быть пустым</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="201"/>
        <source>Empty Login Field</source>
        <translation type="unfinished">Поле &amp;quot;Имя пользователя&amp;quot; пустое</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="250"/>
        <source>Can&apos;t logout user
error: </source>
        <translation type="unfinished">Ошибка выхода из пользователя:</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="254"/>
        <source>Logout Error</source>
        <translation type="unfinished">Ошибка выхода из пользователя</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/auth_button.py" line="259"/>
        <source>Can&apos;t log out</source>
        <translation type="unfinished">Не могу выйти из сессии</translation>
    </message>
</context>
<context>
    <name>ButtonInGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="64"/>
        <source>Front</source>
        <translation>Передняя</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="65"/>
        <source>Rear</source>
        <translation>Задняя</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="66"/>
        <source>Drilling</source>
        <translation>Обзор бурения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="67"/>
        <source>Birdview</source>
        <translation>Панорама</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="80"/>
        <source>Drill</source>
        <translation>Бурение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="81"/>
        <source>Propel</source>
        <translation>Продвижение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="72"/>
        <source>Normal</source>
        <translation>нормальный</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="73"/>
        <source>Cracked</source>
        <translation>Трещиноватые</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="74"/>
        <source>Hard</source>
        <translation>Твердые породы</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="75"/>
        <source>Flooded</source>
        <translation type="unfinished">Обводненная</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="76"/>
        <source>Dry</source>
        <translation type="unfinished">Сухая</translation>
    </message>
</context>
<context>
    <name>CategoryContainer</name>
    <message>
        <location filename="../ext_translations.tri" line="192"/>
        <source>U-Fork</source>
        <translation>Вилка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="193"/>
        <source>Carousel</source>
        <translation>Карусель</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="194"/>
        <source>Pins</source>
        <translation>Штифты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="195"/>
        <source>Arm</source>
        <translation>Люнет</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="196"/>
        <source>Wrench</source>
        <translation>Ключ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="197"/>
        <source>Jacks</source>
        <translation>Домкраты</translation>
    </message>
</context>
<context>
    <name>ConditionalTextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="304"/>
        <source>Maneuver in process</source>
        <translation>Расчет маневра</translation>
    </message>
</context>
<context>
    <name>ControllerPanel</name>
    <message>
        <location filename="../ext_translations.tri" line="3"/>
        <source>Driving</source>
        <translation>Движение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="6"/>
        <source>           pull extend
     Jacks</source>
        <translation>           втянуть вытянуть
     домкраты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="7"/>
        <source>Drilling</source>
        <translation>Бурение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="8"/>
        <source>Arm</source>
        <translation>Люнет</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="24"/>
        <source>Feed</source>
        <translation>Подача</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="10"/>
        <source>Rotating</source>
        <translation>Вращение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="26"/>
        <source>Force</source>
        <translation>Усилие</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="12"/>
        <source>Compressor</source>
        <translation>Компрессор</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="13"/>
        <source>Jacks</source>
        <translation>Домкраты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="14"/>
        <source>Rear</source>
        <translation>Задний</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="17"/>
        <source>Tower Tilt</source>
        <translation>Наклон мачты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="18"/>
        <source>Tilt</source>
        <translation>Наклон</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="19"/>
        <source>Upper Pin</source>
        <translation>В. штифт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="20"/>
        <source>Lower Pin</source>
        <translation>Н. штифт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="21"/>
        <source>  Arm</source>
        <translation>Люнет</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="22"/>
        <source>Extention</source>
        <translation>Наращивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="23"/>
        <source>Manipulator</source>
        <translation>Манипулятор</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="25"/>
        <source>Turning</source>
        <translation>Вращение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="27"/>
        <source>clamp  offset</source>
        <translation>зажим  отвод</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="28"/>
        <source>   Fork</source>
        <translation>Вилка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="29"/>
        <source>Carousel</source>
        <translation>Карусель</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="30"/>
        <source>Supply</source>
        <translation>Подвод</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="31"/>
        <source>Angle</source>
        <translation>Поворот</translation>
    </message>
</context>
<context>
    <name>CustomSlider</name>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="357"/>
        <source>Apply</source>
        <comment>send data from this widget to system</comment>
        <translation>Применить</translation>
    </message>
    <message>
        <location filename="../customClasses/customSliderWidget.py" line="360"/>
        <source>Save</source>
        <comment>Button caption that push value into settings file</comment>
        <translation>Сохранить</translation>
    </message>
</context>
<context>
    <name>CustomSliderWrapper</name>
    <message>
        <location filename="../ext_translations.tri" line="58"/>
        <source>Feed pressure</source>
        <translation>Давление подачи</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="59"/>
        <source>bar</source>
        <translation>бар</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="60"/>
        <source>Rotation speed</source>
        <translation>Скорость вращения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="61"/>
        <source>rpm</source>
        <translation>об/мин</translation>
    </message>
</context>
<context>
    <name>DangerousFeature</name>
    <message>
        <location filename="../ext_translations.tri" line="55"/>
        <source>Dangerous feature!</source>
        <translation>Опасная функция!</translation>
    </message>
</context>
<context>
    <name>DangerousFeatureDescription</name>
    <message>
        <location filename="../ext_translations.tri" line="56"/>
        <source>Description!</source>
        <translation>Данная функция снимает ограничения по управлению буровым станком</translation>
    </message>
</context>
<context>
    <name>DialIndicator</name>
    <message>
        <location filename="../ext_translations.tri" line="33"/>
        <source>Revolutions
(revs/min) x10</source>
        <translation>Скор. вращения
(об/мин) х10</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="34"/>
        <source>Air
pressure (bar)</source>
        <translation>Давление
воздуха
(bar)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="38"/>
        <source>Drill Feed
pressure (psi)</source>
        <translation>Давление
на забой
(psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="36"/>
        <source>Rotation
pressure (psi)</source>
        <translation>Давление вращения
(psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="35"/>
        <source>Air
pressure (psi)</source>
        <translation>Давление воздуха
(psi)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="37"/>
        <source>Rotation torque
(klb*ft)</source>
        <translation>Момент вращения
(klb*ft)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="39"/>
        <source>Drill Feed
pressure (klbf)</source>
        <translation>Давление подачи
(klbf)</translation>
    </message>
</context>
<context>
    <name>Distance2HoleWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="408"/>
        <source>Hole distance </source>
        <translation>Дистанция до скважины</translation>
    </message>
</context>
<context>
    <name>DowntimesButton</name>
    <message>
        <location filename="../downtimes.py" line="552"/>
        <source>Has active downtime!</source>
        <translation>Есть активный простой!</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="565"/>
        <source>Has downtime to finish!</source>
        <translation>Есть простой для завершения!</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="573"/>
        <source>Create Downtime</source>
        <translation>Создать простой</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="283"/>
        <source>Authorization Needed</source>
        <translation>Необходима авторизация</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="283"/>
        <source>You need to log in to perform this action.</source>
        <translation>Вам необходимо войти в систему, чтобы выполнить это действие.</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="319"/>
        <source>None</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="341"/>
        <source>Vehicle Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="341"/>
        <source>No valid vehicle to save downtime for.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="353"/>
        <source>Switched to New Downtime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="353"/>
        <source>A new active downtime was detected.
Your entered comment and type are now associated with the new downtime.
Please press &apos;Save downtime&apos; again to finalize these changes.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="385"/>
        <source>Error</source>
        <translation>Ошибка</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="381"/>
        <source>Failed to save downtime. Please try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="430"/>
        <source>Downtime Changed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="409"/>
        <source>A new active downtime was created automatically while you were editing.

Do you want to edit the newly created downtime instead?

If you choose &apos;Yes&apos;, your currently entered comment and downtime type will now be applied to the new downtime. The form will remain open with your input unchanged.
Press &apos;Save downtime&apos; again to finalize.

If you choose &apos;No&apos;, we will attempt to create a new downtime anyway.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="430"/>
        <source>The downtime you were editing has changed on the server.

Do you want to reload the latest downtime data?

If you choose &apos;Yes&apos;, your current inputs will be replaced with the server&apos;s data.
If you choose &apos;No&apos;, we will attempt to save your current changes anyway.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DowntimesFormDialog</name>
    <message>
        <location filename="../downtimes.py" line="24"/>
        <source>Downtime Edit</source>
        <translation>Редактировать простой</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="53"/>
        <source>End time</source>
        <translation>Время окончания</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="34"/>
        <source>I want to finish downtime record</source>
        <translation>Хочу завершить запись простоя</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="44"/>
        <source>Apply auto end time</source>
        <translation>Применить автоматическое время окончания</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="45"/>
        <source>Set Current Time</source>
        <translation>Установить текущее время</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="133"/>
        <source>Save downtime</source>
        <translation>Сохранить</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="55"/>
        <source>Auto end time</source>
        <translation>Автоматическое время окончания</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="63"/>
        <source>Downtime ID</source>
        <translation>ID простоя</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="64"/>
        <source>Vehicle ID</source>
        <translation>ID машины</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="65"/>
        <source>Type</source>
        <translation>Тип</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="66"/>
        <source>Description</source>
        <translation>Описание</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="67"/>
        <source>Start time</source>
        <translation>Время начала</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="195"/>
        <source>Not available</source>
        <translation>Недоступно</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="99"/>
        <source>End time must be later than start time and before the current time.</source>
        <translation>Время окончания должно быть позже времени начала и раньше текущего времени.</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="115"/>
        <source>Close downtime record</source>
        <translation>Закрыть запись простоя</translation>
    </message>
    <message>
        <location filename="../downtimes.py" line="137"/>
        <source>Not set</source>
        <translation>Не выбран</translation>
    </message>
</context>
<context>
    <name>DowntimesTypes</name>
    <message>
        <location filename="../ext_translations.tri" line="410"/>
        <source>drill_maintenance</source>
        <translation>Обслуживание машины</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="411"/>
        <source>drill_maintenance_description</source>
        <translation>Техническое обслуживание станка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="412"/>
        <source>no_operator</source>
        <translation>Нет оператора</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="413"/>
        <source>no_operator_description</source>
        <translation>Оператор не назначен на станок</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="414"/>
        <source>no_task</source>
        <translation>Нет задания</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="415"/>
        <source>no_task_description</source>
        <translation>Не назначено задание для станка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="416"/>
        <source>robot_malfunction</source>
        <translation>Неисправность робота</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="417"/>
        <source>robot_malfunction_description</source>
        <translation>Робот не функционирует. Возможные причины: датчики, проводка, механическая поломка, ПО</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="418"/>
        <source>software_update</source>
        <translation>Обновление ПО</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="419"/>
        <source>software_update_description</source>
        <translation>Производится обновление ПО на станке</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="420"/>
        <source>area_not_ready</source>
        <translation>Участок не готов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="421"/>
        <source>area_not_ready_description</source>
        <translation>Участок не готов к работе</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="422"/>
        <source>refueling</source>
        <translation>Заправка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="423"/>
        <source>refueling_description</source>
        <translation>Ожидание заправки материалов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="424"/>
        <source>robot_maintenance</source>
        <translation>Обслуживание робота</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="425"/>
        <source>robot_maintenance_description</source>
        <translation>Обслуживание робота (калибровка, настройка)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="426"/>
        <source>manual_operation</source>
        <translation>Ручной режим</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="427"/>
        <source>manual_operation_description</source>
        <translation>Станок работает в ручном режиме (под управлением машиниста на борту)</translation>
    </message>
</context>
<context>
    <name>DrillJoystickWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="86"/>
        <source>Driving</source>
        <translation>Движение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="87"/>
        <source>Jacks</source>
        <translation>Домкраты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="88"/>
        <source>Retract</source>
        <translation>Втянуть</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="89"/>
        <source>Extend</source>
        <translation>Выдвинуть</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="90"/>
        <source>Right track</source>
        <translation>Правая гусеница</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="91"/>
        <source>Left track</source>
        <translation>Левая гусеница</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="92"/>
        <source>Back</source>
        <translation>Назад</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="93"/>
        <source>Forth</source>
        <translation>Вперёд</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="95"/>
        <source>Drilling</source>
        <translation>Бурение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="96"/>
        <source>Feed force</source>
        <translation>Сила подачи</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="97"/>
        <source>Rotation</source>
        <translation>Вращение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="100"/>
        <source>Arm</source>
        <translation>Люнет</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="101"/>
        <source>Open</source>
        <translation>Открыть</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="102"/>
        <source>Close</source>
        <translation>Закрыть</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="133"/>
        <source>Feed</source>
        <translation>Подача</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="134"/>
        <source>Up</source>
        <translation>Прибавить</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="135"/>
        <source>Down</source>
        <translation>Убавить</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="136"/>
        <source>Compressor</source>
        <translation>Компрессор</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="137"/>
        <source>On</source>
        <translation>Вкл</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="138"/>
        <source>Off</source>
        <translation>Выкл</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="110"/>
        <source>Leveling</source>
        <translation>Выравнивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="111"/>
        <source>All jacks</source>
        <translation>Все домкраты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="112"/>
        <source>Rear</source>
        <translation>Задний</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="114"/>
        <source>Tower</source>
        <translation>Мачта</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="115"/>
        <source>Inclined Pin</source>
        <translation>Наклонный Штифт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="116"/>
        <source>Tilt</source>
        <translation>Наклон</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="119"/>
        <source>Vertical Pin</source>
        <translation>Вертикальный Штифт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="121"/>
        <source>Wrench</source>
        <translation>Ключ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="122"/>
        <source>Release</source>
        <translation>Разжим</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="123"/>
        <source>Turn</source>
        <translation>Повернуть</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="124"/>
        <source>Stow</source>
        <translation>Убрать</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="125"/>
        <source>Swing in</source>
        <translation>Подвести</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="126"/>
        <source>Fork</source>
        <translation>Вилка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="127"/>
        <source>Stop</source>
        <translation>Остановить</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="129"/>
        <source>Carousel</source>
        <translation>Карусель</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="130"/>
        <source>Index</source>
        <translation>Индекс</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="98"/>
        <source>CCW</source>
        <translation>Против часовой</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="99"/>
        <source>CW</source>
        <translation>По часовой</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="117"/>
        <source>Raise</source>
        <translation>Поднять</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="118"/>
        <source>Lower</source>
        <translation>Опустить</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="56"/>
        <source>Red button</source>
        <translation>Красная кнопка</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="59"/>
        <source>Green button</source>
        <translation>Зелёная кнопка</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="139"/>
        <source>Red and Green Buttons</source>
        <translation>Красная и зелёная кнопки</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="142"/>
        <source>Knob widget</source>
        <translation>Кнопка-ручка</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="145"/>
        <source>Left Joystick</source>
        <translation>Левый джойстик</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="148"/>
        <source>Center Joystick</source>
        <translation>Центральный джойстик</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="151"/>
        <source>Right Joystick</source>
        <translation>Правый джойстик</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="154"/>
        <source>Tumbler Widget</source>
        <translation>Тумблер</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="15"/>
        <source>Left</source>
        <translation>Левый</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="16"/>
        <source>Right</source>
        <translation>Правый</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="131"/>
        <source>Dust flaps</source>
        <translation>Фартук</translation>
    </message>
</context>
<context>
    <name>DropActionButton</name>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="16"/>
        <source>Confirm current task cancelling</source>
        <translation type="unfinished">Подтвердите отмену текущего задания</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="25"/>
        <source>Permitted to qualified Engineers or Developers only</source>
        <translation type="unfinished">Действие разрешено только квалифицированным сервисным инженерам и разработчикам</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="27"/>
        <source>Wrong password</source>
        <translation type="unfinished">Неверный пароль</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="84"/>
        <source>Unusual Action</source>
        <translation type="unfinished">Нестандартное действие</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/drop_action_button.py" line="84"/>
        <source>
                                            Drilling in the process, can&apos;t cancel task right now.
                                            Enter password if still want to proceed
                                            </source>
        <translation type="unfinished">Бурение уже начато, невозможно отменить задание.
ВВедите пароль если все равно хотите отменить</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="41"/>
        <source>Cancel
task</source>
        <translation>Отменить
задание</translation>
    </message>
</context>
<context>
    <name>DropDrillError</name>
    <message>
        <location filename="../ext_translations.tri" line="42"/>
        <source>Clear
error</source>
        <translation>Очистить
ошибку</translation>
    </message>
</context>
<context>
    <name>DropTailingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="43"/>
        <source>Clear
tailing</source>
        <translation>Очистить
бровку</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="44"/>
        <source>Detected tailing will be cleared</source>
        <translation>Линия бровки будет очищена</translation>
    </message>
</context>
<context>
    <name>DynamicActionButton</name>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="66"/>
        <source>Finish
Action</source>
        <translation type="unfinished">Завершить
Действие</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="48"/>
        <source>Finish
Drilling</source>
        <translation type="unfinished">Завершить
Бурение</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="53"/>
        <source>Finish
Stow</source>
        <translation type="unfinished">Завершить
Разборку</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="58"/>
        <source>Finish
Buildup</source>
        <translation type="unfinished">Завершить
Наращивание</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="63"/>
        <source>Finish
Moving</source>
        <translation type="unfinished">Завершить
Движение</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="16"/>
        <source>WARNING: Before finishing rod buildup, make sure you have completed the process manually!
Incorrect completion may damage the drilling rig.</source>
        <translation type="unfinished">ВНИМАНИЕ: Перед тем как закончить наращивание убедитесь, что процесс полностью завершен вручную!
Неправильное завершение может повредить станок!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="17"/>
        <source>Are you sure you want to finish rod buildup?</source>
        <translation type="unfinished">Вы уверены, что хотите завершить наращивание?</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="18"/>
        <source>Finish Rod Buildup</source>
        <translation type="unfinished">Завершить наращивание</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="24"/>
        <source>WARNING: Before finishing rod stow, make sure you have completed the process manually!
Incorrect completion may damage the drilling rig.</source>
        <translation type="unfinished">ВНИМАНИЕ: Перед тем как закончить разбор става убедитесь, что процесс полностью завершен вручную!
Неправильное завершение может повредить станок!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="25"/>
        <source>Are you sure you want to finish rod stow?</source>
        <translation type="unfinished">Вы уверены, что хотите завершить разбор става?</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/dynamic_action_button.py" line="26"/>
        <source>Finish Rod Stow</source>
        <translation type="unfinished">Завершить разбор става</translation>
    </message>
</context>
<context>
    <name>DynamicLabel</name>
    <message>
        <location filename="../ext_translations.tri" line="176"/>
        <source>Opened</source>
        <translation>Открыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="177"/>
        <source>Closed</source>
        <translation>Закрыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="178"/>
        <source>Extended</source>
        <translation>Выдвинута</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="179"/>
        <source>Retracted</source>
        <translation>Убрана</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="180"/>
        <source>Index 1</source>
        <translation>Индекс 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="181"/>
        <source>Index 2</source>
        <translation>Индекс 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="182"/>
        <source>Stowed</source>
        <translation>Убран</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="183"/>
        <source>Engaged</source>
        <translation>Подведён</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="186"/>
        <source>Removed</source>
        <translation>Убрана</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="188"/>
        <source>No data</source>
        <translation>Нет данных</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="184"/>
        <source>Gripped</source>
        <translation>Захвачен</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="185"/>
        <source>Released</source>
        <translation>Освобожден</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="189"/>
        <source>Locked</source>
        <translation>Заблокирован</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="187"/>
        <source>Moving</source>
        <translation>Движение</translation>
    </message>
</context>
<context>
    <name>EliminateFirstLevelRestrictionsButton</name>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="123"/>
        <source>Error</source>
        <translation type="unfinished">Ошибка</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="123"/>
        <source>Wrong password</source>
        <translation type="unfinished">Неверный пароль</translation>
    </message>
</context>
<context>
    <name>EliminateFullRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="52"/>
        <source>All
Restrictions
off</source>
        <translation>Снять
все
ограничения</translation>
    </message>
</context>
<context>
    <name>EliminateFullRestrictionsButton</name>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="192"/>
        <source>Error</source>
        <translation type="unfinished">Ошибка</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/eliminate_restrictions_button.py" line="192"/>
        <source>Wrong password</source>
        <translation type="unfinished">Неверный пароль</translation>
    </message>
</context>
<context>
    <name>EliminateRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="51"/>
        <source>Eliminate
Restrictions</source>
        <translation>Снять
ограничения</translation>
    </message>
</context>
<context>
    <name>EmergencyDialog</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="757"/>
        <source>Emergency</source>
        <translation>Аварийная остановка</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="772"/>
        <source>&lt;b&gt;Emergency was set for the {} vehicle.&lt;/b&gt;&lt;br&gt;&lt;br&gt;</source>
        <translation>&amp;lt;b&amp;gt;Аварийная остановка была произведена для {} машины.&amp;lt;/b&amp;gt;&amp;lt;br&amp;gt;&amp;lt;br&amp;gt;</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="773"/>
        <source>Operator can activate the emergency mode with a button on the control panel or remotely with a radio remote control button. &lt;br&gt;&lt;br&gt;Motor would be automatically stopped in this mode. &lt;br&gt;&lt;br&gt;Please identify the problem that caused the emergency and eliminate it. Verify with the cameras that the operation is safe. &lt;br&gt;&lt;br&gt;Afterwards, you can reset the emergency with the button below. &lt;br&gt;&lt;br&gt;To hide this window, press the cancel button. You can return to this window later by pressing the emergency button in the vehicle selection window.</source>
        <translation>Оператор может активировать аварийную остановку кнопкой на панели управления или дистанционно с помощью кнопки радиоуправления. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;В этом режиме двигатель будет автоматически остановлен. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;Пожалуйста, установите проблему, которая вызвала аварийную остановку, и устраните её. По камерам убедитесь, что данная операция безопасна. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;После этого вы можете сбросить аварийную ситуацию с помощью кнопки ниже. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;Чтобы скрыть это окно, нажмите кнопку &amp;quot;Отмена&amp;quot;. Вы можете вернуться к этому окну позднее, нажав кнопку аварийной остановки в окне выбора машины.</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="784"/>
        <source>Reset emergency</source>
        <translation>Сбросить аварийную остановку</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="785"/>
        <source>Hide this dialog</source>
        <translation>Убрать этот диалоговое окно</translation>
    </message>
</context>
<context>
    <name>EmergencySetter</name>
    <message>
        <location filename="../gui_modules/controls/emergency_setter.py" line="25"/>
        <source>DENY MOVEMENT</source>
        <translation type="unfinished">ЗАПРЕТ ДВИЖЕНИЯ</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/emergency_setter.py" line="32"/>
        <source>PERMIT MOVEMENT</source>
        <translation type="unfinished">РАЗРЕШЕНИЕ ДВИЖЕНИЯ</translation>
    </message>
</context>
<context>
    <name>FinishBuildupButton</name>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="51"/>
        <source>End
Extending</source>
        <translation type="unfinished">Закончить
Наращивание</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="25"/>
        <source>Attention!
Make sure that the new rod is extended
Fork and key is retracted</source>
        <translation type="unfinished">Внимание! Убедитесь что штанга нарощена,
Вилка и ключ убраны</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_buildup_button.py" line="27"/>
        <source>Attention!</source>
        <translation type="unfinished">Внимание</translation>
    </message>
</context>
<context>
    <name>FinishHoleButton</name>
    <message>
        <location filename="../ext_translations.tri" line="45"/>
        <source>Finish
drilling</source>
        <translation>Завершить
бурение</translation>
    </message>
</context>
<context>
    <name>FinishMovingButton</name>
    <message>
        <location filename="../ext_translations.tri" line="46"/>
        <source>Finish
moving</source>
        <translation>Завершить
движение</translation>
    </message>
</context>
<context>
    <name>FinishStowButton</name>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="52"/>
        <source>Finish
Stow</source>
        <translation type="unfinished">Закончить
разборку</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="26"/>
        <source>Attention!
Make sure the boring rod is not in the borehole,
Fork and Key are retracted away!</source>
        <translation type="unfinished">Внимание!
Убедитесь что штанга не в скважине,
Вилка и ключ убраны</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/finish_stow_button.py" line="28"/>
        <source>Attention!</source>
        <translation type="unfinished">Внимание</translation>
    </message>
</context>
<context>
    <name>HardEmergencyButton</name>
    <message>
        <location filename="../gui_modules/buttons/hard_emergency_button.py" line="82"/>
        <source>Emergency
Stop</source>
        <translation type="unfinished">Аварийная
Остановка</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/hard_emergency_button.py" line="91"/>
        <source>Continue
working</source>
        <translation type="unfinished">Продолжить
работу</translation>
    </message>
</context>
<context>
    <name>InputDialog</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="230"/>
        <source> description</source>
        <translation type="unfinished">описание</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="239"/>
        <source>Event description</source>
        <translation type="unfinished">Описание события</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="325"/>
        <source>From</source>
        <comment>for messaging: _from_ vehicle </comment>
        <translation type="unfinished">От</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="325"/>
        <source>At</source>
        <comment>for messaging: received _at_ time</comment>
        <translation type="unfinished">В</translation>
    </message>
</context>
<context>
    <name>JoystickWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="652"/>
        <source>Top Action</source>
        <translation>Верхнее действие</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="653"/>
        <source>Bottom Action</source>
        <translation>Нижнее действие</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="654"/>
        <source>0.0%</source>
        <translation>0.0%</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="659"/>
        <source>Nonlinear</source>
        <translation>Нелинейный</translation>
    </message>
</context>
<context>
    <name>KnobViewWithCaption</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="535"/>
        <source>Nonlinear</source>
        <comment>Toggle Switch caption</comment>
        <translation>Нелинейный</translation>
    </message>
</context>
<context>
    <name>MachineStateManagement</name>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="80"/>
        <source>Unlock</source>
        <translation type="unfinished">Разблокировать</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="112"/>
        <source>Lock</source>
        <translation type="unfinished">Заблокировать</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="128"/>
        <source>Commit</source>
        <translation type="unfinished">Подтвердить</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="136"/>
        <source>Cancel</source>
        <translation type="unfinished">Отменить</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="200"/>
        <source>Idle</source>
        <translation>Ожидание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="202"/>
        <source>Driving</source>
        <translation>Движение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="245"/>
        <source>Leveling</source>
        <translation>Выравнивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="204"/>
        <source>Tower tilt</source>
        <translation>Управление мачтой</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="226"/>
        <source>Drilling</source>
        <translation>Бурение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="206"/>
        <source>Shaft Build-up</source>
        <translation>Наращивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="207"/>
        <source>Shaft stow</source>
        <translation>Снятие штанги</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="208"/>
        <source>Grounding</source>
        <translation>Снятие домкратов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="209"/>
        <source>Wait for Remote</source>
        <translation>Ожидание ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="210"/>
        <source>Prepare for Remote</source>
        <translation>Подготовка к ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="211"/>
        <source>In Remote</source>
        <translation>ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="212"/>
        <source>Finishing Remote</source>
        <translation>Окончание ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="213"/>
        <source>Waiting after leveling</source>
        <translation>Ожидание бурения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="214"/>
        <source>String restoring</source>
        <translation>Подъем става</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="215"/>
        <source>Waiting before leveling</source>
        <translation>Ожидание выравнивания</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="216"/>
        <source>Failure</source>
        <translation>Ошибка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="217"/>
        <source>Locking</source>
        <translation>Выдвижение штифтов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="218"/>
        <source>Unlocking</source>
        <translation>Втягивание штифтов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="219"/>
        <source>Arm closing</source>
        <translation>Закрытие люнета</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="220"/>
        <source>Tilt Regulation</source>
        <translation>Наклон мачты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="221"/>
        <source>Touchdown</source>
        <translation>Касание земли</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="225"/>
        <source>Overburden pass</source>
        <translation>Забуривание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="227"/>
        <source>Hard Rot</source>
        <translation>Заклинивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="228"/>
        <source>Pullup</source>
        <translation>Протяжка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="229"/>
        <source>After Pullup</source>
        <translation>Опускание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="230"/>
        <source>String Raising</source>
        <translation>Подъем става</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="231"/>
        <source>Waiting after drill</source>
        <translation>Окончание бурения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="232"/>
        <source>Pass soft</source>
        <translation>Мягкая порода</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="233"/>
        <source>Tower</source>
        <translation>Высвобождение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="234"/>
        <source>Arm</source>
        <translation>Люнет</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="235"/>
        <source>Opening</source>
        <translation>Открытие</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="237"/>
        <source>Closing</source>
        <translation>Закрытие</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="238"/>
        <source>Close</source>
        <translation>Закрыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="239"/>
        <source>Jacks</source>
        <translation>Домкраты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="241"/>
        <source>Pulling</source>
        <translation>Снятие с домкратов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="242"/>
        <source>Pulled</source>
        <translation>Домкраты втянуты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="243"/>
        <source>Restoring pulled</source>
        <translation>Втягивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="247"/>
        <source>Holding</source>
        <translation>Удерживание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="248"/>
        <source>Carousel</source>
        <translation>Карусель</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="249"/>
        <source>Carousel opening</source>
        <translation>Выдвижение кассеты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="250"/>
        <source>Carousel closing</source>
        <translation>Задвижение кассеты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="251"/>
        <source>Carousel turning cw</source>
        <translation>Завижение карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="252"/>
        <source>Carousel turning ccw</source>
        <translation>Выдвижение карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="199"/>
        <source>Main State</source>
        <translation>Состояние</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="201"/>
        <source>Init</source>
        <translation>Инициализация</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="244"/>
        <source>Lowering jacks</source>
        <translation>Вытягивание домкратов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="240"/>
        <source>Dust flaps</source>
        <translation>Защита от пыли</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="236"/>
        <source>Open</source>
        <translation>Открыть</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="224"/>
        <source>Unstucking</source>
        <translation>Выход из застревания</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="223"/>
        <source>Lifting up</source>
        <translation>Поднятие домкратов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="246"/>
        <source>Final leveling</source>
        <translation>Завершающе выравнивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="253"/>
        <source>Planner</source>
        <translation>Планировщик движения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="254"/>
        <source>Moving</source>
        <translation>Движение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="255"/>
        <source>Approach</source>
        <translation>Наведение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="256"/>
        <source>Computing</source>
        <translation>Вычисление</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="88"/>
        <source>Dangerous feature</source>
        <translation type="unfinished">Опасная функция</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="89"/>
        <source>You are activating a dangerous function.
If used incorrectly, there is a risk of damaging the machine.
Continue only if you know what you are doing.

Enter your password:</source>
        <translation type="unfinished">Вы активируете опасную функцию.
При неправильном использовании есть риск повреждения станка.
Продолжайте, только если знаете, что делаете.

Введите пароль:</translation>
    </message>
    <message>
        <location filename="../gui_modules/controls/machine_state_management.py" line="92"/>
        <source>Ok</source>
        <translation type="unfinished">Ок</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="257"/>
        <source>Initial Check</source>
        <translation>Инициализация</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="258"/>
        <source>Unstuck Down</source>
        <translation>Расклинивание вниз</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="259"/>
        <source>Unstuck Spin</source>
        <translation>Расклинивание вращением</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="260"/>
        <source>Unstuck Up</source>
        <translation>Расклинивание вверх</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="261"/>
        <source>Rod changer</source>
        <translation>Наращивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="262"/>
        <source>Aligning rod for cup</source>
        <translation>Выравнивание штанги для стакана</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="263"/>
        <source>Aligning rod for fork</source>
        <translation>Выравнивание штанги для вилки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="264"/>
        <source>Approaching rod for carousel</source>
        <translation>Подход к штанге для карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="265"/>
        <source>Approaching rod for fork</source>
        <translation>Подход к штанге для вилки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="266"/>
        <source>Apply wrench</source>
        <translation>Применение ключа</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="267"/>
        <source>Brakeout</source>
        <translation>Расстыковка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="268"/>
        <source>Closing arm</source>
        <translation>Закрытие люнета</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="269"/>
        <source>Closing carousel</source>
        <translation>Закрытие карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="270"/>
        <source>Closing wrench</source>
        <translation>Закрытие ключа</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="271"/>
        <source>Detaching rod</source>
        <translation>Отсоединение штанги</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="272"/>
        <source>Detaching rod in cup</source>
        <translation>Отсоединение штанги в стакане</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="273"/>
        <source>Lift head to pull depth</source>
        <translation>Подъем вращателя на глубину извлечения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="274"/>
        <source>Finish</source>
        <translation>Завершение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="275"/>
        <source>Inserting rod in cup</source>
        <translation>Вставка штанги в стакан</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="276"/>
        <source>Lifting head to carousel</source>
        <translation>Подъем вращателя к карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="277"/>
        <source>Closing fork</source>
        <translation>Закрытие вилки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="278"/>
        <source>Screwing new rod</source>
        <translation>Закручивание новой штанги</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="279"/>
        <source>Opening arm</source>
        <translation>Открытие люнета</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="280"/>
        <source>Opening carousel</source>
        <translation>Открытие карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="281"/>
        <source>Opening fork and arm</source>
        <translation>Открытие вилки и люнета</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="282"/>
        <source>Opening wrench</source>
        <translation>Открытие ключа</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="283"/>
        <source>Pull out</source>
        <translation>Извлечение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="284"/>
        <source>Screwing</source>
        <translation>Закручивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="285"/>
        <source>Turn shaft ccw</source>
        <translation>Поворот штанги против часовой стрелки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="286"/>
        <source>Turn shaft cw</source>
        <translation>Поворот штанги по часовой стрелке</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="287"/>
        <source>Turn shaft cw in carousel cup</source>
        <translation>Поворот штанги по часовой стрелке в стакане</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="288"/>
        <source>Opening fork</source>
        <translation>Открытие вилки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="289"/>
        <source>Fork</source>
        <translation>Вилка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="290"/>
        <source>Missed rod</source>
        <translation>Промах мимо лысок</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="291"/>
        <source>Turning CW</source>
        <translation>Поворот по часовой стрелке</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="292"/>
        <source>Turning CCW</source>
        <translation>Поворот против часовой стрелки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="293"/>
        <source>Turning wrench</source>
        <translation>Поворот ключа</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="294"/>
        <source>Releasing wrench</source>
        <translation>Освобождение ключа</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="295"/>
        <source>Wrench</source>
        <translation>Ключ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="296"/>
        <source>Wrench is open</source>
        <translation>Ключ открыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="297"/>
        <source>Wrench is closed</source>
        <translation>Ключ закрыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="298"/>
        <source>Init Check</source>
        <translation>Инициализация</translation>
    </message>
</context>
<context>
    <name>MapWidget</name>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="80"/>
        <source>Auto Center: ON</source>
        <translation>Центрирование</translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="88"/>
        <source>Reset View</source>
        <translation>Сбросить отображение</translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>Auto Center: {0}</source>
        <translation>Центрирование: {0}</translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>ON</source>
        <translation>ВКЛ</translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="248"/>
        <source>OFF</source>
        <translation>ВЫКЛ</translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="759"/>
        <source>Distance to hole: -- m</source>
        <translation>Расстояние до скважины: -- м</translation>
    </message>
    <message>
        <location filename="../gui_modules/map/map_widget.py" line="786"/>
        <source>Distance to hole: {0:.2f} m</source>
        <translation>Расстояние до скважины: {0:.2f} м</translation>
    </message>
</context>
<context>
    <name>MessageLogs</name>
    <message>
        <location filename="../ext_translations.tri" line="306"/>
        <source>100000101</source>
        <translation>Ошибка номер 1000000101</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="307"/>
        <source>fucking_code_123</source>
        <translation>Факинг код 123</translation>
    </message>
</context>
<context>
    <name>MessageTypeColour</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="279"/>
        <source>Red</source>
        <translation type="unfinished">Красный</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="281"/>
        <source>Yellow</source>
        <translation type="unfinished">Жёлтый</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="283"/>
        <source>Orange</source>
        <translation type="unfinished">Оранжевый</translation>
    </message>
</context>
<context>
    <name>MessagesWidgetMaximized</name>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="147"/>
        <source>Message Log</source>
        <translation type="unfinished">Пул сообщений</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="165"/>
        <source>Debug Mode</source>
        <translation type="unfinished">Режим отладки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="312"/>
        <source>ALREADY_DONE</source>
        <translation>Команда уже была выполнена</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="313"/>
        <source>WAIT_FINISH</source>
        <translation>Команда/задание не принята; машина выполняет предыдущую</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="314"/>
        <source>LOST_HAL_CONN</source>
        <translation>Отсутствует соединение с HAL-сервером</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="315"/>
        <source>CAN_NOT_SWITCH_TO_RC</source>
        <translation>Робот не смог перейти в режим ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="318"/>
        <source>RC_ROTATE_CAROUSEL_IDX1</source>
        <translation>Используйте режим ДУ для поворота карусели по часовой стрелке</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="319"/>
        <source>RC_ROTATE_CAROUSEL_IDX2</source>
        <translation>Используйте режим ДУ для поворота карусели против часовой стрелки</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="320"/>
        <source>RC_CLOSE_CAROUSEL</source>
        <translation>Используйте режим ДУ для закрытия карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="321"/>
        <source>RC_OPEN_CAROUSEL</source>
        <translation>Используйте режим ДУ для открытия карусели</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="322"/>
        <source>RC_OPEN_ARM</source>
        <translation>Используйте режим ДУ для открытия люнета</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="323"/>
        <source>RC_CLOSE_ARM</source>
        <translation>Используйте режим ДУ для закрытия люнета</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="326"/>
        <source>RC_MOVE</source>
        <translation>Используйте режим ДУ для переезда в целевую точку</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="327"/>
        <source>RC_UNSTUCK</source>
        <translation>Используйте режим ДУ для освобождения бура из застревания</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="328"/>
        <source>RC_LEVELING</source>
        <translation>Используйте режим ДУ для отдомкрачивания</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="330"/>
        <source>RC_LIFT_STRING</source>
        <translation>Используйте режим ДУ для подтягивания просевшего става</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="332"/>
        <source>RC_RODE_CHANGE</source>
        <translation>Используйте режим ДУ для наращивания штанги</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="333"/>
        <source>RC_LOCK_TOWER</source>
        <translation>Используйте режим ДУ для фиксации мачты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="334"/>
        <source>RC_UNLOCK_TOWER</source>
        <translation>Используйте режим ДУ для разблокировки мачты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="335"/>
        <source>RC_TOWER_TILT</source>
        <translation>Используйте режим ДУ для регулирования угла мачты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="336"/>
        <source>COMPRESSOR_FAILURE</source>
        <translation>Неисправность компрессора</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="338"/>
        <source>WRENCH_SWITCH_FAILURE</source>
        <translation>Неисправность концевика ключа</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="340"/>
        <source>IBOX_NO_CONN</source>
        <translation>Отсутсвие соединения с iBox</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="341"/>
        <source>IPLACE_NO_CONN</source>
        <translation>Отсутсвие соединения с РМО</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="348"/>
        <source>RESTORING_PULLED_JACKS</source>
        <translation>Вягивание просевших домкратов...</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="351"/>
        <source>RESTORED_STRING</source>
        <translation>Став востановлен из проседшего положения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="352"/>
        <source>STARTING_NEW_ACT</source>
        <translation>Робот начинает новое задание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="353"/>
        <source>READY_FOR_NEW_ACT</source>
        <translation>Робот готов к получению нового задания</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="354"/>
        <source>CAN_NOT_DRILL_CUR_HOLE</source>
        <translation>Робот не смог начать бурить текущую скважину и поехал на следующую</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="355"/>
        <source>CAN_NOT_ACCEPT_ACTION</source>
        <translation>Робот не может начать новое задание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="342"/>
        <source>LEFT_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>Неисправность концевика левого домкрата</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="343"/>
        <source>RIGHT_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>Неисправность концевика правого домкрата</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="344"/>
        <source>REAR_JACK_LIMIT_SWITCH_FAILED</source>
        <translation>Неисправность концевика заднего домкрата</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="345"/>
        <source>CLOSING_ARM_PULLING_JACKS</source>
        <translation>Машина переходит к новому заданию, закрывает люнет и втягивает домкраты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="346"/>
        <source>WRONG_ARM_STATE</source>
        <translation>Неправильное состояние люнета</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="347"/>
        <source>HEAD_TOO_LOW</source>
        <translation>Нельзя закрыть люнет: вращатель слишком низко</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="324"/>
        <source>RC_OPEN_DUST_FLAPS</source>
        <translation>Нужно открыть фартук в дистанционном управлении</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="325"/>
        <source>RC_CLOSE_DUST_FLAPS</source>
        <translation>Нужно закрыть фартук в дистанционном управлении</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="329"/>
        <source>RC_LEVEL_TOO_LOW</source>
        <translation>Необходимо отдомкратиться выше в дистанционном управлении</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="356"/>
        <source>COLLISION_PREVENTER_STOPPAGE</source>
        <translation>Остановка по препятствию</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="215"/>
        <source>Save recent onboard bags for further analysis?</source>
        <translation type="unfinished">Сохранить последние бортовые бэги для дальнейшего анализа?</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/messages_widget.py" line="216"/>
        <source>Save event?</source>
        <translation type="unfinished">Сохранить событие?</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="316"/>
        <source>DEPTH_CORRECTION_FAILED</source>
        <translation>Не удалось откорректировать глубину</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="337"/>
        <source>LEVEL_SENSOR_FAILURE</source>
        <translation>Невалидные показания датчика наклона</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="349"/>
        <source>NO_RTK</source>
        <translation>Неверные GPS-данные (отсутствие диффпоправки)</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="350"/>
        <source>ANGLES_ABOVE_LIMITS</source>
        <translation>Превышены допустимые углы наклона</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="357"/>
        <source>NO_FRONT_LIDAR</source>
        <translation>Отсутствуют данные с переднего лидара</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="358"/>
        <source>NO_REAR_LIDAR</source>
        <translation>Отсутствуют данные с заднего лидара</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="317"/>
        <source>UNEVEN_TERRAIN</source>
        <translation>Неровная поверхность, движение будет замедленно</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="359"/>
        <source>SLOW_MAST_TILT_MSG</source>
        <translation>Замедление наклона мачты из-за текущего положения мачты!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="360"/>
        <source>FORBID_MOVING_ROD_MSG</source>
        <translation>Перемещение штанги запрещено из-за текущего состояния люнета!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="361"/>
        <source>FORBID_MOVING_JACKS_MSG</source>
        <translation>Перемещение домкратов запрещено из-за текущего положения бура!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="331"/>
        <source>RC_STRING_TOO_HIGH</source>
        <translation>Штанга находится слишком высоко</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="339"/>
        <source>LASER_FAILURE</source>
        <translation>Неисправность лазера</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="362"/>
        <source>FORBID_MOVING_TRACKS_MSG</source>
        <translation>Перемещение гусениц запрещено из-за текущего положения домкратов!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="364"/>
        <source>SENSOR_TIMEOUT</source>
        <translation>Не поступают данные с датчика. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="365"/>
        <source>SENSOR_OOR</source>
        <translation>Показания датчика вне допустимого диапазона. Проверьте датчик или его </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="366"/>
        <source>SENSOR_STALL</source>
        <translation>Показания датчика не обновляются. Проверьте датчик или контроллер.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="367"/>
        <source>ROLL_CRITICAL</source>
        <translation>Достигнуто критическое значение поперечного наклона. Требуется немедленное внимание.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="368"/>
        <source>PITCH_CRITICAL</source>
        <translation>Достигнуто критическое значение продольного наклона. Требуется немедленное внимание.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="369"/>
        <source>WRENCH_SENSOR1_FAILURE</source>
        <translation>Отказ датчика ключа 1. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="370"/>
        <source>WRENCH_SENSOR2_FAILURE</source>
        <translation>Отказ датчика ключа 2. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="371"/>
        <source>WRENCH_SENSOR3_FAILURE</source>
        <translation>Отказ датчика ключа 3. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="372"/>
        <source>RPM_SENSOR_FAILURE</source>
        <translation>Отказ датчика частоты вращения. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="373"/>
        <source>FORK_LINEAR_SENSOR_FAILURE</source>
        <translation>Отказ линейного датчика вилки. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="374"/>
        <source>LJACK_LINEAR_SENSOR_FAILURE</source>
        <translation>Отказ линейного датчика левого домкрата. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="375"/>
        <source>RJACK_LINEAR_SENSOR_FAILURE</source>
        <translation>Отказ линейного датчика правого домкрата. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="377"/>
        <source>RRJACK_LINEAR_SENSOR_FAILURE</source>
        <translation>Отказ линейного датчика заднего правого домкрата. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="378"/>
        <source>ROT_PRESS_SENSOR_FAILURE</source>
        <translation>Отказ датчика давления вращения. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="379"/>
        <source>FEED_PRESS_SENSOR_FAILURE</source>
        <translation>Отказ датчика давления подачи. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="380"/>
        <source>AIR_PRESS_SENSOR_FAILURE</source>
        <translation>Отказ датчика давления воздуха. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="381"/>
        <source>CAROUSEL_LINEAR_SENSOR_FAILURE</source>
        <translation>Отказ линейного датчика карусели. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="382"/>
        <source>ARM_LINEAR_SENSOR1_FAILURE</source>
        <translation>Отказ линейного датчика люнета 1. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="383"/>
        <source>ARM_LINEAR_SENSOR2_FAILURE</source>
        <translation>Отказ линейного датчика люнета 2. Проверьте датчик.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="376"/>
        <source>RLJACK_LINEAR_SENSOR_FAILURE</source>
        <translation>Отказ линейного датчика заднего левого домкрата. Проверьте датчик.</translation>
    </message>
</context>
<context>
    <name>MovingDataTranslator</name>
    <message>
        <location filename="../ext_translations.tri" line="300"/>
        <source>Spindle depth: </source>
        <translation>Положение вращателя: </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="301"/>
        <source>m</source>
        <translation>м</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="302"/>
        <source>Drill feed speed: </source>
        <translation>Скорость вращателя: </translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="303"/>
        <source>m/h</source>
        <translation>м/час</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="403"/>
        <source>Engine</source>
        <translation>Обороты двигателя</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="404"/>
        <source>Battery</source>
        <translation>Аккумулятор</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="405"/>
        <source>Coolant temp</source>
        <translation>Температура охлажд. жидкости</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="406"/>
        <source>Water level</source>
        <translation>Уровень воды</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="407"/>
        <source>Fuel level</source>
        <translation>Уровень топлива</translation>
    </message>
</context>
<context>
    <name>PhotoButton</name>
    <message>
        <location filename="../ext_translations.tri" line="50"/>
        <source>Save
images</source>
        <translation>Сохранить
изображения</translation>
    </message>
</context>
<context>
    <name>PullupButton</name>
    <message>
        <location filename="../ext_translations.tri" line="49"/>
        <source>Force
pullup</source>
        <translation>Протяжка</translation>
    </message>
</context>
<context>
    <name>RebootButton</name>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="43"/>
        <source>Robot
Reset</source>
        <translation type="unfinished">Сбросить
Робота</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="68"/>
        <source>Dangerous Feature!</source>
        <translation type="unfinished">Опасная фунция!</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/reboot_button.py" line="68"/>
        <source>
                                       Dangerous Feature will be activated.
                                       Vehicle damage is possible.

                                       MAKE SURE THE ROD IS NOT IN THE BOREHOLE
                                       and that it sage to retract the jacks.

                                       Enter Password:
                                       </source>
        <translation type="unfinished">Вы активируюте опасную функцию.
Возможно повреждение станка.

УБЕДИТЕСЬ ЧТО ШТАНГА НЕ В СКВАЖИНЕ,
и что домкраты можно безопасно втянуть.

Введите пароль:</translation>
    </message>
</context>
<context>
    <name>RecalibAirButton</name>
    <message>
        <location filename="../ext_translations.tri" line="47"/>
        <source>Recalibrate
air pressure</source>
        <translation>Перекалибровать
давление воздуха</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="48"/>
        <source>Current air pressure will be set as nominal</source>
        <translation>Текущее давление воздуха будет установлено как номинальное</translation>
    </message>
</context>
<context>
    <name>ResetShaftCounterButton</name>
    <message>
        <location filename="../gui_modules/buttons/reset_shaft_counter_button.py" line="12"/>
        <source>Reset
Rods</source>
        <translation type="unfinished">Сбросить
Штанги</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="429"/>
        <source>WARNING: Resetting the rod counter while the rod is in the ground can cause severe damage to the rod and the drilling rig!</source>
        <translation>ВНИМАНИЕ: Сброс счётчика штанг, когда штанга находится в скважине, может привести к серьёзному повреждению штанги и буровой установки!</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="430"/>
        <source>Are you sure you want to reset the rod counter?</source>
        <translation>Вы уверены, что хотите сбросить счётчик штанг?</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="431"/>
        <source>Reset Rod Counter</source>
        <translation>Сброс счётчика штанг</translation>
    </message>
</context>
<context>
    <name>RestoreRestrictions</name>
    <message>
        <location filename="../ext_translations.tri" line="53"/>
        <source>Restore
Control
Restrictions</source>
        <translation>Восстановить
ограничение
управлением</translation>
    </message>
</context>
<context>
    <name>SendHoleButton</name>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="48"/>
        <source>Drill
here</source>
        <translation type="unfinished">Бурить
Здесь</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="70"/>
        <source>Depth</source>
        <translation type="unfinished">Глубина</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="80"/>
        <source>Tilt</source>
        <translation type="unfinished">Наклон</translation>
    </message>
    <message>
        <location filename="../gui_modules/buttons/send_hole_button.py" line="89"/>
        <source>Hole ID</source>
        <translation type="unfinished">ID скважины</translation>
    </message>
</context>
<context>
    <name>SmartButton</name>
    <message>
        <location filename="../ext_translations.tri" line="309"/>
        <source>test Button Core</source>
        <translation>Тест кнопк Ядро</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="83"/>
        <source>Water</source>
        <translation>Вода</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="84"/>
        <source>Lights</source>
        <translation>Фары</translation>
    </message>
</context>
<context>
    <name>SmartButtonGroup</name>
    <message>
        <location filename="../ext_translations.tri" line="63"/>
        <source>Cameras mode</source>
        <translation>Камеры</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="79"/>
        <source>Operation mode</source>
        <translation>Режим работы</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="69"/>
        <source>Drill mode</source>
        <translation>Режим бурения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="70"/>
        <source>Wet mode</source>
        <translation>Тип скважины</translation>
    </message>
</context>
<context>
    <name>StaticLabel</name>
    <message>
        <location filename="../ext_translations.tri" line="166"/>
        <source>Position</source>
        <translation>Позиция</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="167"/>
        <source>Index</source>
        <translation>Индекс</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="174"/>
        <source>Dust flaps</source>
        <translation>Фартук</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="169"/>
        <source>Grip</source>
        <translation>Захват</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="170"/>
        <source>Vertical</source>
        <translation>Верт.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="171"/>
        <source>Slanted</source>
        <translation>Накл.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="172"/>
        <source>U-Fork</source>
        <translation>Вилка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="173"/>
        <source>Rod support</source>
        <translation>Люнет</translation>
    </message>
</context>
<context>
    <name>Switch2MovingDialog</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="676"/>
        <source>Switch to Autonomous Moving</source>
        <translation>Переключение в автономный режим</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="692"/>
        <source>&lt;b&gt;Drill with vehicle id {} could move after switch to autonomous mode.&lt;/b&gt;&lt;br&gt;</source>
        <translation>&amp;lt;b&amp;gt;Станок с идентификатором id {} может начать движение после переключения в автономный режим.&amp;lt;/b&amp;gt;&amp;lt;br&amp;gt;</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="693"/>
        <source>Drill could move after switch to autonomous mode, because planner is in Moving, Approach or Computing mode. &lt;br&gt;&lt;br&gt;If this is what you expect, check the goal point and just press &lt;b&gt;Continue&lt;/b&gt; button. &lt;br&gt;&lt;br&gt;If you finished moving manually and want robot to continue with drilling, press &lt;b&gt;Finish moving and continue autonomous&lt;/b&gt;.</source>
        <translation>Станок может начать движение после перехода в автономный режим, потому что планировщик находится в режиме движения, наведения или вычислений. &amp;lt;br&amp;gt;&amp;lt;br&amp;gt;Если это то, чего вы ожидаете, проверьте точку цели и просто нажмите кнопку &amp;lt;b&amp;gt;Продолжить&amp;lt;/b&amp;gt;. Если вы закончили перемещение вручную и хотите, чтобы робот продолжил работу перейдя в бурение, нажмите кнопку &amp;lt;b&amp;gt;Закончить перемещение и перейти в автономный режим&amp;lt;/b&amp;gt;.</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="699"/>
        <source>Continue</source>
        <translation>Продолжить</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="700"/>
        <source>Finish moving and continue autonomous</source>
        <translation>Закончить перемещение и перейти в автономный режим</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="701"/>
        <source>Cancel</source>
        <translation>Отменить</translation>
    </message>
</context>
<context>
    <name>Task</name>
    <message>
        <location filename="../ext_translations.tri" line="385"/>
        <source>drilling</source>
        <translation>Бурение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="386"/>
        <source>idle</source>
        <translation>Ожидание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="388"/>
        <source>moving</source>
        <translation>Движение</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="389"/>
        <source>leveling</source>
        <translation>Выравнивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="390"/>
        <source>tower_tilt</source>
        <translation>Наклон мачты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="391"/>
        <source>shaft_buildup</source>
        <translation>Наращивание</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="392"/>
        <source>shaft_stow</source>
        <translation>Снятие штанги</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="393"/>
        <source>grounding</source>
        <translation>Снятие домкратов</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="394"/>
        <source>remote_wait</source>
        <translation>Ожидание ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="396"/>
        <source>remote_prepare</source>
        <translation>Подготовка к ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="397"/>
        <source>end_remote</source>
        <translation>Окончание ДУ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="398"/>
        <source>wait_after_level</source>
        <translation>Ожидание бурения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="399"/>
        <source>restore_string</source>
        <translation>Подъем става</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="400"/>
        <source>wait_before_level</source>
        <translation>Ожидание выравнивания</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="387"/>
        <source>failure</source>
        <translation>Ошибка</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="395"/>
        <source>remote</source>
        <translation>Удалённое управление</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="401"/>
        <source>init_check</source>
        <translation>Инициализация</translation>
    </message>
</context>
<context>
    <name>TextComplicatedWidget</name>
    <message>
        <location filename="../gui_modules/text/text_complicated_widget.py" line="95"/>
        <source> no version</source>
        <translation type="unfinished"> версия недоступна</translation>
    </message>
    <message>
        <location filename="../gui_modules/text/text_complicated_widget.py" line="97"/>
        <source> HAL unreachable</source>
        <translation type="unfinished"> HAL недоступен</translation>
    </message>
</context>
<context>
    <name>TextWidget</name>
    <message>
        <location filename="../ext_translations.tri" line="1"/>
        <source>Select Vehicle</source>
        <translation>Выбор машины</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="2"/>
        <source>Connection Mode</source>
        <translation>Режим подключения</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="32"/>
        <source>Remote Control Mode</source>
        <translation>Режим пульта</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="142"/>
        <source>Vertical</source>
        <translation>Верт.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="143"/>
        <source>Slanted</source>
        <translation>Накл.</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="141"/>
        <source>Pins</source>
        <translation>Штифты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="146"/>
        <source>Opened</source>
        <translation>Открыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="147"/>
        <source>Closed</source>
        <translation>Закрыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="144"/>
        <source>Arm</source>
        <translation>Люнет</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="149"/>
        <source>Extended</source>
        <translation>Выдвинута</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="161"/>
        <source>Removed</source>
        <translation>Убрана</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="148"/>
        <source>Carousel</source>
        <translation>Карусель</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="162"/>
        <source>Jacks</source>
        <translation>Домкраты</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="163"/>
        <source>Tower is in position</source>
        <translation>Мачта установлена</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="150"/>
        <source>Retracted</source>
        <translation>Убрана</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="151"/>
        <source>Index 1</source>
        <translation>Индекс 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="152"/>
        <source>Index 2</source>
        <translation>Индекс 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="153"/>
        <source>Rod in cup 1</source>
        <translation>Штанга в чаше 1</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="154"/>
        <source>Rod in cup 2</source>
        <translation>Штанга в чаше 2</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="155"/>
        <source>Wrench</source>
        <translation>Ключ</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="157"/>
        <source>Stowed</source>
        <translation>Убран</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="158"/>
        <source>Engaged</source>
        <translation>Подведён</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="159"/>
        <source>Grip open</source>
        <translation>Захват открыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="160"/>
        <source>Grip closed</source>
        <translation>Захват закрыт</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="164"/>
        <source>Work permission</source>
        <translation>Разрешение работы</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="145"/>
        <source>Dust flaps</source>
        <translation>Zashita ot pili</translation>
    </message>
    <message>
        <location filename="../ext_translations.tri" line="156"/>
        <source>U-Fork</source>
        <translation>Вилка</translation>
    </message>
</context>
<context>
    <name>TumblerWidget</name>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="869"/>
        <source>Tumbler On</source>
        <translation>Тумблер вкл</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="870"/>
        <source>Tumbler in the Middle</source>
        <translation>Тумблер в центре</translation>
    </message>
    <message>
        <location filename="../drillJoystickWidget/joystick.py" line="871"/>
        <source>Tumbler Off</source>
        <translation>Тумблер выкл</translation>
    </message>
</context>
<context>
    <name>VehicleCard</name>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="92"/>
        <source>Task: No info</source>
        <translation>Задание: нет информации</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="94"/>
        <source>Offline</source>
        <translation>Не в сети</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="96"/>
        <source>No info</source>
        <translation>Нет информации</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="108"/>
        <source>Watch</source>
        <comment>like watch what this vehicle is doing</comment>
        <translation>Наблюдение</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="113"/>
        <source>Control</source>
        <comment>like control this vehicle remotely</comment>
        <translation>Управление</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="117"/>
        <source>Disconnect</source>
        <comment>there means not watch and not control vehicle</comment>
        <translation>Отключение</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="120"/>
        <source>Move Permission</source>
        <comment>does vehicle has permission to move?</comment>
        <translation>Запрет движения</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="123"/>
        <source>RoboMode</source>
        <comment>Checkbox, if checked -&gt; vehicle is robot, else manual</comment>
        <translation>Роборежим</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="265"/>
        <source>Robot</source>
        <translation>Робот</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="268"/>
        <source>Manual</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Ручной режим</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="282"/>
        <source>Ok</source>
        <translation>Ок</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="285"/>
        <source>Need Permission</source>
        <comment>Caption for RED LED when vehicle is not permitted to move</comment>
        <translation>Нет разрешения</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="385"/>
        <source>No task</source>
        <comment>Label in vehicle selector when no task given</comment>
        <translation>Нет задания</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="391"/>
        <source>Online</source>
        <comment>LED indicator when vehicle is online</comment>
        <translation>В сети</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="395"/>
        <source>Robot</source>
        <comment>LED when vehicle is in Robo-mode</comment>
        <translation>Робот</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="398"/>
        <source>Manual</source>
        <comment>LED when vehicle is not in Robo-mode</comment>
        <translation>Ручной режим</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="402"/>
        <source>Offline</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Не в сети</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="406"/>
        <source>No Data</source>
        <comment>LED indicator when vehicle is offline</comment>
        <translation>Нет данных</translation>
    </message>
    <message>
        <location filename="../customClasses/vehicleSelector.py" line="115"/>
        <source>Emergency</source>
        <comment>reset emergency</comment>
        <translation>Аварийная ситуация</translation>
    </message>
</context>
<context>
    <name>VehicleSelectorWrapper</name>
    <message>
        <location filename="../gui_modules/controls/vehicle_selector_wrapper.py" line="94"/>
        <source>No data</source>
        <translation type="unfinished">Нет данных</translation>
    </message>
</context>
<context>
    <name>veh_selector</name>
    <message>
        <location filename="../veh_selector.py" line="32"/>
        <source>Watch</source>
        <translation>Наблюдение</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="33"/>
        <source>Control</source>
        <translation>Управление</translation>
    </message>
    <message>
        <location filename="../veh_selector.py" line="34"/>
        <source>Disconnect</source>
        <translation>Отключение</translation>
    </message>
</context>
</TS>
