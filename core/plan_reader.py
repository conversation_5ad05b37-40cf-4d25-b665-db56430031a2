#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import math
import time
import hashlib
from threading import Thread, Event
import requests
import logging
import random

logger = logging.getLogger(__name__)

class PlanReader(object):
    def __init__(self, core, *args, **kwargs):
        self.core = core
        self.killed = False
        self.retry_delays = {}  # Track retry delays per URL
        self.min_retry_delay = 1.0  # Initial retry delay in seconds
        self.max_retry_delay = 30.0  # Maximum retry delay
        self.stop_event = Event()
        self.cached_plans = {}  # Cache for successful plan fetches

    def prepare(self, *args, **kwargs):
        self.update_thread = Thread(target=self.update, daemon=True)

    def start(self):
        self.update_thread.start()

    def get_retry_delay(self, url):
        """Calculate retry delay with exponential backoff and jitter"""
        if url not in self.retry_delays:
            self.retry_delays[url] = self.min_retry_delay
            return self.min_retry_delay
        
        # Exponential backoff with jitter (to avoid thundering herd)
        current_delay = self.retry_delays[url]
        # Double the delay for next time, but cap at max
        next_delay = min(current_delay * 2, self.max_retry_delay)
        self.retry_delays[url] = next_delay
        
        # Add jitter (±20%)
        jitter = random.uniform(-0.2, 0.2) * current_delay
        return max(current_delay + jitter, self.min_retry_delay)

    def reset_retry_delay(self, url):
        """Reset the retry delay for a URL after successful connection"""
        if url in self.retry_delays:
            del self.retry_delays[url]

    def update(self):
        while not self.killed and not self.stop_event.is_set():
            vehid = None
            if self.core.in_rc_vehid is not None:
                vehid = self.core.in_rc_vehid
            elif self.core.watched_vehid is not None:
                vehid = self.core.watched_vehid
            elif self.core.selected_vehid is not None:
                vehid = self.core.selected_vehid

            if vehid is not None and "vehid" in self.core.telemetry[vehid].keys() and self.core.telemetry[vehid]["vehid"] is not None:
                hal_vehid = str(self.core.telemetry[vehid]["vehid"])
                self.fetch_vehicle_data(vehid, hal_vehid)

            # Sleep for a reasonable time before next check
            self.stop_event.wait(0.6)

    def fetch_vehicle_data(self, vehid, hal_vehid):
        """Fetch vehicle data with retry logic"""
        try:
            req_url = self.core.config['hal_ip'] + "/api/vehicles/" + hal_vehid
            response = requests.get(req_url, timeout=2.0)
            
            if response.ok:
                self.reset_retry_delay(req_url)
                veh_data = response.json()
                if "planid" in veh_data and veh_data['planid'] is not None:
                    planid = str(veh_data['planid'])
                    self.fetch_plan_data(vehid, planid, veh_data['active_hole'])
                else:
                    # No plan assigned to vehicle
                    if hasattr(self.core, 'plan') and vehid in self.core.plan:
                        del self.core.plan[vehid]
                    self.core.next_hole_location = None
            else:
                retry_delay = self.get_retry_delay(req_url)
                logger.debug(f"Failed to fetch vehicle data, status: {response.status_code}. Retrying in {retry_delay:.2f}s")

        except requests.exceptions.Timeout:
            retry_delay = self.get_retry_delay(req_url)
            logger.debug(f"Timeout fetching vehicle data. Retrying in {retry_delay:.2f}s")
        except requests.exceptions.RequestException as e:
            retry_delay = self.get_retry_delay(req_url)
            logger.debug(f"Error fetching vehicle data: {e}. Retrying in {retry_delay:.2f}s")
        except Exception as e:
            retry_delay = self.get_retry_delay(req_url)
            logger.error(f"Unexpected error fetching vehicle data: {e}. Retrying in {retry_delay:.2f}s")

    def fetch_plan_data(self, vehid, planid, active_hole):
        """Fetch plan data with retry logic"""
        try:
            req_url = self.core.config['hal_ip'] + "/api/plan-no-kobus/" + planid
            response = requests.get(req_url, timeout=2.0)
            
            if response.ok:
                self.reset_retry_delay(req_url)
                plan = response.json()
                
                # Store plan data in core for visualization
                if not hasattr(self.core, 'plan'):
                    self.core.plan = {}
                self.core.plan[vehid] = plan
                
                # Cache the plan for this planid
                self.cached_plans[planid] = plan
                
                if active_hole is None:
                    self.core.next_hole_location = None
                elif "holes" in plan:
                    self.process_holes(plan, active_hole)
            else:
                # Check if we have a cached plan for this planid
                if planid in self.cached_plans:
                    plan = self.cached_plans[planid]
                    if active_hole is None:
                        self.core.next_hole_location = None
                    elif "holes" in plan:
                        self.process_holes(plan, active_hole)
                
                retry_delay = self.get_retry_delay(req_url)
                logger.debug(f"Failed to fetch plan data, status: {response.status_code}. Retrying in {retry_delay:.2f}s")

        except requests.exceptions.Timeout:
            retry_delay = self.get_retry_delay(req_url)
            logger.debug(f"Timeout fetching plan data. Retrying in {retry_delay:.2f}s")
        except requests.exceptions.RequestException as e:
            retry_delay = self.get_retry_delay(req_url)
            logger.debug(f"Error fetching plan data: {e}. Retrying in {retry_delay:.2f}s")
        except Exception as e:
            retry_delay = self.get_retry_delay(req_url)
            logger.error(f"Unexpected error fetching plan data: {e}. Retrying in {retry_delay:.2f}s")

    def process_holes(self, plan, active_hole):
        """
        Process hole data and store active hole location in core
        """
        for hole in plan['holes']:
            if hole['holeid'] == active_hole:
                self.core.next_hole_location = hole['location']
                break

    def kill(self):
        """
        Stop the update thread
        """
        self.killed = True
        self.stop_event.set()
        if hasattr(self, 'update_thread') and self.update_thread.is_alive():
            self.update_thread.join(timeout=1.0)





