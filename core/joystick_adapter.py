#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import enum
import serial
import socket
import logging
import json
import numpy as np
from copy import deepcopy
from threading import Thread
from typing import Dict, List, Any, Optional

from PyQt6.QtCore import QObject

# Import constants from joystick module
from drillJoystickWidget.joystick import RESTRICTION_MODE, EASY_RESTRICTION_MODE, RECOMMENDATION_MODE
from gui_common import get_from_telemetry

THREAD_SLEEP_RATE = 0.02
ENCODING = 'utf-8'

SLOW_MAST_TILT_MSG = 'Slowing down mast tilt due to current mast position!'
FORBID_MOVING_ROD_MSG = 'Moving rod is forbidden due to rod support current state!'
FORBID_MOVING_JACKS_MSG = 'Moving jacks is forbidden due to current string position!'
FORBID_MOVING_TRACKS_MSG = 'Moving tracks is forbidden due to current jacks position!'


def make_nonlinear(val):
    """
    Нормализует данные c с джойстиков.
    """
    s = np.sign(val)
    v = abs(val)

    v = 0.092 * np.exp(2.48525 * v) - 0.1
    if v < 0:
        v = 0.0
    elif v > 1.0:
        v = 1.0
    v *= s
    val = v
    return val


class Layouts(str, enum.Enum):
    """ Названия лейаутов панели управления """
    DRIVING = "driving"
    DRILLING = "drilling"
    LEVELING = "leveling"
    TOWER_CONTROL = "tower_control"
    BUILDUP = "buildup"
    CAROUSEL = "carousel_control"


class JoystickAdapter(QObject):
    """Adapter for reading remote control commands from the joystick panel.

    This class handles the communication with the physical joystick controller
    via serial port or UDP, processes the input data, and provides it to the
    application core.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """Initialize the JoystickAdapter.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.core = core

        # State variables
        self.killed = False
        self.channels: Dict[str, Any] = {}
        self.layouts: Dict[str, Any] = {}
        self.channel_name_map: Dict[str, Any] = {}
        self.channel_to_index_map: Dict[str, int] = {}
        self.connection_loss_timeout = 0.0
        self.last_udp_data_time = 0.0  # Track when we last received UDP data

        # Communication objects
        self.ser: Optional[serial.Serial] = None
        self.srv: Optional[socket.socket] = None

        # Thread management
        self.listen_thread: Optional[Thread] = None

        # Configuration parameters
        self.pkg_size: Optional[int] = None
        self.pkg_timeout: Optional[float] = None
        self.joystick_ang_channel_map: Optional[Dict[str, Any]] = None
        self.switch_pose_channel_map: Optional[Dict[str, Any]] = None
        self.joystick_power_coef: Optional[Dict[str, float]] = None
        self.channel_thresholds: Optional[Dict[str, float]] = None
        self.switches_state_map: Optional[Dict[str, Any]] = None
        self.no_lock_modes: List[str] = []

        # Machine state parameters
        self.arm_close_min_depth: Optional[float] = None
        self.arm_open_max_depth: Optional[float] = None
        self.previous_vehid: Optional[str] = None
        self.max_string_pos: Optional[float] = None
        self.m_top_ang_easy_tilt: Optional[float] = None
        self.m_low_ang_easy_tilt: Optional[float] = None
        self.m_top_ang_hard_tilt: Optional[float] = None
        self.m_low_ang_hard_tilt: Optional[float] = None
        self.easy_tilt_coef: Optional[float] = None
        self.hard_tilt_coef: Optional[float] = None

        # Restriction handling
        self.restriction_msg: Optional[str] = None
        self.last_restriction_msg_ts: Optional[float] = None

    def prepare(self,
                layouts: Dict[str, Any],
                channel_name_map: Dict[str, Any],
                channel_to_index_map: Dict[str, int],
                joystick_power_coef: Dict[str, float],
                channel_thresholds: Dict[str, float],
                switches_state_map: Dict[str, Any],
                udp_ip: str,
                udp_port: int,
                port: List[str] = ["/dev/ttyACM0"],
                baudrate: int = 115200,
                pkg_size: int = 49,
                pkg_timeout: float = 0.0,
                no_lock_modes: List[str] = []) -> None:
        """
        Prepare the joystick adapter by connecting to the joystick controller.

        Attempts to connect to the joystick controller via serial port.
        If connection fails, initializes a UDP server as an alternative
        channel for receiving control signals.

        Args:
            layouts: Dictionary of joystick layouts
            channel_name_map: Mapping of channel names
            channel_to_index_map: Mapping of channel names to indices
            joystick_power_coef: Power coefficients for joysticks
            channel_thresholds: Thresholds for channels
            switches_state_map: Mapping of switch states
            udp_ip: IP address for UDP server
            udp_port: Port for UDP server
            port: Controller port in the system (default: ["dev/ttyACM0"])
            baudrate: Data transmission rate (default: 115200)
            pkg_size: Size of the package from the controller (default: 49)
            pkg_timeout: Timeout (default: 0.0, non-blocking)
            no_lock_modes: Modes where locking is disabled (default: [])
        """
        for p in port:
            try:
                self.ser = serial.Serial(port=p,
                                         baudrate=baudrate,
                                         parity=serial.PARITY_NONE,
                                         stopbits=serial.STOPBITS_ONE,
                                         bytesize=serial.EIGHTBITS,
                                         timeout=0)
                break

            except (OSError, serial.SerialException) as e:
                logging.error(e)
                logging.error("Could not connect to joystick controller %s" % p)

        self.__init_udp_server(udp_ip, udp_port)

        self.pkg_size = pkg_size
        self.pkg_timeout = pkg_timeout

        self.layouts = layouts
        self.channel_name_map = channel_name_map
        self.channel_to_index_map = channel_to_index_map
        self.joystick_power_coef = joystick_power_coef
        self.channel_thresholds = channel_thresholds
        self.switches_state_map = switches_state_map
        self.prev_layout = None
        self.reset_lock_allowed = [True, True, True]
        self.fixed_joys = [None, None, None]
        self.joy_zero_counter = [0, 0, 0]
        self.last_cycle_time = time.time()
        self.no_lock_modes = no_lock_modes

    def start(self) -> None:
        """
        Start the monitoring thread to read joystick input.

        This starts either the serial or UDP monitoring thread based on available connection.
        """
        if self.ser:
            update_func = self.update_from_serial
        else:
            update_func = self.update_from_udp
        self.listen_thread = Thread(target=update_func, args=())
        self.listen_thread.daemon = True  # Make thread a daemon so it exits when main thread exits
        self.listen_thread.start()

    def __init_udp_server(self, ip, port):
        """
        Инициализирует UDP-сервер для приема данных управления.
        """
        logging.info("Starting UDP-server to accept control data...")
        self.srv = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.srv.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        # Set a timeout so recvfrom doesn't block forever
        self.srv.settimeout(0.5)

        self.srv.bind((ip, port))
        logging.info("Started UDP-server on %s:%s" % (ip, port))

    def update_from_udp(self):
        """
        Читает данные управления по UDP.
        """
        # UDP inactivity timeout (in seconds)
        UDP_INACTIVITY_TIMEOUT = 1.0

        while not self.killed:
            current_time = time.time()
            try:
                # Use try-except to handle socket timeout
                raw_data, _ = self.srv.recvfrom(128 * 1024)  # We don't need the address
                try:
                    data = json.loads(raw_data)
                except TypeError:
                    data = None

                curr_layout_name = self.core.current_layout_name
                if data is not None:
                    self.last_udp_data_time = current_time  # Update the timestamp
                    self.update_controls_udp(data)
                    self.set_controls(curr_layout_name)
                else:
                    logging.warning("Lost connection to joystick controller; Stop!")
                    self.reset_controls()
                    self.stop_by_permission()
            except socket.timeout:
                # Check if we've exceeded the inactivity timeout
                if self.last_udp_data_time > 0 and (current_time - self.last_udp_data_time) > UDP_INACTIVITY_TIMEOUT:
                    logging.warning("UDP joystick inactivity timeout; Resetting controls")
                    self.reset_controls()
                    self.last_udp_data_time = 0  # Reset to avoid repeated warnings
            except OSError as e:
                # Socket was likely closed during shutdown
                if self.killed:
                    break
                logging.error(f"Socket error: {e}")
                # Try to recover
                time.sleep(1)

            time.sleep(THREAD_SLEEP_RATE)

    def update_from_serial(self):
        """
        Читает данные управления по RS-232.
        """
        prev_rc_vehid = None
        while not self.killed:
            data = self.read_data()

            dt = time.time() - self.last_cycle_time
            self.last_cycle_time = time.time()
            curr_layout_name = self.core.current_layout_name

            if data is not None:
                channels = self.update_controls_ser(data)

                if (
                        self.prev_layout is None or self.prev_layout == curr_layout_name) and self.core.in_rc_vehid == prev_rc_vehid:
                    for i in range(3):
                        if channels[
                            'joystick%d_button' % (i + 1)] and self.core.current_layout_name not in self.no_lock_modes:
                            self.fixed_joys[i] = channels['joystick%d' % (i + 1)]
                            self.reset_lock_allowed[i] = False
                else:
                    for i in range(3):
                        self.reset_lock_allowed[i] = True
                        self.fixed_joys[i] = None

                for i in range(3):
                    if abs(channels['joystick' + str(i + 1)]) < 0.03:
                        self.joy_zero_counter[i] += dt
                        if self.joy_zero_counter[i] > 0.5:
                            self.reset_lock_allowed[i] = True
                    else:
                        self.joy_zero_counter[i] = 0
                        if self.reset_lock_allowed[i]:
                            self.fixed_joys[i] = None

                    self.channels = channels
                    if self.fixed_joys[i] is not None:
                        self.channels['joystick' + str(i + 1)] = self.fixed_joys[i]
                        self.channels['joystick%d_button' % (i + 1)] = True
                    else:
                        self.channels['joystick%d_button' % (i + 1)] = False
            else:
                logging.warning("Lost connection to joystick controller; Stop!")
                # self.set_emergency()
                self.stop_by_permission()

            prev_rc_vehid = self.core.in_rc_vehid
            self.set_controls(curr_layout_name)
            time.sleep(THREAD_SLEEP_RATE)

    def set_controls(self, curr_layout_name):
        """
        Process joystick data and publish control messages to ROS2 topics
        using the zenoh client.

        Args:
            curr_layout_name: The name of the current layout
        """
        self.core.newJoystickDataReady.emit(self.channels)
        if curr_layout_name and not self.killed:
            control_data = self.generate_controls(curr_layout_name)
            self.publish_to_topics(control_data)
        self.prev_layout = curr_layout_name

    def read_data(self) -> str:
        """
        Читает данные от контролера.
        При отсутсвии/потери подключения к контролеру - отправляет команду экстренной
        остановки на машину, если она была под управлением, завершает работу.

        :return: Строка прочитаных из каналов значений.
        """
        no_conn_time = 0
        while not self.killed:
            ts_start = time.time()
            try:
                # Check if the connection is still alive
                if self.killed or not self.ser or not self.ser.is_open:
                    return None

                b = self.ser.read(1)
                if b != b'1':
                    continue
                b = self.ser.read(1)
                if b != b'A':
                    continue
                data = b'1A' + self.ser.read(47)
                if data[-1] != ord('F') and data[-2] != ord('F'):
                    logging.info("Wrong packet: ", data[-1], data[-2])
                    continue
                self.ser.flushInput()
                return str(data.decode())
            except (OSError, serial.SerialException) as e:
                # Log the error but only if we're not shutting down
                if not self.killed:
                    logging.error(e)
                    logging.warning("Lost connection with joystick controller; Stop!")
                    self.stop_by_permission()
                no_conn_time += time.time() - ts_start
                if no_conn_time > self.pkg_timeout:
                    break
        return None

    def update_controls_udp(self, data):
        """
        Парсит данные об управлении, полученные по UDP.

        Args:
            data (dict): cловарь с данными с джойстиков вида:
                {
                    "joystick1":0,
                     "joystick2":0,
                     "joystick3":0,
                     "pot_value":0,
                     "button_pair":0,
                     "switch_value":0
                }
        """
        for k, val in data.items():
            k = self.channel_name_map[k]
            if k in self.core.nonlin_enabled.keys() and self.core.nonlin_enabled[k]:
                val = make_nonlinear(val)

            self.channels[k] = val

        self.channels['joystick1_button'] = False
        self.channels['joystick2_button'] = False
        self.channels['joystick3_button'] = False

    def update_controls_ser(self, data):
        """
        Парсит данные об управлении, полученные по serial-порту.

        Args:
            data (str)
        """
        channels = {}
        for k in self.channel_to_index_map.keys():
            chann = self.channel_to_index_map[k]
            d = ''
            if isinstance(chann, list):
                for c in chann:
                    d += data[c]
            else:
                d = data[chann]

            if 'E' not in d:
                val = float(d)
                if k in self.switches_state_map.keys():
                    val = self.switches_state_map[k][str(int(val))]
                elif k in self.channel_thresholds.keys():
                    vmin = self.channel_thresholds[k][0]
                    vmid = self.channel_thresholds[k][1]
                    vmax = self.channel_thresholds[k][2]

                    if vmid <= val:
                        val = (val - vmid) / (vmax - vmid)
                    else:
                        val = -((vmid - val) / (vmid - vmin))
                    if abs(val) < 0.035:
                        val = 0
                    if val > 1:
                        val = 1.0
                    elif val < -1:
                        val = -1.0

                    if k in self.core.nonlin_enabled.keys() and self.core.nonlin_enabled[k]:
                        val = make_nonlinear(val)

                channels[k] = val
            else:
                channels[k] = 0
                self.set_emergency()
        return channels

    def set_emergency(self):
        self.stop_by_permission()
        cmd = {"emergency_control": "set"}
        if self.core.in_rc_vehid is not None:
            self.core.output_data[self.core.in_rc_vehid].update(cmd)
        elif self.core.watched_vehid is not None:
            self.core.output_data[self.core.watched_vehid].update(cmd)
        elif self.core.selected_vehid is not None:
            self.core.output_data[self.core.selected_vehid].update(cmd)

    def find_widget_by_control(self, control, curr_layout_name):
        """
        Find the widget associated with a control within a specific layout.
        If the control uses inversion (`-`) or a threshold (`@`), these are also processed.

        Args:
            control (str): The name of the control.
            curr_layout_name (str): The name of the current layout.

        Returns:
            str: The name of the widget associated with the control.
            If the control is not found, returns None.
        """
        layout = self.layouts[curr_layout_name]
        for topic in layout:
            if "fields" in layout[topic]:
                for ch, widget in layout[topic]["fields"].items():
                    if isinstance(widget, str):
                        widget_name, _, _ = widget.partition("@")
                        if widget_name.startswith("-"):
                            widget_name = widget_name[1:]
                        if ch == control:
                            return widget_name
        return None

    def generate_controls(self, curr_layout_name: str) -> dict:
        """
        Generate control messages for ROS2 topics based on joystick input and the current layout.

        Args:
            curr_layout_name: Name of the current layout

        Returns:
            Dictionary with control messages and vehicle ID
        """
        control_messages = {}
        if not curr_layout_name:
            return {'messages': control_messages, 'vehid': None}

        layout = self.layouts[curr_layout_name]
        
        # Determine vehicle ID using simplified logic
        vehid = self.core.in_rc_vehid if self.core.in_rc_vehid is not None else self.core.watched_vehid

        if vehid != self.previous_vehid:
            if vehid is not None:
                if 'get_params' in self.core.output_data[vehid].keys():
                    pl = json.loads(self.core.output_data[vehid]['get_params'])
                    pl.append("/DrillerNode/arm_close_min_depth")
                    pl.append("/DrillerNode/arm_open_max_depth")
                    pl.append("/RemoteConnectorNode/m_top_ang_easy_tilt")
                    pl.append("/RemoteConnectorNode/m_low_ang_easy_tilt")
                    pl.append("/RemoteConnectorNode/m_top_ang_hard_tilt")
                    pl.append("/RemoteConnectorNode/m_low_ang_hard_tilt")
                    pl.append("/RemoteConnectorNode/easy_tilt_coef")
                    pl.append("/RemoteConnectorNode/hard_tilt_coef")
                    pl.append("/MainStateMachineNode/top_to_initial")
                    pl.append("/MainStateMachineNode/allowed_string_drawdown")
                    self.core.output_data[vehid]['get_params'] = json.dumps(pl)
                else:
                    p = ["/DrillerNode/arm_close_min_depth", "/DrillerNode/arm_open_max_depth",
                         "/RemoteConnectorNode/m_top_ang_easy_tilt", "/RemoteConnectorNode/m_low_ang_easy_tilt",
                         "/RemoteConnectorNode/m_top_ang_hard_tilt", "/RemoteConnectorNode/m_low_ang_hard_tilt",
                         "/RemoteConnectorNode/easy_tilt_coef", "/RemoteConnectorNode/hard_tilt_coef",
                         "/MainStateMachineNode/top_to_initial", "/MainStateMachineNode/allowed_string_drawdown"]
                    self.core.output_data[vehid]['get_params'] = json.dumps(p)

                self.arm_close_min_depth = None
                self.arm_open_max_depth = None
                self.max_string_pos = None
                self.m_top_ang_easy_tilt = None
                self.m_low_ang_easy_tilt = None
                self.m_top_ang_hard_tilt = None
                self.m_low_ang_hard_tilt = None
                self.easy_tilt_coef = None
                self.hard_tilt_coef = None

        if (self.arm_close_min_depth is None or self.arm_open_max_depth is None) and vehid is not None:
            self.get_arm_minmax_depths(vehid)

        if (self.m_top_ang_easy_tilt is None or self.m_low_ang_easy_tilt is None or
            self.m_top_ang_hard_tilt is None or self.m_low_ang_hard_tilt is None or
            self.easy_tilt_coef is None or self.hard_tilt_coef is None) and vehid is not None:
            self.get_tilt_control_restrictions(vehid)

        if self.max_string_pos is None and vehid is not None:
            self.get_max_string_pos(vehid)

        self.previous_vehid = vehid
        self.core.joy_mode = {}
        self.restriction_msg = None

        if vehid is not None:
            # Check if drill_control_remote exists in current layout
            has_drill_topic = "drill_control_remote" in layout
            has_tower_topic = "tower_control_remote" in layout
            has_jacks_topic = "jacks_control_remote" in layout
            has_tracks_topic = "tracks_control_remote" in layout
            
            for topic_name, topic_config in layout.items():
                if "msg_type" not in topic_config or "fields" not in topic_config:
                    continue

                msg_fields = {}
                
                for field_name, channel_ref in topic_config["fields"].items():
                    # Handle constant values
                    if isinstance(channel_ref, (int, float)):
                        msg_fields[field_name] = channel_ref
                        continue
                    
                    # Process channel reference
                    thr = None
                    if '@' in channel_ref:
                        channel_ref, thr = channel_ref.split('@')
                        thr = float(thr)
                    
                    # Handle inverted values
                    if channel_ref.startswith('-'):
                        val = -self.channels[channel_ref[1:]]
                        widget_name = channel_ref[1:]
                    else:
                        val = self.channels[channel_ref]
                        widget_name = channel_ref
                    
                    # Apply threshold if necessary
                    if thr is not None:
                        val = 1 * int(np.sign(val)) if abs(val) >= thr else 0
                    
                    # Apply restrictions based on control type
                    # Apply restrictions for drill_control_remote if exists in layout
                    if has_drill_topic and topic_name == "drill_control_remote" and field_name == 'feed_speed' and \
                            self.arm_close_min_depth is not None and self.arm_open_max_depth is not None:
                        spindle_depth = get_from_telemetry(self.core, 'spindle_depth')
                        arm_open = get_from_telemetry(self.core, 'arm_open')
                        
                        # Find ctrl field in arm_control_remote if it exists
                        arm_widget_name = None
                        if "arm_control_remote" in layout and "fields" in layout["arm_control_remote"]:
                            for arm_field, arm_channel in layout["arm_control_remote"]["fields"].items():
                                if arm_field == "ctrl":
                                    arm_channel = arm_channel[1:] if arm_channel.startswith('-') else arm_channel
                                    arm_channel = arm_channel.split('@')[0] if '@' in arm_channel else arm_channel
                                    arm_widget_name = arm_channel
                                    break
                                    
                        if (spindle_depth < self.arm_close_min_depth and arm_open and val < 0) or \
                                (spindle_depth > self.arm_open_max_depth and not arm_open and val > 0):
                            val = 0.0
                            self.core.joy_mode[widget_name] = RESTRICTION_MODE
                            if arm_widget_name:
                                self.core.joy_mode[arm_widget_name] = RECOMMENDATION_MODE
                            self.restriction_msg = {
                                'msg': FORBID_MOVING_ROD_MSG,
                                'source': 'RMO',
                                'time': time.time(),
                                'code': 'FORBID_MOVING_ROD_MSG',
                                'level': 16
                            }
                    
                    # Apply restrictions for tower_control_remote if exists in layout
                    elif has_tower_topic and topic_name == "tower_control_remote" and field_name == 'tilt' and \
                         self.m_top_ang_easy_tilt is not None and self.m_low_ang_easy_tilt is not None:
                        mast_angle = -get_from_telemetry(self.core, "tower_angle")
                        if (mast_angle > self.m_top_ang_easy_tilt and val < 0) or \
                                (mast_angle < self.m_low_ang_easy_tilt and val > 0):
                            val *= self.easy_tilt_coef
                            self.core.joy_mode[widget_name] = EASY_RESTRICTION_MODE
                            self.restriction_msg = {
                                'msg': SLOW_MAST_TILT_MSG,
                                'source': 'RMO',
                                'time': time.time(),
                                'code': 'SLOW_MAST_TILT_MSG',
                                'level': 16
                            }
                        # slowdown again if closer
                        elif (mast_angle > self.m_top_ang_hard_tilt and val < 0) or \
                                (mast_angle < self.m_low_ang_hard_tilt and val > 0):
                            val *= self.hard_tilt_coef
                            self.core.joy_mode[widget_name] = EASY_RESTRICTION_MODE
                            self.restriction_msg = {
                                'msg': SLOW_MAST_TILT_MSG,
                                'source': 'RMO',
                                'time': time.time(),
                                'code': 'SLOW_MAST_TILT_MSG',
                                'level': 16
                            }
                    
                    # Apply restrictions for jacks if exists in layout
                    elif has_jacks_topic and topic_name == "jacks_control_remote" and \
                         (field_name == "rear" or field_name == "right" or field_name == "left") and \
                         self.max_string_pos is not None:
                        if val < 0 and get_from_telemetry(self.core, "spindle_depth") > self.max_string_pos:
                            val = 0
                            self.core.joy_mode[widget_name] = RESTRICTION_MODE
                            self.restriction_msg = {
                                'msg': FORBID_MOVING_JACKS_MSG,
                                'source': 'RMO',
                                'time': time.time(),
                                'code': 'FORBID_MOVING_JACKS_MSG',
                                'level': 16
                            }
                    
                    # Apply restrictions for tracks if exists in layout        
                    elif has_tracks_topic and topic_name == "tracks_control_remote" and \
                         (field_name == 'left' or field_name == 'right'):
                        all_jacks_pulled = get_from_telemetry(self.core, "rear_jack_pulled") and \
                                           get_from_telemetry(self.core, "left_jack_pulled") and \
                                           get_from_telemetry(self.core, "right_jack_pulled")
                        if not all_jacks_pulled:
                            if self.core.telemetry[vehid].get("current_robomode") and \
                                    self.core.telemetry[vehid].get("move_permission") and \
                                    self.core.telemetry[vehid].get("MainStateMachineNode") == "remote":

                                val = 0.0
                                self.core.joy_mode[widget_name] = RESTRICTION_MODE
                                self.restriction_msg = {
                                    'msg': FORBID_MOVING_TRACKS_MSG,
                                    'source': 'RMO',
                                    'time': time.time(),
                                    'code': 'FORBID_MOVING_TRACKS_MSG',
                                    'level': 16
                                }
                
                    # Store the processed value
                    msg_fields[field_name] = val
                
                # Add the message to control messages
                control_messages[topic_name] = {
                    "msg_type": topic_config["msg_type"],
                    "fields": msg_fields
                }

            # Handle restriction messages
            if self.restriction_msg is not None:
                if self.last_restriction_msg_ts is None:
                    self.last_restriction_msg_ts = self.restriction_msg['time']
                    self.core.log_msgs[vehid].append(self.restriction_msg)
                else:
                    if self.restriction_msg['time'] - self.last_restriction_msg_ts > 5:
                        self.last_restriction_msg_ts = self.restriction_msg['time']
                        self.core.log_msgs[vehid].append(self.restriction_msg)
                        
        return {'messages': control_messages, 'vehid': vehid}

    def publish_to_topics(self, control_data):
        """
        Publish control messages to ROS2 topics using zenoh_client.
        
        Args:
            control_data: Dictionary containing control messages and vehicle ID
        """
        if not hasattr(self.core, 'zenoh_client') or self.core.zenoh_client is None:
            logging.warning("zenoh_client not available, cannot publish control messages")
            return
        
        # Extract control messages and vehicle ID
        control_messages = control_data['messages']
        namespace = control_data['vehid']
        
        for topic_name, msg_data in control_messages.items():
            try:
                # Extract message type and fields
                msg_type = msg_data["msg_type"]
                fields = msg_data["fields"]
                
                # Use zenoh_client to publish - use regular publish instead of publish_joystick
                # to avoid issues with priority parameter
                self.core.zenoh_client.publish(
                    key_expr=topic_name,
                    msg_type=msg_type,
                    frame_id='joystick_panel',
                    namespace=namespace,
                    **fields
                )
            except Exception as e:
                logging.error(f"Error publishing to topic {topic_name}: {e}")

    def get_arm_minmax_depths(self, vehid):
        if 'params' in self.core.telemetry[vehid].keys() and "/DrillerNode/arm_close_min_depth" in \
                self.core.telemetry[vehid]['params'].keys() and "/DrillerNode/arm_open_max_depth" in \
                self.core.telemetry[vehid]['params'].keys():
            self.arm_close_min_depth = self.core.telemetry[vehid]['params']["/DrillerNode/arm_close_min_depth"]
            self.arm_open_max_depth = self.core.telemetry[vehid]['params']["/DrillerNode/arm_open_max_depth"]

    def get_max_string_pos(self, vehid):
        if 'params' in self.core.telemetry[vehid].keys() and "/MainStateMachineNode/top_to_initial" in \
                self.core.telemetry[vehid]['params'].keys() and "/MainStateMachineNode/allowed_string_drawdown" in \
                self.core.telemetry[vehid]['params'].keys():
            self.max_string_pos = self.core.telemetry[vehid]['params']["/MainStateMachineNode/top_to_initial"] + \
                                  self.core.telemetry[vehid]['params']["/MainStateMachineNode/allowed_string_drawdown"]

    def get_tilt_control_restrictions(self, vehid):
        if 'params' in self.core.telemetry[vehid].keys() and \
                "/RemoteConnectorNode/m_top_ang_easy_tilt" in self.core.telemetry[vehid]['params'].keys() and \
                "/RemoteConnectorNode/m_low_ang_easy_tilt" in self.core.telemetry[vehid]['params'].keys() and \
                "/RemoteConnectorNode/m_top_ang_hard_tilt" in self.core.telemetry[vehid]['params'].keys() and \
                "/RemoteConnectorNode/m_low_ang_hard_tilt" in self.core.telemetry[vehid]['params'].keys() and \
                "/RemoteConnectorNode/easy_tilt_coef" in self.core.telemetry[vehid]['params'].keys() and \
                "/RemoteConnectorNode/hard_tilt_coef" in self.core.telemetry[vehid]['params'].keys():
            self.m_top_ang_easy_tilt = self.core.telemetry[vehid]['params']["/RemoteConnectorNode/m_top_ang_easy_tilt"]
            self.m_low_ang_easy_tilt = self.core.telemetry[vehid]['params']["/RemoteConnectorNode/m_low_ang_easy_tilt"]
            self.m_top_ang_hard_tilt = self.core.telemetry[vehid]['params']["/RemoteConnectorNode/m_top_ang_hard_tilt"]
            self.m_low_ang_hard_tilt = self.core.telemetry[vehid]['params']["/RemoteConnectorNode/m_low_ang_hard_tilt"]
            self.easy_tilt_coef = self.core.telemetry[vehid]['params']["/RemoteConnectorNode/easy_tilt_coef"]
            self.hard_tilt_coef = self.core.telemetry[vehid]['params']["/RemoteConnectorNode/hard_tilt_coef"]
            print('GOT THEM')

    def reset_controls(self):
        """
        Reset all control channels to zero.
        This is called when a connection is lost to ensure no lingering commands.
        """
        # Reset all channels to zero
        for key in self.channels.keys():
            self.channels[key] = 0.0

        # Apply the reset controls
        if self.core.current_layout_name:
            self.set_controls(self.core.current_layout_name)

        # Ensure we're not in control mode
        self.stop_by_permission()

    def stop_by_permission(self):
        if self.core.in_rc_vehid is not None:
            vehid = self.core.in_rc_vehid
        elif self.core.watched_vehid is not None:
            vehid = self.core.watched_vehid
        elif self.core.selected_vehid is not None:
            vehid = self.core.selected_vehid
        else:
            vehid = None

        if vehid is not None:
            self.core.setMovePermission(vehid, False)

    def kill(self) -> None:
        """
        Stop all running threads and close connections.
        """
        self.killed = True

        # Clean up serial connection if open
        if self.ser and self.ser.is_open:
            try:
                self.ser.close()
            except Exception as e:
                logging.error(f"Error closing serial connection: {e}")

        # Clean up socket if open
        if self.srv:
            try:
                self.srv.close()
            except Exception as e:
                logging.error(f"Error closing socket: {e}")

        # Wait for thread to terminate
        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=1.0)  # Wait up to 1 second
