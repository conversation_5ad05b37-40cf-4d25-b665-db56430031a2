import os
import json
import sys
import glob
import serial
import collections
import contextlib


def get_serial_ports() -> list:
    """
    Возвращает список serial-портов, к которому подключено устройство.
    Предполагает, что в системе возможно подключение только одного устройства.

    :raises EnvironmentError:
        На неподдерживаемых и неизвестных платформах
    :returns:
        Список доступных портов в системе
    """
    if sys.platform.startswith('win'):
        ports = ['COM%s' % (i + 1) for i in range(256)]
    elif sys.platform.startswith('linux') or sys.platform.startswith('cygwin'):
        ports = glob.glob('/dev/tty[A-Za-z]*')
    elif sys.platform.startswith('darwin'):
        ports = glob.glob('/dev/tty.*')
    else:
        raise EnvironmentError('Unsupported platform')

    result = []
    for port in ports:
        with contextlib.suppress(OSError, serial.SerialException):
            s = serial.Serial(port)
            s.close()
            result.append(port)
    return result


def load_json_config(filename: str = 'modules.json') -> dict:
    dir_path = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(dir_path, filename)
    with open(config_path, 'r') as modules_config:
        return json.load(modules_config, object_pairs_hook=collections.OrderedDict)
