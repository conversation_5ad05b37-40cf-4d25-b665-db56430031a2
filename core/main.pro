SOURCES = core.py \
    gui_modules/map/map_widget.py \
    gui_modules/indicators/angle_indicator_widget.py \
    gui_modules/cameras/cameras.py \
    gui_modules/indicators/dial_indicator.py \
    gui_modules/buttons/auth_button.py \
    gui_modules/buttons/drop_action_button.py \
    gui_modules/buttons/drop_drill_error.py \
    gui_modules/buttons/drop_tailing_button.py \
    gui_modules/buttons/dynamic_action_button.py \
    gui_modules/buttons/eliminate_restrictions_button.py \
    gui_modules/buttons/finish_buildup_button.py \
    gui_modules/buttons/finish_stow_button.py \
    gui_modules/buttons/hard_emergency_button.py \
    gui_modules/buttons/photo_button.py \
    gui_modules/buttons/pullup_button.py \
    gui_modules/buttons/reboot_button.py \
    gui_modules/buttons/recalib_air_button.py \
    gui_modules/buttons/reset_shaft_counter_button.py \
    gui_modules/buttons/send_hole_button.py \
    gui_modules/containers/category_container.py \
    gui_modules/controls/custom_slider_wrapper.py \
    gui_modules/controls/discrete_input.py \
    gui_modules/controls/emergency_setter.py \
    gui_modules/controls/joystick_widget_wrapper.py \
    gui_modules/controls/machine_state_management.py \
    gui_modules/controls/nodes_switch.py \
    gui_modules/controls/vehicle_selector_wrapper.py \
    gui_modules/data/core_data_translator.py \
    gui_modules/indicators/circle_widget.py \
    gui_modules/indicators/depth_widget_wrapper.py \
    gui_modules/indicators/distance2_hole_widget.py \
    gui_modules/indicators/gps_widget.py \
    gui_modules/text/conditional_text_widget.py \
    gui_modules/text/messages_widget.py \
    gui_modules/text/moving_data_translator.py \
    gui_modules/text/text_complicated_widget.py \
    gui_modules/text/text_widget.py \
    downtimes.py \
    veh_selector.py \
    ext_translations.tri \
    customClasses/customSliderWidget.py \
    customClasses/vehicleSelector.py \
    drillJoystickWidget/joystick.py
TRANSLATIONS = translations/es_ES.ts translations/ru_RU.ts translations/en_EN.ts