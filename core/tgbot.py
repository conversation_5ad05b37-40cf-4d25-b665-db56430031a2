# from threading import Thread
import telegram
from pythonping import ping
from telegram.ext import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CallbackQueryHandler, MessageHandler, Filters
from telegram import InlineKeyboardMarkup, InlineKeyboardButton
from threading import Thread
import time
import csv
import requests
import pyautogui
from datetime import datetime, timed<PERSON>ta
from playsound import playsound

class TgBotClass(object):
    def __init__(self, core, *args, **kwargs):
        self.core = core
        self.killed = False
        self.ping_killed = False
        self.lash_plan_hash = None
        self.alarm_queue = []
        self.improve_queue = []
        self.last_hole = 0
        self.spam_list = ['182116723', '299268874']
        self.last_hole = 'zero'
        self.tmp_auto = 'zero'


    def echo(self, bot, update):
        if update.message.text == "300":
            bot.send_message(chat_id=update.message.chat_id, text="Как дела у машиниста?")
        else:
            bot.send_message(chat_id=update.message.chat_id, text="Чё каво?",
                             parse_mode=telegram.ParseMode.MARKDOWN)

    def state(self, bot, update, args):
        if '4694' in self.core.telemetry.keys():
            if 'MainStateMachineNode' in self.core.telemetry['4694'].keys():
                a = self.core.telemetry['4694']['MainStateMachineNode']
        else:
            a = 'Ноу коннексьён'
        bot.send_message(chat_id=update.message.chat_id, text=a)

    def ping(self, bot, update, args):
        frame_size = int(args[0]) if len(args) else 1472
        response_list = ping('10.32.46.80', size=frame_size, count=6, df=True)
        if response_list.success():
            txt = 'Min: ' + str(response_list.rtt_min_ms) + '\nMax: ' + str(response_list.rtt_max_ms) + \
                  '\nAvg: ' + str(response_list.rtt_avg_ms)
            if response_list.rtt_avg_ms > 100:
                txt += '\n\nПлохая связь. Сказать тебе, когда станет лучше?\n/alarm_improve'
            bot.send_message(chat_id=update.message.chat_id, text=txt)
        else:
            txt = 'Станок не отвечает\n\nХочешь узнать, когда связь поднимется?\n/alarm_online'
            txt += '\n\nИли подождём прям очень хорошую связь?\n/alarm_improve'
            bot.send_message(chat_id=update.message.chat_id, text=txt)

    # def ping_stat(self, bot, update, args):
    #     try:
    #         f = open('ping_stat.txt', 'r')
    #         content = f.readlines()
    #         txt = ''
    #         availability_hour = float(content[0]) / 720 * 100
    #         availability_day = float(content[1]) / 17280 * 100
    #         txt = 'Станок был в сети:\nЗа час {:.1f}%\nЗа день {:.1f}%'.format(availability_hour, availability_day)
    #         f.close()
    #     except:
    #         txt = 'Не смог найти файл статистики'
    #     bot.send_message(chat_id=update.message.chat_id, text=txt)


    def alarm_online(self, bot, update, args):
        uid = update.message.chat_id
        if not (uid in self.alarm_queue):
            self.alarm_queue.append(uid)
            bot.send_message(chat_id=uid, text='Ок, я тебе скажу, когда станок появится в сети')
        else:
            bot.send_message(chat_id=uid, text='Я про тебя не забыл, не волнуйся')

    def alarm_improve(self, bot, update, args):
        uid = update.message.chat_id
        if not (uid in self.alarm_queue):
            self.improve_queue.append(uid)
            bot.send_message(chat_id=uid, text='Ок, я тебе скажу, когда на станке появится отличная связь')
        else:
            bot.send_message(chat_id=uid, text='Я про тебя не забыл, не волнуйся')

    def start_spam(self, bot, update, args):
        uid = update.message.chat_id
        if not (str(uid) in self.spam_list):
            self.spam_list.append(str(uid))
            print(self.spam_list)
            bot.send_message(chat_id=uid, text='Я буду тебе говорить о каждой новой скважине\n'
                                               'Когда надоест, используй команду /nospam')
        else:
            bot.send_message(chat_id=uid, text='Ты и так уже в спам-списке')

    def stop_spam(self, bot, update, args):
        uid = update.message.chat_id
        if str(uid) in self.spam_list:
            self.spam_list.remove(str(uid))
            print(self.spam_list)
            bot.send_message(chat_id=uid, text='Удолил тебя')
        else:
            bot.send_message(chat_id=uid, text='Я тебе и так не собирался ничего слать')


    def drill_stat(self, bot, update, args):
        uid = update.message.chat_id
        csv_link = 'http://10.32.46.79:5000/api/history/download-csv'
        with requests.Session() as s:
            download = s.get(csv_link)
            if len(args) == 1:
                if args[0] == 'csv':
                    with open('tmp/last_stat.csv', 'wb') as f:
                        f.write(download.content)
                    with open('tmp/last_stat.csv', 'rb') as f:
                        bot.send_document(chat_id=uid, document=f)
                    return
        decoded_content = download.content.decode('utf-8')
        cr = csv.reader(decoded_content.splitlines(), delimiter=',')
        all_holes_list = list(cr)
        deep_sum = 0
        deep_sum_with_bad_stat = 0
        # time_sum = 0
        txt = ''
        txt_det = ''
        holes_num = 0
        filter_day = None
        mode = ''
        if len(args) > 2:
            bot.send_message(chat_id=uid, text='Неправильное количество агрументов. Верный формат: /drill_stat 31-08')
            return
        if len(args):
            if args[0] == 'today':
                filter_day = datetime.today().strftime('%d-%m')
            else:
                import re
                rex = re.compile("^[0-3][0-9]-[0-1][0-9]$")
                if not rex.match(args[0]):
                    bot.send_message(chat_id=uid, text='Ошибка в формате даты. Верный формат: /drill_stat 31-08')
                    return
                else:
                    filter_day = args[0]
            if len(args) == 2:
                try:
                    int(args[1])
                    mode = 'downtimes'
                except ValueError:
                    if args[1] != 'details':
                        bot.send_message(chat_id=uid, text='Ошибка в формате минут простоев. Верный формат: /drill_stat 31-08 300')
                    else:
                        mode = 'details'

        if len(all_holes_list):
            if all_holes_list[0][0] == 'action_seq':  # проверка правильности документа
                time_min = 1900000000
                time_max = 1500000000
                downtimes = 0 if mode in ('details', '') else int(args[1])
                for hole in all_holes_list[1:]:
                    if hole[1] and hole[2]:
                        date_this_hole = datetime.fromtimestamp(int(float(hole[2]))).strftime('%d-%m')
                        if (date_this_hole == filter_day) or (filter_day is None):
                            # cycle_time = float(hole[2]) - float(hole[1])
                            deep_sum += float(hole[11])
                            # time_sum += cycle_time
                            if float(hole[1]) < time_min:
                                time_min = float(hole[1])
                            if float(hole[2]) > time_max:
                                time_max = float(hole[2])
                            holes_num += 1
                    if hole[1]:
                        date_this_hole = datetime.fromtimestamp(int(float(hole[1]))).strftime('%d-%m')
                        if (date_this_hole == filter_day) or (filter_day is None):
                            deep_sum_with_bad_stat += float(hole[11])
                            txt_det += hole[6] + ' || '
                            txt_det += datetime.fromtimestamp(int(float(hole[1]))).strftime('%H:%M - ')
                            if hole[2]: txt_det += datetime.fromtimestamp(int(float(hole[2]))).strftime('%H:%M')
                            else: txt_det += '**:**'
                            txt_det += ' | ' + f'{float(hole[11]):.{1}f}' + '\n'

                if deep_sum > 0:
                    time_sum = float(time_max) - float(time_min)
                    productivity = (time_sum/60 - downtimes) / deep_sum
                    txt += f'Скважин: {holes_num}\n'
                    # txt += f'Метров: {deep_sum:.{0}f}\n'
                    txt += f'Метров: {deep_sum_with_bad_stat:.{0}f}\n'
                    # txt += f'Часов: {time_sum/60/60:.{2}f}\n'
                    txt += f'Часов: {(time_sum/60 - downtimes)/60 + (deep_sum_with_bad_stat - deep_sum) * productivity / 60:.{2}f}\n'
                    if len(args):
                        if mode != 'details':
                            txt += f'Темп: {productivity:.{2}f} минут/метр\n'
                        if len(args) == 2:
                            if args[1] == 'details':
                                txt += '\n' + txt_det
                else:
                    txt = 'Не нашел истории на эту дату'
            else:
                txt = 'Ошибка в файле с историей'
        else:
            txt = 'Не нашел скважины'
        print(txt)
        bot.send_message(chat_id=uid, text=txt)


    def stat(self, bot, update, args):
        uid = update.message.chat_id
        csv_link = 'http://10.32.46.79:5000/api/history/download-csv'
        with requests.Session() as s:
            download = s.get(csv_link)
            if len(args) == 1:
                if args[0] == 'csv':
                    with open('tmp/last_stat.csv', 'wb') as f:
                        f.write(download.content)
                    with open('tmp/last_stat.csv', 'rb') as f:
                        bot.send_document(chat_id=uid, document=f)
                    return
        decoded_content = download.content.decode('utf-8')
        cr = csv.reader(decoded_content.splitlines(), delimiter=',')
        all_holes_list = list(cr)
        deep_sum1 = deep_sum2 =0
        deep_sum_with_bad_stat1 = deep_sum_with_bad_stat2 = 0
        # time_sum = 0
        txt = ''
        txt_det1 = txt_det2 = ''
        holes_num1 = holes_num2 = 0
        day = None
        mode = 'details'
        if len(args) > 2:
            bot.send_message(chat_id=uid, text='Неправильное количество агрументов. Верный формат: /drill_stat 31-08')
            return
        if len(args):
            if args[0] == 'today':
                day = datetime.today()

            else:
                import re
                rex = re.compile("^[0-3][0-9]-[0-1][0-9]$")
                if not rex.match(args[0]):
                    bot.send_message(chat_id=uid, text='Ошибка в формате даты. Верный формат: /drill_stat 31-08')
                    return
                else:
                    day = datetime(datetime.today().year, month=int(args[0].split("-")[1]), day=int(args[0].split("-")[0]))
            filter_start_shift1 = datetime(year=day.year, month=day.month, day=day.day, hour=8)
            filter_end_shift1 = datetime(year=day.year, month=day.month, day=day.day, hour=20)
            filter_start_shift2 = filter_end_shift1
            filter_end_shift2 = filter_start_shift1 + timedelta(days=1)


        if len(all_holes_list):
            if all_holes_list[0][0] == 'action_seq':  # проверка правильности документа
                time_min1 = time_min2 = 1900000000
                time_max1 = time_max2 = 1500000000
                downtimes = 0 if mode in ('details', '') else int(args[1])
                for hole in all_holes_list[1:]:
                    if hole[1]:
                        date_this_hole = datetime.fromtimestamp(int(float(hole[1])))
                        if day is not None:
                            if (date_this_hole > filter_start_shift1) and (date_this_hole < filter_end_shift1):
                                holes_num1 += 1
                                if hole[2]:
                                    deep_sum1 += float(hole[11])
                                    if float(hole[2]) > time_max1:
                                        time_max1 = float(hole[2])
                                if float(hole[1]) < time_min1:
                                    time_min1 = float(hole[1])
                                deep_sum_with_bad_stat1 += float(hole[11])

                                txt_det1 += hole[6] + ' || '
                                txt_det1 += datetime.fromtimestamp(int(float(hole[1]))).strftime('%H:%M - ')
                                if hole[2]: txt_det1 += datetime.fromtimestamp(int(float(hole[2]))).strftime('%H:%M')
                                else: txt_det1 += '**:**'
                                txt_det1 += ' | ' + f'{float(hole[11]):.{1}f}' + '\n'

                            if (date_this_hole > filter_start_shift2) and (date_this_hole < filter_end_shift2):
                                holes_num2 += 1
                                if hole[2]:
                                    deep_sum2 += float(hole[11])
                                    if float(hole[2]) > time_max2:
                                        time_max2 = float(hole[2])
                                if float(hole[1]) < time_min2:
                                    time_min2 = float(hole[1])
                                deep_sum_with_bad_stat2 += float(hole[11])

                                txt_det2 += hole[6] + ' || '
                                txt_det2 += datetime.fromtimestamp(int(float(hole[1]))).strftime('%H:%M - ')
                                if hole[2]: txt_det2 += datetime.fromtimestamp(int(float(hole[2]))).strftime('%H:%M')
                                else: txt_det2 += '**:**'
                                txt_det2 += ' | ' + f'{float(hole[11]):.{1}f}' + '\n'


                if deep_sum1:
                    time_sum = float(time_max1) - float(time_min1)
                    productivity = (time_sum/60 - downtimes) / deep_sum1
                    txt += f'Смена 1\nСкважин: {holes_num1}\n'
                    txt += f'Метров: {deep_sum_with_bad_stat1:.{0}f}\n'
                    txt += f'Часов: {(time_sum/60 - downtimes)/60 + (deep_sum_with_bad_stat1 - deep_sum1) * productivity / 60:.{2}f}\n'
                    if len(args):
                        if mode != 'details':
                            txt += f'Темп: {productivity:.{2}f} минут/метр\n'
                        if len(args) == 2:
                            if args[1] == 'details':
                                txt += '\n' + txt_det2
                if deep_sum2:
                    time_sum = float(time_max2) - float(time_min2)
                    productivity = (time_sum/60 - downtimes) / deep_sum2
                    txt += f'\nСмена 2\nСкважин: {holes_num2}\n'
                    txt += f'Метров: {deep_sum_with_bad_stat2:.{0}f}\n'
                    txt += f'Часов: {(time_sum/60 - downtimes)/60 + (deep_sum_with_bad_stat2 - deep_sum2) * productivity / 60:.{2}f}\n'
                    if len(args):
                        if mode != 'details':
                            txt += f'Темп: {productivity:.{2}f} минут/метр\n'
                        if len(args) == 2:
                            if args[1] == 'details':
                                txt += '\n' + txt_det2
                if not (deep_sum1 or deep_sum2):
                    txt = 'Не нашел истории на эту дату'
            else:
                txt = 'Ошибка в файле с историей'
        else:
            txt = 'Не нашел скважины'
        print(txt)
        bot.send_message(chat_id=uid, text=txt)


    def ping_update(self, bot):
        while not self.ping_killed:
            response_list = ping('10.32.46.80', size=1470, count=4, df=True)
            if response_list.success() and response_list.rtt_avg_ms < 100:
                if len(self.improve_queue) or len(self.alarm_queue):
                    txt = 'Есть годный коннект, меньше 100мс!'
                    txt += '\nMin: ' + str(response_list.rtt_min_ms) + '\nMax: ' + str(response_list.rtt_max_ms) + \
                          '\nAvg: ' + str(response_list.rtt_avg_ms)
                    for chat_id in self.improve_queue:
                        bot.send_message(chat_id=chat_id, text=txt)
                    self.improve_queue = []
                    for chat_id in self.alarm_queue:
                        bot.send_message(chat_id=chat_id, text=txt)
                    self.alarm_queue = []
                # self.update_ping_statistic(1)
            elif response_list.success():
                if len(self.alarm_queue):
                    txt = 'Ура, станок ожил! Пинг > 100 ms.'
                    txt += '\nMin: ' + str(response_list.rtt_min_ms) + '\nMax: ' + str(response_list.rtt_max_ms) + \
                          '\nAvg: ' + str(response_list.rtt_avg_ms)
                    for chat_id in self.alarm_queue:
                        bot.send_message(chat_id=chat_id, text=txt)
                    self.alarm_queue = []
            #     self.update_ping_statistic(1)
            # else:
            #     self.update_ping_statistic(-1)

            # чекаем изменение скважины
            try:
                telemetry_last_hole = self.core.telemetry['4694']['last_holeid']
                if self.last_hole == 'zero':
                    self.last_hole = telemetry_last_hole
                    time.sleep(5)
                    continue
                if telemetry_last_hole is not None and telemetry_last_hole != "None" and self.last_hole != telemetry_last_hole:
                    print(f'New hole! Old:{self.last_hole} New:{telemetry_last_hole}')
                    csv_link = 'http://10.32.46.79:5000/api/history/download-csv'
                    with requests.Session() as s:
                        download = s.get(csv_link)
                    decoded_content = download.content.decode('utf-8')
                    cr = csv.reader(decoded_content.splitlines(), delimiter=',')
                    my_list = list(cr)
                    print('In CSV prelast:', my_list[-2][6])
                    print('In CSV last:', my_list[-1][6])
                    hole_stat = my_list[-1]

                    if not (hole_stat[6] == telemetry_last_hole):
                        hole_stat = my_list[-2]
                        if not hole_stat[6] == telemetry_last_hole:
                            print(f'Hole notification alarm: last and prelast holes in csv != last hole from telemetry\n'
                                  f'Telemetry: {telemetry_last_hole}\n'
                                  f'Last in csv: {my_list[-1]}\nPrelast in csv: {my_list[-2]}\n')
                            return
                    print(f'Sending hole_stat for ID{hole_stat[6]}')
                    if hole_stat[1] and hole_stat[2] and hole_stat[11]:
                        playsound("sounds/hole_finished.wav")
                        cycle_time = float(hole_stat[2]) - float(hole_stat[1])
                        depth = float(hole_stat[11])
                        txt = f'Пробурил скважину {telemetry_last_hole}\n'
                        txt += f'Метров: {depth:.{1}f}\n'
                        txt += f'Минут: {cycle_time/60:.{1}f}\n'
                        txt += f'Темп: {cycle_time/60/depth:.{2}f}\n'
                        for chat_id in self.spam_list:
                            bot.send_message(chat_id=chat_id, text=txt)
                        self.last_hole = telemetry_last_hole
                    else:
                        print(f'Did not found time of depth for ID{hole_stat[6]}')
                        continue
            except KeyError as e:
                pass
            if '4694' in self.core.telemetry.keys():
                if 'MainStateMachineNode' in self.core.telemetry['4694'].keys():
                    a = self.core.telemetry['4694']['MainStateMachineNode']
                    if self.tmp_auto != a:
                        for u in self.spam_list:
                            # bot.send_message(chat_id=u, text=a)
                            self.tmp_auto = a
            time.sleep(5)

    # def update_ping_statistic(self, delta):
    #     try:
    #         f = open('ping_stat.txt', 'r+')
    #         #   проверяем строки  если не 2, то очищаем нулями
    #         content = f.readlines()
    #         if len(content) != 2:
    #             #  файл сломался, очищаем файл, суём нули
    #             f.close()
    #             f = open('ping_stat.txt', 'w')
    #             f.write('0\n0')
    #             new_ping_hour = new_ping_day = 0
    #         else:
    #             ping_hour = int(content[0])
    #             ping_day = int(content[1])
    #             # print('I found', ping_hour, ping_day)
    #
    #             new_ping_hour = ping_hour + delta
    #             if new_ping_hour > 720:
    #                 new_ping_hour = 720
    #             if new_ping_hour < 0:
    #                 new_ping_hour = 0
    #             new_ping_day = ping_day + delta
    #             if new_ping_day > 17280:
    #                 new_ping_day = 17280
    #             if new_ping_day < 0:
    #                 new_ping_day = 0
    #             f.seek(0, 0)
    #             f.write(str(new_ping_hour) + '\n')
    #             f.write(str(new_ping_day))
    #     except OSError as e:
    #         f = open('ping_stat.txt', 'w')
    #         f.write('0\n0')
    #         new_ping_hour = new_ping_day = 0
    #     finally:
    #         f.close()
    #         return (ping_hour, ping_day)

    def pic(self, bot, update, args):
        uid = update.message.chat_id
        import os
        os.remove('ph/scr.png')
        if len(args):
            # ready_screenshot = pyautogui.screenshot('ph/scr.png', region=(args[0],args[1],args[2],args[3]))
            if args[0] == 'map':
                ready_screenshot = pyautogui.screenshot('ph/scr.png', region=(1925, 5, 650, 450))
            if args[0] == 'status':
                ready_screenshot = pyautogui.screenshot('ph/scr.png', region=(7030, 0, 650, 1050))
        else:
            ready_screenshot = pyautogui.screenshot('ph/scr.png')
        self.screenshot_thread = Thread(target=self.wait_for_screenshot, args=(bot, uid, ready_screenshot))
        self.screenshot_thread.start()

    def wait_for_screenshot(self, bot, uid, ready_screenshot):
        while not ready_screenshot:
            time.sleep(1)
        bot.send_photo(chat_id=uid, photo=open('ph/scr.png', 'rb'))

    def prepare(self, *args, **kwargs):
        self.updater = Updater(token="1303828858:AAHOGC_s9zRZjpwug-3cFHahW_2xkYu43ns", use_context=False)



    def start(self):



        try:
            self.dispatcher = self.updater.dispatcher
            self.dispatcher.add_handler(MessageHandler(Filters.text & ~Filters.command, self.echo))
            # self.dispatcher.add_handler(CallbackQueryHandler(react_inline))

            self.dispatcher.add_handler(CommandHandler("state", self.state, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("ping", self.ping, pass_args=True))
            # self.dispatcher.add_handler(CommandHandler("ping_stat", self.ping_stat, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("alarm_online", self.alarm_online, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("alarm_improve", self.alarm_improve, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("drill_stat", self.drill_stat, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("spam", self.start_spam, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("nospam", self.stop_spam, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("pic", self.pic, pass_args=True))
            self.dispatcher.add_handler(CommandHandler("stat", self.stat, pass_args=True))
            self.updater.start_polling()

            self.ping_update_thread = Thread(target=self.ping_update, args=(self.dispatcher.bot,))
            self.ping_update_thread.start()

        except telegram.error.NetworkError:
            print('Проблемы с доступом к серверу telegram.org.')