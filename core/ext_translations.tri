QT_TRANSLATE_NOOP("TextWidget", "Select Vehicle")
QT_TRANSLATE_NOOP("TextWidget", "Connection Mode")
QT_TRANSLATE_NOOP("ControllerPanel", "Driving")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Right")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Left")
QT_TRANSLATE_NOOP("ControllerPanel", "           pull extend\n     Jacks")
QT_TRANSLATE_NOOP("ControllerPanel", "Drilling")
QT_TRANSLATE_NOOP("ControllerPanel", "Arm")
QT_TRANSLATE_NOOP("ControllerPanel", "Feed")
QT_TRANSLATE_NOOP("ControllerPanel", "Rotating")
QT_TRANSLATE_NOOP("ControllerPanel", "Force")
QT_TRANSLATE_NOOP("ControllerPanel", "Compressor")
QT_TRANSLATE_NOOP("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>")
QT_TRANSLATE_NOOP("<PERSON><PERSON><PERSON><PERSON>", "Rear")
QT_TRANSLATE_NOOP("DrillJ<PERSON>stickWidget", "Left")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Right")
QT_TRANSLATE_NOOP("ControllerPanel", "Tower Tilt")
QT_TRANSLATE_NOOP("ControllerPanel", "Tilt")
QT_TRANSLATE_NOOP("ControllerPanel", "Upper Pin")
QT_TRANSLATE_NOOP("ControllerPanel", "Lower Pin")
QT_TRANSLATE_NOOP("ControllerPanel", "  Arm")
QT_TRANSLATE_NOOP("ControllerPanel", "Extention")
QT_TRANSLATE_NOOP("ControllerPanel", "Manipulator")
QT_TRANSLATE_NOOP("ControllerPanel", "Feed")
QT_TRANSLATE_NOOP("ControllerPanel", "Turning")
QT_TRANSLATE_NOOP("ControllerPanel", "Force")
QT_TRANSLATE_NOOP("ControllerPanel", "clamp  offset")
QT_TRANSLATE_NOOP("ControllerPanel", "   Fork")
QT_TRANSLATE_NOOP("ControllerPanel", "Carousel")
QT_TRANSLATE_NOOP("ControllerPanel", "Supply")
QT_TRANSLATE_NOOP("ControllerPanel", "Angle")
QT_TRANSLATE_NOOP("TextWidget", "Remote Control Mode")
QT_TRANSLATE_NOOP("DialIndicator", "Revolutions\n(revs/min) x10")
QT_TRANSLATE_NOOP("DialIndicator", "Air\npressure (bar)")
QT_TRANSLATE_NOOP("DialIndicator", "Air\npressure (psi)")
QT_TRANSLATE_NOOP("DialIndicator", "Rotation\npressure (psi)")
QT_TRANSLATE_NOOP("DialIndicator", "Rotation torque\n(klb*ft)")
QT_TRANSLATE_NOOP("DialIndicator", "Drill Feed\npressure (psi)")
QT_TRANSLATE_NOOP("DialIndicator", "Drill Feed\npressure (klbf)")

QT_TRANSLATE_NOOP("DropActionButton", "Cancel\ntask")
QT_TRANSLATE_NOOP("DropDrillError", "Clear\nerror")
QT_TRANSLATE_NOOP("DropTailingButton", "Clear\ntailing")
QT_TRANSLATE_NOOP("DropTailingButton", "Detected tailing will be cleared")
QT_TRANSLATE_NOOP("FinishHoleButton", "Finish\ndrilling")
QT_TRANSLATE_NOOP("FinishMovingButton", "Finish\nmoving")
QT_TRANSLATE_NOOP("RecalibAirButton", "Recalibrate\nair pressure")
QT_TRANSLATE_NOOP("RecalibAirButton", "Current air pressure will be set as nominal")
QT_TRANSLATE_NOOP("PullupButton", "Force\npullup")
QT_TRANSLATE_NOOP("PhotoButton", "Save\nimages")
QT_TRANSLATE_NOOP("EliminateRestrictions", "Eliminate\nRestrictions")
QT_TRANSLATE_NOOP("EliminateFullRestrictions", "All\nRestrictions\noff")
QT_TRANSLATE_NOOP("RestoreRestrictions", "Restore\nControl\nRestrictions")

QT_TRANSLATE_NOOP("DangerousFeature", "Dangerous feature!")
QT_TRANSLATE_NOOP("DangerousFeatureDescription", "Description!")

QT_TRANSLATE_NOOP("CustomSliderWrapper", "Feed pressure")
QT_TRANSLATE_NOOP("CustomSliderWrapper", "bar")
QT_TRANSLATE_NOOP("CustomSliderWrapper", "Rotation speed")
QT_TRANSLATE_NOOP("CustomSliderWrapper", "rpm")

QT_TRANSLATE_NOOP("SmartButtonGroup", "Cameras mode")
QT_TRANSLATE_NOOP("ButtonInGroup", "Front")
QT_TRANSLATE_NOOP("ButtonInGroup", "Rear")
QT_TRANSLATE_NOOP("ButtonInGroup", "Drilling")
QT_TRANSLATE_NOOP("ButtonInGroup", "Birdview")

QT_TRANSLATE_NOOP("SmartButtonGroup", "Drill mode")
QT_TRANSLATE_NOOP("SmartButtonGroup", "Wet mode")

QT_TRANSLATE_NOOP("ButtonInGroup", "Normal")
QT_TRANSLATE_NOOP("ButtonInGroup", "Cracked")
QT_TRANSLATE_NOOP("ButtonInGroup", "Hard")
QT_TRANSLATE_NOOP("ButtonInGroup", "Flooded")
QT_TRANSLATE_NOOP("ButtonInGroup", "Dry")


QT_TRANSLATE_NOOP("SmartButtonGroup", "Operation mode")
QT_TRANSLATE_NOOP("ButtonInGroup", "Drill")
QT_TRANSLATE_NOOP("ButtonInGroup", "Propel")

QT_TRANSLATE_NOOP("SmartButton", "Water")
QT_TRANSLATE_NOOP("SmartButton", "Lights")

QT_TRANSLATE_NOOP("DrillJoystickWidget", "Driving")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Jacks")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Retract")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Extend")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Right track")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Left track")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Back")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Forth")

QT_TRANSLATE_NOOP("DrillJoystickWidget", "Drilling")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Feed force")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Rotation")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "CCW")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "CW")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Arm")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Open")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Close")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Feed")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Up")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Down")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Compressor")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "On")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Off")

QT_TRANSLATE_NOOP("DrillJoystickWidget", "Leveling")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "All jacks")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Rear")

QT_TRANSLATE_NOOP("DrillJoystickWidget", "Tower")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Inclined Pin")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Tilt")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Raise")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Lower")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Vertical Pin")

QT_TRANSLATE_NOOP("DrillJoystickWidget", "Wrench")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Release")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Turn")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Stow")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Swing in")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Fork")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Stop")

QT_TRANSLATE_NOOP("DrillJoystickWidget", "Carousel")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Index")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Dust flaps")

QT_TRANSLATE_NOOP("DrillJoystickWidget", "Feed")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Up")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Down")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Compressor")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "On")
QT_TRANSLATE_NOOP("DrillJoystickWidget", "Off")


QT_TRANSLATE_NOOP("TextWidget", "Pins")
QT_TRANSLATE_NOOP("TextWidget", "Vertical")
QT_TRANSLATE_NOOP("TextWidget", "Slanted")
QT_TRANSLATE_NOOP("TextWidget", "Arm")
QT_TRANSLATE_NOOP("TextWidget", "Dust flaps")
QT_TRANSLATE_NOOP("TextWidget", "Opened")
QT_TRANSLATE_NOOP("TextWidget", "Closed")
QT_TRANSLATE_NOOP("TextWidget", "Carousel")
QT_TRANSLATE_NOOP("TextWidget", "Extended")
QT_TRANSLATE_NOOP("TextWidget", "Retracted")
QT_TRANSLATE_NOOP("TextWidget", "Index 1")
QT_TRANSLATE_NOOP("TextWidget", "Index 2")
QT_TRANSLATE_NOOP("TextWidget", "Rod in cup 1")
QT_TRANSLATE_NOOP("TextWidget", "Rod in cup 2")
QT_TRANSLATE_NOOP("TextWidget", "Wrench")
QT_TRANSLATE_NOOP("TextWidget", "U-Fork")
QT_TRANSLATE_NOOP("TextWidget", "Stowed")
QT_TRANSLATE_NOOP("TextWidget", "Engaged")
QT_TRANSLATE_NOOP("TextWidget", "Grip open")
QT_TRANSLATE_NOOP("TextWidget", "Grip closed")
QT_TRANSLATE_NOOP("TextWidget", "Removed")
QT_TRANSLATE_NOOP("TextWidget", "Jacks")
QT_TRANSLATE_NOOP("TextWidget", "Tower is in position")
QT_TRANSLATE_NOOP("TextWidget", "Work permission")

QT_TRANSLATE_NOOP("StaticLabel", "Position")
QT_TRANSLATE_NOOP("StaticLabel", "Index")
QT_TRANSLATE_NOOP("StaticLabel", "Dust flaps")
QT_TRANSLATE_NOOP("StaticLabel", "Grip")
QT_TRANSLATE_NOOP("StaticLabel", "Vertical")
QT_TRANSLATE_NOOP("StaticLabel", "Slanted")
QT_TRANSLATE_NOOP("StaticLabel", "U-Fork")
QT_TRANSLATE_NOOP("StaticLabel", "Rod support")
QT_TRANSLATE_NOOP("StaticLabel", "Dust flaps")

QT_TRANSLATE_NOOP("DynamicLabel", "Opened")
QT_TRANSLATE_NOOP("DynamicLabel", "Closed")
QT_TRANSLATE_NOOP("DynamicLabel", "Extended")
QT_TRANSLATE_NOOP("DynamicLabel", "Retracted")
QT_TRANSLATE_NOOP("DynamicLabel", "Index 1")
QT_TRANSLATE_NOOP("DynamicLabel", "Index 2")
QT_TRANSLATE_NOOP("DynamicLabel", "Stowed")
QT_TRANSLATE_NOOP("DynamicLabel", "Engaged")
QT_TRANSLATE_NOOP("DynamicLabel", "Gripped")
QT_TRANSLATE_NOOP("DynamicLabel", "Released")
QT_TRANSLATE_NOOP("DynamicLabel", "Removed")
QT_TRANSLATE_NOOP("DynamicLabel", "Moving")
QT_TRANSLATE_NOOP("DynamicLabel", "No data")
QT_TRANSLATE_NOOP("DynamicLabel", "Locked")


QT_TRANSLATE_NOOP("CategoryContainer", "U-Fork")
QT_TRANSLATE_NOOP("CategoryContainer", "Carousel")
QT_TRANSLATE_NOOP("CategoryContainer", "Pins")
QT_TRANSLATE_NOOP("CategoryContainer", "Arm")
QT_TRANSLATE_NOOP("CategoryContainer", "Wrench")
QT_TRANSLATE_NOOP("CategoryContainer", "Jacks")

QT_TRANSLATE_NOOP("MachineStateManagement", "Main State")
QT_TRANSLATE_NOOP("MachineStateManagement", "Idle")
QT_TRANSLATE_NOOP("MachineStateManagement", "Init")
QT_TRANSLATE_NOOP("MachineStateManagement", "Driving")
QT_TRANSLATE_NOOP("MachineStateManagement", "Leveling")
QT_TRANSLATE_NOOP("MachineStateManagement", "Tower tilt")
QT_TRANSLATE_NOOP("MachineStateManagement", "Drilling")
QT_TRANSLATE_NOOP("MachineStateManagement", "Shaft Build-up")
QT_TRANSLATE_NOOP("MachineStateManagement", "Shaft stow")
QT_TRANSLATE_NOOP("MachineStateManagement", "Grounding")
QT_TRANSLATE_NOOP("MachineStateManagement", "Wait for Remote")
QT_TRANSLATE_NOOP("MachineStateManagement", "Prepare for Remote")
QT_TRANSLATE_NOOP("MachineStateManagement", "In Remote")
QT_TRANSLATE_NOOP("MachineStateManagement", "Finishing Remote")
QT_TRANSLATE_NOOP("MachineStateManagement", "Waiting after leveling")
QT_TRANSLATE_NOOP("MachineStateManagement", "String restoring")
QT_TRANSLATE_NOOP("MachineStateManagement", "Waiting before leveling")
QT_TRANSLATE_NOOP("MachineStateManagement", "Failure")
QT_TRANSLATE_NOOP("MachineStateManagement", "Locking")
QT_TRANSLATE_NOOP("MachineStateManagement", "Unlocking")
QT_TRANSLATE_NOOP("MachineStateManagement", "Arm closing")
QT_TRANSLATE_NOOP("MachineStateManagement", "Tilt Regulation")
QT_TRANSLATE_NOOP("MachineStateManagement", "Touchdown")
QT_TRANSLATE_NOOP("MachineStateManagement", "Unstucking")
QT_TRANSLATE_NOOP("MachineStateManagement", "Lifting up")
QT_TRANSLATE_NOOP("MachineStateManagement", "Unstucking")
QT_TRANSLATE_NOOP("MachineStateManagement", "Overburden pass")
QT_TRANSLATE_NOOP("MachineStateManagement", "Drilling")
QT_TRANSLATE_NOOP("MachineStateManagement", "Hard Rot")
QT_TRANSLATE_NOOP("MachineStateManagement", "Pullup")
QT_TRANSLATE_NOOP("MachineStateManagement", "After Pullup")
QT_TRANSLATE_NOOP("MachineStateManagement", "String Raising")
QT_TRANSLATE_NOOP("MachineStateManagement", "Waiting after drill")
QT_TRANSLATE_NOOP("MachineStateManagement", "Pass soft")
QT_TRANSLATE_NOOP("MachineStateManagement", "Tower")
QT_TRANSLATE_NOOP("MachineStateManagement", "Arm")
QT_TRANSLATE_NOOP("MachineStateManagement", "Opening")
QT_TRANSLATE_NOOP("MachineStateManagement", "Open")
QT_TRANSLATE_NOOP("MachineStateManagement", "Closing")
QT_TRANSLATE_NOOP("MachineStateManagement", "Close")
QT_TRANSLATE_NOOP("MachineStateManagement", "Jacks")
QT_TRANSLATE_NOOP("MachineStateManagement", "Dust flaps")
QT_TRANSLATE_NOOP("MachineStateManagement", "Pulling")
QT_TRANSLATE_NOOP("MachineStateManagement", "Pulled")
QT_TRANSLATE_NOOP("MachineStateManagement", "Restoring pulled")
QT_TRANSLATE_NOOP("MachineStateManagement", "Lowering jacks")
QT_TRANSLATE_NOOP("MachineStateManagement", "Leveling")
QT_TRANSLATE_NOOP("MachineStateManagement", "Final leveling")
QT_TRANSLATE_NOOP("MachineStateManagement", "Holding")
QT_TRANSLATE_NOOP("MachineStateManagement", "Carousel")
QT_TRANSLATE_NOOP("MachineStateManagement", "Carousel opening")
QT_TRANSLATE_NOOP("MachineStateManagement", "Carousel closing")
QT_TRANSLATE_NOOP("MachineStateManagement", "Carousel turning cw")
QT_TRANSLATE_NOOP("MachineStateManagement", "Carousel turning ccw")
QT_TRANSLATE_NOOP("MachineStateManagement", "Planner")
QT_TRANSLATE_NOOP("MachineStateManagement", "Moving")
QT_TRANSLATE_NOOP("MachineStateManagement", "Approach")
QT_TRANSLATE_NOOP("MachineStateManagement", "Computing")
QT_TRANSLATE_NOOP("MachineStateManagement", "Initial Check")
QT_TRANSLATE_NOOP("MachineStateManagement", "Unstuck Down")
QT_TRANSLATE_NOOP("MachineStateManagement", "Unstuck Spin")
QT_TRANSLATE_NOOP("MachineStateManagement", "Unstuck Up")
QT_TRANSLATE_NOOP("MachineStateManagement", "Rod changer")
QT_TRANSLATE_NOOP("MachineStateManagement", "Aligning rod for cup")
QT_TRANSLATE_NOOP("MachineStateManagement", "Aligning rod for fork")
QT_TRANSLATE_NOOP("MachineStateManagement", "Approaching rod for carousel")
QT_TRANSLATE_NOOP("MachineStateManagement", "Approaching rod for fork")
QT_TRANSLATE_NOOP("MachineStateManagement", "Apply wrench")
QT_TRANSLATE_NOOP("MachineStateManagement", "Brakeout")
QT_TRANSLATE_NOOP("MachineStateManagement", "Closing arm")
QT_TRANSLATE_NOOP("MachineStateManagement", "Closing carousel")
QT_TRANSLATE_NOOP("MachineStateManagement", "Closing wrench")
QT_TRANSLATE_NOOP("MachineStateManagement", "Detaching rod")
QT_TRANSLATE_NOOP("MachineStateManagement", "Detaching rod in cup")
QT_TRANSLATE_NOOP("MachineStateManagement", "Lift head to pull depth")
QT_TRANSLATE_NOOP("MachineStateManagement", "Finish")
QT_TRANSLATE_NOOP("MachineStateManagement", "Inserting rod in cup")
QT_TRANSLATE_NOOP("MachineStateManagement", "Lifting head to carousel")
QT_TRANSLATE_NOOP("MachineStateManagement", "Closing fork")
QT_TRANSLATE_NOOP("MachineStateManagement", "Screwing new rod")
QT_TRANSLATE_NOOP("MachineStateManagement", "Opening arm")
QT_TRANSLATE_NOOP("MachineStateManagement", "Opening carousel")
QT_TRANSLATE_NOOP("MachineStateManagement", "Opening fork and arm")
QT_TRANSLATE_NOOP("MachineStateManagement", "Opening wrench")
QT_TRANSLATE_NOOP("MachineStateManagement", "Pull out")
QT_TRANSLATE_NOOP("MachineStateManagement", "Screwing")
QT_TRANSLATE_NOOP("MachineStateManagement", "Turn shaft ccw")
QT_TRANSLATE_NOOP("MachineStateManagement", "Turn shaft cw")
QT_TRANSLATE_NOOP("MachineStateManagement", "Turn shaft cw in carousel cup")
QT_TRANSLATE_NOOP("MachineStateManagement", "Opening fork")
QT_TRANSLATE_NOOP("MachineStateManagement", "Fork")
QT_TRANSLATE_NOOP("MachineStateManagement", "Missed rod")
QT_TRANSLATE_NOOP("MachineStateManagement", "Turning CW")
QT_TRANSLATE_NOOP("MachineStateManagement", "Turning CCW")
QT_TRANSLATE_NOOP("MachineStateManagement", "Turning wrench")
QT_TRANSLATE_NOOP("MachineStateManagement", "Releasing wrench")
QT_TRANSLATE_NOOP("MachineStateManagement", "Wrench")
QT_TRANSLATE_NOOP("MachineStateManagement", "Wrench is open")
QT_TRANSLATE_NOOP("MachineStateManagement", "Wrench is closed")
QT_TRANSLATE_NOOP("MachineStateManagement", "Init Check")

QT_TRANSLATE_NOOP("MovingDataTranslator", "Spindle depth: ")
QT_TRANSLATE_NOOP("MovingDataTranslator", "m")
QT_TRANSLATE_NOOP("MovingDataTranslator", "Drill feed speed: ")
QT_TRANSLATE_NOOP("MovingDataTranslator", "m/h")
QT_TRANSLATE_NOOP("ConditionalTextWidget", "Maneuver in process")

QT_TRANSLATE_NOOP("MessageLogs", "100000101")
QT_TRANSLATE_NOOP("MessageLogs", "fucking_code_123")
# Names for all smart-buttons and smart-checkboxes
QT_TRANSLATE_NOOP("SmartButton", "test Button Core")


QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "ALREADY_DONE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "WAIT_FINISH")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "LOST_HAL_CONN")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "CAN_NOT_SWITCH_TO_RC")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "DEPTH_CORRECTION_FAILED")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "UNEVEN_TERRAIN")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_ROTATE_CAROUSEL_IDX1")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_ROTATE_CAROUSEL_IDX2")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_CLOSE_CAROUSEL")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_OPEN_CAROUSEL")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_OPEN_ARM")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_CLOSE_ARM")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_OPEN_DUST_FLAPS")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_CLOSE_DUST_FLAPS")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_MOVE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_UNSTUCK")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_LEVELING")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_LEVEL_TOO_LOW")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_LIFT_STRING")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_STRING_TOO_HIGH")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_RODE_CHANGE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_LOCK_TOWER")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_UNLOCK_TOWER")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RC_TOWER_TILT")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "COMPRESSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "LEVEL_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "WRENCH_SWITCH_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "LASER_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "IBOX_NO_CONN")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "IPLACE_NO_CONN")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "LEFT_JACK_LIMIT_SWITCH_FAILED")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RIGHT_JACK_LIMIT_SWITCH_FAILED")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "REAR_JACK_LIMIT_SWITCH_FAILED")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "CLOSING_ARM_PULLING_JACKS")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "WRONG_ARM_STATE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "HEAD_TOO_LOW")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RESTORING_PULLED_JACKS")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "NO_RTK")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "ANGLES_ABOVE_LIMITS")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RESTORED_STRING")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "STARTING_NEW_ACT")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "READY_FOR_NEW_ACT")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "CAN_NOT_DRILL_CUR_HOLE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "CAN_NOT_ACCEPT_ACTION")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "COLLISION_PREVENTER_STOPPAGE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "NO_FRONT_LIDAR")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "NO_REAR_LIDAR")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "SLOW_MAST_TILT_MSG")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "FORBID_MOVING_ROD_MSG")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "FORBID_MOVING_JACKS_MSG")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "FORBID_MOVING_TRACKS_MSG")

QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "SENSOR_TIMEOUT")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "SENSOR_OOR")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "SENSOR_STALL")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "ROLL_CRITICAL")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "PITCH_CRITICAL")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "WRENCH_SENSOR1_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "WRENCH_SENSOR2_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "WRENCH_SENSOR3_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RPM_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "FORK_LINEAR_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "LJACK_LINEAR_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RJACK_LINEAR_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RLJACK_LINEAR_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "RRJACK_LINEAR_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "ROT_PRESS_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "FEED_PRESS_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "AIR_PRESS_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "CAROUSEL_LINEAR_SENSOR_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "ARM_LINEAR_SENSOR1_FAILURE")
QT_TRANSLATE_NOOP("MessagesWidgetMaximized", "ARM_LINEAR_SENSOR2_FAILURE")

QT_TRANSLATE_NOOP("Task", "drilling")
QT_TRANSLATE_NOOP("Task", "idle")
QT_TRANSLATE_NOOP("Task", "failure")
QT_TRANSLATE_NOOP("Task", "moving")
QT_TRANSLATE_NOOP("Task", "leveling")
QT_TRANSLATE_NOOP("Task", "tower_tilt")
QT_TRANSLATE_NOOP("Task", "shaft_buildup")
QT_TRANSLATE_NOOP("Task", "shaft_stow")
QT_TRANSLATE_NOOP("Task", "grounding")
QT_TRANSLATE_NOOP("Task", "remote_wait")
QT_TRANSLATE_NOOP("Task", "remote")
QT_TRANSLATE_NOOP("Task", "remote_prepare")
QT_TRANSLATE_NOOP("Task", "end_remote")
QT_TRANSLATE_NOOP("Task", "wait_after_level")
QT_TRANSLATE_NOOP("Task", "restore_string")
QT_TRANSLATE_NOOP("Task", "wait_before_level")
QT_TRANSLATE_NOOP("Task", "init_check")

QT_TRANSLATE_NOOP("MovingDataTranslator", "Engine")
QT_TRANSLATE_NOOP("MovingDataTranslator", "Battery")
QT_TRANSLATE_NOOP("MovingDataTranslator", "Coolant temp")
QT_TRANSLATE_NOOP("MovingDataTranslator", "Water level")
QT_TRANSLATE_NOOP("MovingDataTranslator", "Fuel level")
QT_TRANSLATE_NOOP("Distance2HoleWidget", "Hole distance ")

QT_TRANSLATE_NOOP("DowntimesTypes", 'drill_maintenance')
QT_TRANSLATE_NOOP("DowntimesTypes", 'drill_maintenance_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'no_operator')
QT_TRANSLATE_NOOP("DowntimesTypes", 'no_operator_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'no_task')
QT_TRANSLATE_NOOP("DowntimesTypes", 'no_task_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'robot_malfunction')
QT_TRANSLATE_NOOP("DowntimesTypes", 'robot_malfunction_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'software_update')
QT_TRANSLATE_NOOP("DowntimesTypes", 'software_update_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'area_not_ready')
QT_TRANSLATE_NOOP("DowntimesTypes", 'area_not_ready_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'refueling')
QT_TRANSLATE_NOOP("DowntimesTypes", 'refueling_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'robot_maintenance')
QT_TRANSLATE_NOOP("DowntimesTypes", 'robot_maintenance_description')
QT_TRANSLATE_NOOP("DowntimesTypes", 'manual_operation')
QT_TRANSLATE_NOOP("DowntimesTypes", 'manual_operation_description')

QT_TRANSLATE_NOOP("ResetShaftCounterButton", "WARNING: Resetting the rod counter while the rod is in the ground can cause severe damage to the rod and the drilling rig!")
QT_TRANSLATE_NOOP("ResetShaftCounterButton", "Are you sure you want to reset the rod counter?")
QT_TRANSLATE_NOOP("ResetShaftCounterButton", "Reset Rod Counter")
