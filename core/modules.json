{"communicator": {"is_active": true, "file_name": "core", "class_name": "Communicator", "init_args": {}, "prep_args": {}}, "joystick_adapter": {"is_active": true, "file_name": "joystick_adapter", "class_name": "JoystickAdapter", "init_args": {}, "prep_args": {"port": ["/dev/ttyACM0", "/dev/ttyACM1", "/dev/ttyACM2", "/dev/ttyACM3", "/dev/ttyACM4"], "baudrate": 115200, "pkg_size": 49, "pkg_timeout": 0.5, "udp_ip": "0.0.0.0", "udp_port": 2000, "joystick_power_coef": 1, "channel_thresholds": {"joystick1": [0, 1.44, 2.95], "joystick2": [0, 1.46, 2.96], "joystick3": [0.05, 1.51, 2.95], "pot_value": [0, 0, 2.95]}, "switches_state_map": {"joystick1_button": {"1": false, "0": true}, "joystick2_button": {"1": false, "0": true}, "joystick3_button": {"1": false, "0": true}, "button_pair": {"1": 1, "11": 0, "10": -1, "0": 0}, "switch": {"1": -1, "11": 0, "10": 1, "0": 0}}, "channel_name_map": {"switch_value": "switch", "joystick1": "joystick1", "joystick2": "joystick2", "joystick3": "joystick3", "button_pair": "button_pair", "pot_value": "pot_value"}, "channel_to_index_map": {"joystick1_button": 6, "joystick2_button": 8, "joystick3_button": 11, "button_pair": [5, 7], "switch": [9, 10], "joystick1": [19, 20, 21, 22], "joystick2": [23, 24, 25, 26], "joystick3": [27, 28, 29, 30], "pot_value": [31, 32, 33, 34]}, "no_lock_modes": ["driving", "leveling"], "layouts": {"driving": {"cat": {"left_track": "-joystick1", "right_track": "-joystick3"}, "leveler": {"rear_jack": "-button_pair", "right_jack": "-button_pair", "left_jack": "-button_pair"}}, "drilling": {"drill": {"rotation_speed": "joystick1", "feed_speed": "joystick3", "feed_pressure": "pot_value", "rotation_torque": 1.0}, "compressor": {"compressor_on": "switch", "compressor_power": 1.0}, "arm": {"move_arm": "joystick2"}, "dust_flaps": {"move_df": "-button_pair"}}, "leveling": {"leveler": {"rear_jack": "joystick2", "right_jack": "joystick3", "left_jack": "joystick1"}}, "tower_control": {"tower": {"inclined_pin": "-joystick1@0.5", "vertical_pin": "-joystick3@0.5", "tower_tilt": "-joystick2"}, "arm": {"move_arm": "button_pair"}}, "buildup": {"drill": {"rotation_speed": "joystick1", "feed_pressure": "pot_value", "feed_speed": "joystick3", "rotation_torque": 1.0}, "wrench": {"move_wrench": "joystick2", "grip_wrench": "-button_pair"}, "fork": {"move_fork": "-switch"}}, "carousel_control": {"carousel": {"move_carousel": "joystick2", "rotate_carousel": "button_pair"}, "drill": {"rotation_speed": "joystick1", "feed_pressure": "pot_value", "feed_speed": "joystick3", "rotation_torque": 1.0}, "arm": {"move_arm": "-switch"}}}}}, "plan_reader": {"is_active": true, "file_name": "plan_reader", "class_name": "PlanReader", "init_args": {}, "prep_args": {}}, "core_data_translator": {"is_active": false, "file_name": "gui_modules", "class_name": "CoreDataTranslator", "init_args": {}, "prep_args": {}}, "map": {"is_active": true, "file_name": "gui_modules", "class_name": "Map", "init_args": {}, "prep_args": {"width": 635, "height": 440, "x": 10, "y": 10, "screen_num": 1}}, "front_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 1570, "height": 1080, "x": 175, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 2, "vflip": true, "hflip": true}}}, "prep_args": {"cam_id": "6"}}, "birdview_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"birdview": {"width": 1570, "height": 1080, "x": 175, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 180, "screen": 2, "vflip": true, "hflip": true}}}, "prep_args": {"cam_id": "1"}}, "rear_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"rear": {"width": 1570, "height": 1080, "x": 175, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 180, "screen": 2}}}, "prep_args": {"cam_id": "5"}}, "right_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 1439, "height": 1080, "x": 1745, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "vflip": true, "rotation": 0, "screen": 2}, "birdview": {"width": 1439, "height": 1080, "x": 1745, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "vflip": true, "rotation": 0, "screen": 2}, "rear": {"width": 1439, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 1, "vflip": true}, "drill": {"width": 1264, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.1, 0.0, 0.1], "rotation": 0, "screen": 1, "vflip": true, "hflip": true}}}, "prep_args": {"cam_id": "3"}}, "drill_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"drill": {"width": 810, "height": 1080, "x": 300, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 90, "screen": 2}}}, "prep_args": {"cam_id": "2"}}, "tower_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"drill": {"width": 810, "height": 1080, "x": 1110, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 90, "screen": 2}}}, "prep_args": {"cam_id": "7"}}, "left_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 1439, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 1, "vflip": true}, "birdview": {"width": 1439, "height": 1080, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 1, "vflip": true}, "rear": {"width": 1439, "height": 1080, "x": 1745, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 2, "vflip": true}, "drill": {"width": 1264, "height": 1080, "x": 0, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 180, "screen": 3}}}, "prep_args": {"cam_id": "4"}}, "dial_indicator_rot_speed": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 240, "height": 240, "x": 30, "y": 102, "field_to_read": "drill_rotation_speed", "initial_value": 25, "ticks_font_size": 15, "scale_range": 14, "scale_step": 2, "caption": "Revolutions\n(revs/min) x10", "scale_gradient": {"green": 0.34, "yellow": 0.3, "red": 0.15}, "caption_font_size": 10, "coeff": 0.1, "digit_value_coeff": 10, "modes_visible": ["drill"], "screen_num": 2}}, "dial_indicator_air_pressure": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 240, "height": 240, "x": 30, "y": 345, "field_to_read": "air_pressure", "initial_value": 150, "ticks_font_size": 15, "caption_font_size": 10, "scale_range": 150, "scale_step": 25, "caption": "Air\npressure (psi)", "scale_gradient": {"green": 0.7, "yellow": 0.63, "red": 0.4}, "coeff": 14.504, "modes_visible": ["drill"], "screen_num": 2}}, "dial_indicator_rot_pressure": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 240, "height": 240, "x": 30, "y": 590, "field_to_read": "drill_rotation_pressure", "initial_value": 7500, "ticks_font_size": 15, "scale_range": 12, "scale_step": 2, "caption": "Rotation torque\n(klb*ft)", "caption_font_size": 10, "scale_gradient": {"green": 0.6, "yellow": 0.42, "red": 0.25}, "coeff": 0.031, "coeff2": 14.503, "units2": "psi", "modes_visible": ["drill"], "screen_num": 2}}, "dial_indicator_feed_pressure": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 240, "height": 240, "x": 30, "y": 835, "field_to_read": "drill_feed_pressure", "initial_value": 7500, "ticks_font_size": 15, "caption_font_size": 10, "scale_range": 80, "scale_step": 10, "caption": "Drill Feed\npressure (klbf)", "scale_gradient": {"green": 0.65, "yellow": 0.5, "red": 0.25}, "coeff": 0.22, "coeff2": 14.503, "units2": "psi", "modes_visible": ["drill"], "screen_num": 2}}, "angle_indicator": {"is_active": true, "file_name": "gui_modules", "class_name": "AngleIndicator", "init_args": {}, "prep_args": {"width": 300, "height": 300, "x": 30, "y": 480, "label_font_size": 12, "screen_num": 1}}, "rear_jack_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 75, "x": 105, "y": 789, "screen": 1, "field_to_read": "rear_jack_pulled", "second_field_to_read": "rear_jack_on_ground", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "right_jack_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 75, "x": 50, "y": 887, "screen": 1, "field_to_read": "right_jack_pulled", "second_field_to_read": "right_jack_on_ground", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "left_jack_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 75, "x": 160, "y": 887, "screen": 1, "field_to_read": "left_jack_pulled", "second_field_to_read": "left_jack_on_ground", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "carousel_position_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 575, "screen": 1, "static_label": "Position", "field_to_read": "carousel_open", "second_field_to_read": "carousel_closed", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Open", "off_text": "Closed", "intermediate_text": "Moving", "disabled_text": "No data"}}, "carousel_index_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 605, "screen": 1, "static_label": "Index", "field_to_read": "carousel_index_1", "second_field_to_read": "carousel_index_2", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Index 1", "off_text": "Index 2", "intermediate_text": "Moving", "disabled_text": "No data"}}, "fork_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 905, "screen": 1, "static_label": "U-Fork", "field_to_read": "fork_extended", "second_field_to_read": "fork_retracted", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Extended", "off_text": "Retracted", "intermediate_text": "Moving", "disabled_text": "No data"}}, "arm_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 945, "screen": 1, "static_label": "Rod support", "field_to_read": "arm_open", "second_field_to_read": "arm_grip", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Opened", "off_text": "Closed", "intermediate_text": "Moving", "disabled_text": "No data"}}, "wrench_position_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 690, "screen": 1, "static_label": "Position", "field_to_read": "wrench_engaged", "second_field_to_read": "wrench_stowed", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Engaged", "off_text": "Stowed", "intermediate_text": "Moving", "disabled_text": "No data"}}, "wrench_grip_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 720, "screen": 1, "static_label": "<PERSON><PERSON>", "field_to_read": "wrench_grip", "second_field_to_read": "wrench_open", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Gripped", "off_text": "Released", "intermediate_text": "Moving", "disabled_text": "No data"}}, "vert_pin_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 805, "screen": 1, "static_label": "Vertical", "field_to_read": "vert_pin_lock", "second_field_to_read": "vert_pin_release", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Released", "off_text": "Locked", "intermediate_text": "Moving", "disabled_text": "No data"}}, "incl_pin_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 835, "screen": 1, "static_label": "Slanted", "field_to_read": "incl_pin_lock", "second_field_to_read": "incl_pin_release", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Released", "off_text": "Locked", "intermediate_text": "Moving", "disabled_text": "No data"}}, "dust_flaps_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 25, "x": 475, "y": 985, "screen": 1, "static_label": "Dust flaps", "field_to_read": "dust_flaps_closed", "second_field_to_read": "dust_flaps_open", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Closed", "off_text": "Opened", "intermediate_text": "Moving", "disabled_text": "No data"}}, "carousel_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 270, "height": 105, "x": 365, "y": 550, "border_color": "#444444", "border_width": 2, "border_radius": 10, "screen": 1, "is_label": true, "label_text": "Carousel", "label_font_size": "20px", "label_font_color": "#FFFFFF"}}, "wrench_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 270, "height": 105, "x": 365, "y": 665, "border_color": "#444444", "border_width": 2, "border_radius": 10, "screen": 1, "is_label": true, "label_text": "<PERSON><PERSON>", "label_font_size": "20px", "label_font_color": "#FFFFFF"}}, "common_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 270, "height": 130, "x": 365, "y": 905, "border_color": "#444444", "border_width": 2, "border_radius": 10, "screen": 1}}, "pins_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 270, "height": 105, "x": 366, "y": 780, "border_color": "#444444", "border_width": 2, "border_radius": 10, "screen": 1, "is_label": true, "label_text": "Pins", "label_font_color": "#FFFFFF", "label_font_size": "20px"}}, "jacks_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 260, "height": 260, "x": 50, "y": 775, "border_color": "#444444", "border_width": 2, "border_radius": 10, "screen": 1, "is_label": true, "label_text": "<PERSON><PERSON>", "label_font_color": "#FFFFFF", "label_font_size": "28px"}}, "permission_indicator": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 30, "x": 565, "y": 468, "screen": 1, "field_to_read": "move_permission", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "gps_indicator": {"is_active": true, "file_name": "gui_modules", "class_name": "GPSWidget", "init_args": {}, "prep_args": {"r": 40, "x": 612, "y": 3, "screen": 1}}, "hole_dist_indicator": {"is_active": false, "file_name": "gui_modules", "class_name": "Distance2HoleWidget", "init_args": {"text": "Hole distance "}, "prep_args": {"width": 635, "height": 40, "x": 10, "y": 415, "font_size": "23px", "screen_num": 1}}, "jacks_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "<PERSON><PERSON>", "x": 145, "y": 1000, "font_color": "#FFFFFF", "screen": 1, "font_size": "30px"}}, "tower_ok_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 30, "x": 35, "y": 720, "screen": 1, "field_to_read": "tower_ok_to_lock", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "tower_ok_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Tower is in position", "font_size": "19px", "x": 50, "y": 780, "font_color": "#FFFFFF", "screen": 1, "left": true}}, "permission_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Work permission", "x": 365, "y": 485, "font_color": "#FFFFFF", "screen": 1, "font_size": "20px"}}, "machine_state_management": {"is_active": true, "file_name": "gui_modules", "class_name": "MachineStateManagement", "init_args": {}, "prep_args": {"state_machine": {"MainStateMachineNode": {"Main State": {"idle": "Idle", "moving": "Driving", "leveling": "Leveling", "tower_tilt": "Tower tilt", "drilling": "Drilling", "shaft_buildup": "Shaft Build-up", "shaft_stow": "Shaft stow", "grounding": "Grounding", "remote_wait": "Wait for Remote", "remote_prepare": "Prepare for Remote", "remote": "In Remote", "end_remote": "Finishing Remote", "wait_after_level": "Waiting after leveling", "restore_string": "String restoring", "wait_before_level": "Waiting before leveling", "failure": "Failure", "init_check": "Initial Check"}}, "DrillerNode": {"Drilling": {"idle": "Idle", "touchdown": "Touchdown", "overburden_pass": "Overburden pass", "drilling": "Drilling", "hard_rot": "Hard Rot", "pullup": "<PERSON><PERSON><PERSON>", "after_pullup": "After Pullup", "raise": "String Raising", "failure": "Failure", "wait_after_drill": "Waiting after drill", "pass_soft": "Pass soft", "unstuck_down": "Unstuck Down", "unstuck_spin": "Unstuck Spin", "unstuck_up": "Unstuck Up", "unstuck": "Unstuck"}}, "ArmControllerNode": {"Arm": {"opening": "Opening", "open": "Open", "closing": "Closing", "closed": "Close", "failure": "Failure"}}, "RodChangerNode": {"Rod changer": {"idle": "Idle", "align_cup": "Aligning rod for cup", "align_fork": "Aligning rod for fork", "approach_carousel": "Approaching rod for carousel", "approach_fork": "Approaching rod for fork", "apply_wrench": "Apply wrench", "brakeout": "Brak<PERSON><PERSON>", "close_arm": "Closing arm", "close_carousel": "Closing carousel", "close_wrench": "Closing wrench", "detach": "Detaching rod", "detach_cup": "Detaching rod in cup", "final_position": "Lift head to pull depth", "finish": "Finish", "insert": "Inserting rod in cup", "lift_to_carousel": "Lifting head to carousel", "lock": "Closing fork", "new_rod_screwing": "Screwing new rod", "open_arm": "Opening arm", "open_carousel": "Opening carousel", "open_forkarm": "Opening fork and arm", "open_wrench": "Opening wrench", "pull_out": "Pull out", "screwing": "Screwing", "turn_ccw": "Turn shaft ccw", "turn_cw": "Turn shaft cw", "turn_cw_cup": "Turn shaft cw in carousel cup", "unlock": "Opening fork", "failure": "Failure"}}, "ForkControlNode": {"Fork": {"opening": "Opening", "open": "Open", "closing": "Closing", "closed": "Close", "failure": "Failure", "missed": "Missed rod"}}, "CarouselControllerNode": {"Carousel": {"idle": "Idle", "turn_cw": "Turning CW", "turn_ccw": "Turning CCW", "closing": "Closing", "opening": "Opening", "failure": "Failure"}}, "WrenchControllerNode": {"Wrench": {"turn": "Turning", "release": "Releasing", "closing": "Closing", "opening": "Opening", "open": "Open", "closed": "Closed", "failure": "Failure"}}, "DustFlapsControllerNode": {"Dust flaps": {"opening": "Opening", "open": "Open", "closing": "Closing", "closed": "Close", "failure": "Failure"}}, "LevelerNode": {"Jacks": {"init": "Init", "touchdown": "Lowering jacks", "pulling": "Pulling", "pulled": "Pulled", "restore_pulled": "Restoring pulled", "leveling": "Leveling", "final_leveling": "Final leveling", "lift": "Lifting up", "holding": "Holding", "failure": "Failure"}}, "PlannerNode": {"Planner": {"init": "Init", "idle": "Idle", "moving": "Moving", "approach": "Approach", "computing": "Computing", "failure": "Failure"}}}, "widget_params": {"x": 5120, "y": 300, "width": 400, "height": 250, "color": "#D2D6E4", "font_size": "18px", "text_transform": "none", "font_weight": "normal", "screen_num": 3}, "confirm_params": {"color": "#fff", "background": "#4E5754", "background_ok": "#009B76", "background_no": "#E32636", "text_transform": "uppercase"}}}, "drill_speed_display": {"is_active": true, "file_name": "gui_modules", "class_name": "MovingDataTranslator", "init_args": {}, "prep_args": {"x": 2, "y": 5, "width": 400, "height": 100, "screen_num": 2, "telemetry": {"drill_feed_speed": {"name": "Drill feed speed", "units": "m/min", "scale": 60, "smooth_k": 0.02}}, "lbl_name_params": {"color": "#D2D6E4", "font_size": "26px", "text_transform": "none", "font_weight": "normal"}, "lbl_value_params": {"color": "#D2D6E4", "font_size": "28px", "text_transform": "none", "font_weight": "bold"}, "mode_visible": ["drill"], "value_on_new_line": true}}, "telemetry_display": {"is_active": true, "file_name": "gui_modules", "class_name": "MovingDataTranslator", "init_args": {}, "prep_args": {"x": 1280, "y": 50, "width": 505, "height": 230, "screen_num": 3, "telemetry": {"engine_speed": {"name": "Engine", "units": "RPM", "scale": 1, "smooth_k": 1, "red_less": 1790}, "battery_potential": {"name": "Battery", "units": "V", "scale": 1, "smooth_k": 1, "red_less": 24}, "coolant_temperature": {"name": "Coolant temp", "units": "°", "scale": 1, "smooth_k": 1, "red_more": 90}, "water_level": {"name": "Water level", "units": "%", "scale": 100, "smooth_k": 1, "red_less": 0.2}, "fuel_level": {"name": "Fuel level", "units": "%", "scale": 100, "smooth_k": 1, "red_less": 0.2}}, "lbl_name_params": {"color": "#D2D6E4", "font_size": "26px", "text_transform": "none", "font_weight": "normal"}, "lbl_value_params": {"color": "#D2D6E4", "font_size": "28px", "text_transform": "none", "font_weight": "bold"}, "mode_visible": ["ALL"], "value_on_new_line": false, "no_fraction": true}}, "emg_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "HardEmergencyButton", "init_args": {}, "prep_args": {"x": 75, "y": 680, "width": 300, "height": 200, "btn_width": 300, "btn_height": 137, "btn_red_fontsize": "25px", "btn_green_fontsize": "24px", "screen_num": 0}}, "reboot_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "RebootButton", "init_args": {}, "prep_args": {"x": 50, "y": 920, "width": 280, "height": 100, "btn_width": 280, "btn_height": 80, "btn_fontsize": "20px", "screen_num": 0}}, "finish_buildup_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "FinishBuildupButton", "init_args": {}, "prep_args": {"x": 1725, "y": 65, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "20px", "screen_num": 0}}, "finish_stow_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "FinishStowButton", "init_args": {}, "prep_args": {"x": 1725, "y": 170, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "20px", "screen_num": 0}}, "tgbot_module": {"is_active": false, "file_name": "tgbot", "class_name": "TgBotClass", "init_args": {}, "prep_args": {}}, "move_permission_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "move_permission", "sound_file": "sounds/move_perm_beep.wav", "from_telemetry": true, "inverse": false}}, "disconnect_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "veh_active_states", "sound_file": "sounds/disconnect_wookie.wav", "from_telemetry": false, "inverse": false}}, "connect_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "veh_active_states", "sound_file": "sounds/buttondigital.mp3", "from_telemetry": false, "inverse": true}}, "maneuver_processing_text": {"is_active": true, "file_name": "gui_modules", "class_name": "ConditionalTextWidget", "init_args": {}, "prep_args": {"text": "Maneuver in process", "font_size": "19px", "x": 1280, "y": 550, "font_color": "#00FF00", "screen": 3, "left": true, "field_to_read": "maneuver_processing", "from_telemetry": true}}, "feed_press_slider": {"is_active": true, "file_name": "gui_modules", "class_name": "CustomSliderWrapper", "init_args": {}, "prep_args": {"width": 795, "height": 155, "x": 405, "y": 515, "caption": "Feed pressure", "param_name": "/DrillerNode/user_feed_pressure", "minValue": 9.5, "maxValue": 60, "units": "klbf", "unit_scale": 0.22}}, "rot_speed_slider": {"is_active": true, "file_name": "gui_modules", "class_name": "CustomSliderWrapper", "init_args": {}, "prep_args": {"width": 795, "height": 155, "x": 405, "y": 670, "caption": "Rotation speed", "param_name": "/DrillerNode/user_rotation_speed", "minValue": 20, "maxValue": 110, "units": "rpm"}}, "test_depth_meter": {"is_active": true, "file_name": "gui_modules", "class_name": "DepthWidgetWrapper", "init_args": {}, "prep_args": {"width": 180, "height": 585, "x": 1700, "y": 10, "screen_num": 3, "dictToRead": "telemetry", "actualDepthField": "max_wellhead_drilling_depth", "targetDepthField": "target_wellhead_depth", "drillPosField": "wellhead_drilling_depth"}}, "Drill_joystick_controller": {"is_active": true, "file_name": "gui_modules", "class_name": "JoystickWidgetWrapper", "init_args": {}, "prep_args": {"width": 1200, "height": 500, "x": 400, "y": 0, "modes": {"driving": {"name": "Driving", "joy_mid": " ", "joy_mid_top": "", "joy_mid_bottom": "", "joy_left": "Right track", "joy_left_top": "Forth", "joy_left_bottom": "Back", "joy_right": "Left track", "joy_right_top": "Forth", "joy_right_bottom": "Back", "knob": " ", "buttons": "<PERSON><PERSON>", "tumbler": " ", "tumbler_top": "", "tumbler_mid": "", "tumbler_bottom": "", "green_button_name": "Retract", "red_button_name": "Extend"}, "drilling": {"name": "Drilling", "joy_mid": "Arm", "joy_mid_top": "Open", "joy_mid_bottom": "Close", "joy_left": "Rotation", "joy_left_top": "CCW", "joy_left_bottom": "CW", "joy_right": "Feed", "joy_right_top": "Up", "joy_right_bottom": "Down", "knob": "Feed force", "buttons": "Dust flaps", "tumbler": "Compressor", "tumbler_top": "On", "tumbler_mid": "Off", "tumbler_bottom": "Off", "green_button_name": "Open", "red_button_name": "Close"}, "leveling": {"name": "Leveling", "joy_mid": "Rear", "joy_mid_top": "Retract", "joy_mid_bottom": "Extend", "joy_left": "Right", "joy_left_top": "Retract", "joy_left_bottom": "Extend", "joy_right": "Left", "joy_right_top": "Retract", "joy_right_bottom": "Extend", "knob": " ", "buttons": "", "tumbler": " ", "tumbler_top": "", "tumbler_mid": "", "tumbler_bottom": "", "green_button_name": "", "red_button_name": ""}, "tower_control": {"name": "Tower", "joy_mid": "Tilt", "joy_mid_top": "Rai<PERSON>", "joy_mid_bottom": "Lower", "joy_left_top": "Extend", "joy_left_bottom": "Retract", "joy_right": "Vertical Pin", "joy_right_top": "Extend", "joy_right_bottom": "Retract", "joy_left": "Inclined <PERSON>n", "knob": " ", "buttons": "Arm", "tumbler": " ", "tumbler_top": "", "tumbler_mid": "", "tumbler_bottom": "", "green_button_name": "Open", "red_button_name": "Close"}, "buildup": {"name": "<PERSON><PERSON>", "joy_mid": "<PERSON><PERSON>", "joy_mid_top": "<PERSON><PERSON>", "joy_mid_bottom": "Swing in", "joy_left_top": "CCW", "joy_left_bottom": "CW", "joy_right": "Feed", "joy_right_top": "Up", "joy_right_bottom": "Down", "joy_left": "Rotation", "knob": "Feed force", "buttons": "<PERSON><PERSON>", "tumbler": "Fork", "tumbler_top": "Retract", "tumbler_mid": "Stop", "tumbler_bottom": "Extend", "green_button_name": "Release", "red_button_name": "Turn"}, "carousel_control": {"name": "Carousel", "joy_mid": "Carousel", "joy_mid_top": "Retract", "joy_mid_bottom": "Extend", "joy_left_top": "CCW", "joy_left_bottom": "CW", "joy_right": "Feed", "joy_right_top": "Up", "joy_right_bottom": "Down", "joy_left": "Rotation", "knob": "Feed force", "buttons": "Index", "tumbler": "Arm", "tumbler_top": "Open", "tumbler_mid": "Stop", "tumbler_bottom": "Close", "green_button_name": "CW", "red_button_name": "CCW"}}}}, "vehicle_selector_controller": {"is_active": true, "file_name": "gui_modules", "class_name": "VehicleSelectorWrapper", "init_args": {}, "prep_args": {"width": 400, "height": 500, "x": 0, "y": 0}}, "auth_button": {"is_active": true, "file_name": "gui_modules", "class_name": "Auth<PERSON><PERSON><PERSON>", "init_args": {}, "prep_args": {"width": 285, "height": 138, "x": 1608, "y": 829, "login_color": "#171717", "logout_color": "#171717"}}, "drop_err_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropDrillError", "init_args": {"text": "Clear\nerror", "fontSize": 20, "needConfirm": false, "needPassword": false}, "prep_args": {"width": 182, "height": 80, "x": 1010, "y": 845}}, "drill_now_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "SendHoleButton", "init_args": {}, "prep_args": {"x": 395, "y": 828, "width": 195, "height": 230, "btn_width": 182, "btn_height": 80, "btn_fontsize": 20, "font_color": "#D2D6E4", "label_font_size": "20px", "screen_num": 0, "url": "/api/drill-now", "max_depth": 33}}, "drop_action_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropActionButton", "init_args": {"text": "Cancel\ntask", "fontSize": 20}, "prep_args": {"width": 182, "height": 80, "x": 606, "y": 945}}, "recalib_air_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "RecalibAirButton", "init_args": {"text": "Recalibrate\nair pressure", "fontSize": 18, "needConfirm": true, "confirmationText": "Current air pressure will be set as nominal"}, "prep_args": {"width": 182, "height": 80, "x": 1010, "y": 945}}, "drop_tailing_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropTailingButton", "init_args": {"text": "Clear\ntailing", "fontSize": 20, "needConfirm": true, "confirmationText": "Detected tailing will be cleared"}, "prep_args": {"width": 182, "height": 80, "x": 808, "y": 945}}, "pullup_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "init_args": {"text": "Force\npullup", "fontSize": 20, "needConfirm": false}, "prep_args": {"width": 182, "height": 80, "x": 1010, "y": 945}}, "lights_switch": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartCheckbox", "init_args": {"text": "Lights", "paramName": "enable_lights", "fontSize": 22, "tracking": true}, "prep_args": {"width": 270, "height": 60, "x": 1615, "y": 30}}, "dc_switch": {"is_active": false, "file_name": "customClasses.smartButton", "class_name": "SmartCheckbox", "init_args": {"text": "Water", "paramName": "dust_collector", "fontSize": 22, "tracking": true}, "prep_args": {"width": 270, "height": 60, "x": 1615, "y": 200}}, "DriveDrillSwitch": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroup", "init_args": {"caption": "Operation mode", "buttons": {"Drill": "False", "Propel": "True"}, "defaultButton": "Drill", "captionSize": 32, "fontSize": 22, "colCount": 1, "paramName": "drivedrill", "tracking": true}, "prep_args": {"width": 300, "height": 200, "x": 1600, "y": 310}}, "cameras_mode": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroupForCore", "init_args": {"caption": "Cameras mode", "buttons": {"Front": "front", "Rear": "rear", "Drilling": "drill", "Birdview": "birdview"}, "captionSize": 32, "fontSize": 22, "colCount": 1, "coreParam": "cameras_mode", "defaultButton": "Front"}, "prep_args": {"width": 300, "height": 280, "x": 1600, "y": 540}}, "photo_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "PhotoButton", "init_args": {"text": "Save images", "fontSize": 22, "needConfirm": false}, "prep_args": {"width": 40, "height": 40, "x": 1834, "y": 1006}}, "rock_type": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroup", "init_args": {"caption": "Drill mode", "buttons": {"Normal": "", "Cracked": "cracked", "Hard": "hard"}, "captionSize": 18, "fontSize": 16, "colCount": 1, "paramName": "rock_type", "defaultButton": "Normal", "tracking": true}, "prep_args": {"width": 355, "height": 200, "x": 1245, "y": 540}}, "hole_water": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroup", "init_args": {"caption": "Wet mode", "buttons": {"Dry": "", "Flooded": "wet"}, "captionSize": 18, "fontSize": 16, "colCount": 1, "paramName": "hole_water", "defaultButton": "Dry", "tracking": true}, "prep_args": {"width": 355, "height": 140, "x": 1245, "y": 740}}, "messages_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "MessagesWidget", "init_args": {}, "prep_args": {"width": 625, "height": 475, "x": 1280, "y": 580, "screen_num": 3, "msg_colors": {"16": "#f28907", "8": "#e01705", "4": "#f0d400", "2": "#50baeb"}, "filter_str": ["angle overflow", "Recording to drill_nolidar", "Closing drill_nolidar", "Opening drill_nolidar", "Frame processing failed with a TF error"]}}, "water_control": {"is_active": true, "file_name": "gui_modules", "class_name": "DiscreteInput", "init_args": {"text": "Water", "param_name": "dust_collector", "step": 0.05, "allowed_modes": ["drilling", "remote"]}, "prep_args": {"width": 270, "height": 120, "x": 1615, "y": 90, "screen_num": 0}}, "first_level_restrictions_off_button": {"is_active": true, "file_name": "gui_modules", "class_name": "EliminateFirstLevelRestrictionsButton", "init_args": {"text": "Eliminate\nRestrictions", "fontSize": 20, "needConfirm": false, "needPassword": true}, "prep_args": {"width": 270, "height": 70, "x": 1615, "y": 230}}, "mover_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "DM ver."}, "prep_args": {"font_size": "15px", "x": 8, "y": 885, "width": 500, "height": 400, "font_color": "#FFFFFF", "screen": 0, "field_to_read": "drill_mover_version", "from_telemetry": true}}, "lidar_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "LS ver."}, "prep_args": {"font_size": "15px", "x": 8, "y": 910, "width": 500, "height": 40, "font_color": "#FFFFFF", "screen": 0, "field_to_read": "lidar_software_version", "from_telemetry": true}}, "rmo_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "RMO ver.", "font_size": "15px", "x": 17, "y": 945, "font_color": "#FFFFFF", "screen": 0}}, "hal_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "HAL ver."}, "prep_args": {"font_size": "15px", "x": 8, "y": 960, "width": 300, "height": 40, "font_color": "#FFFFFF", "screen": 0, "from_telemetry": false}}, "hal_build_id_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "HAL build ID"}, "prep_args": {"font_size": "15px", "x": 8, "y": 985, "width": 300, "height": 40, "font_color": "#FFFFFF", "screen": 0, "from_telemetry": false}}, "disp_ver_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "Disp. ver."}, "prep_args": {"font_size": "15px", "x": 8, "y": 1010, "width": 300, "height": 40, "font_color": "#FFFFFF", "screen": 0, "from_telemetry": false}}, "downtimes_button": {"is_active": true, "file_name": "downtimes", "class_name": "DowntimesButton", "init_args": {"text": "Register downtime", "fontSize": 18, "needConfirm": false}, "prep_args": {"width": 390, "height": 55, "x": 8, "y": 510}}, "dynamic_action_button": {"is_active": true, "file_name": "gui_modules", "class_name": "DynamicActionButton", "init_args": {"text": "Dynamic Action", "fontSize": 20, "needConfirm": false, "needPassword": false}, "prep_args": {"width": 182, "height": 80, "x": 808, "y": 845}}, "reset_shaft_counter_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "ResetShaftCounterButton", "init_args": {"text": "Reset Counter", "fontSize": 20}, "prep_args": {"width": 182, "height": 80, "x": 606, "y": 845}}}