#!/usr/bin/env python3

import enum
import json
import os
import logging
import multiprocessing as mp

# Configuration and resources
dir_path = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(dir_path, 'settings.json')
with open(config_path, 'r') as config:
    CONFIG = json.load(config)

logging.getLogger("urllib3").setLevel(logging.WARNING)

dir_path = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(dir_path, 'resources_settings.json')
with open(config_path, 'r') as config:
    RESOURCES_CONFIG = json.load(config)

# Image save queues for cameras
im_save = [mp.Queue()] * 10  # 10 queues; separate queue for each camera and some more

# Import Form for GUI utilities
from gui_utils import Form

# Constants for restriction modes
DEFAULT_RESTRICTIONS_MODE = 0
FIRST_LEVEL_RESTRICTIONS_OFF = 1
FULL_RESTRICTIONS_OFF = 2

# Messages for restriction modes
FIRST_LEVEL_REST_OFF_MSG = "First level restrictions are off"
ALL_REST_OFF_MSG = "All restrictions are off"


# Enumerations for various modes and states
class RosLogLevels(enum.Enum):
    DEBUG = 1
    INFO = 2
    WARN = 4
    ERROR = 8
    FATAL = 16

def get_from_telemetry(core, key, default_value=0.0):
    """
    Returns the value of a telemetry field from the controlled/viewed machine.
    If there is no connected machine, returns the default value.

    Args:
        core: Instance of the core class.
        key: Name of the field.
        default_value: Default value for the field.

    Returns:
        Value of the telemetry field.
    """
    vehid = core.in_rc_vehid or core.watched_vehid
    if vehid and key in core.telemetry[vehid].keys():
        return core.telemetry[vehid][key]
    return default_value


class DrawVehModes(str, enum.Enum):
    """ Режимы отрисовки машин """
    DRAW = 'draw'
    UPDATE = 'update'


class ControlModes(str, enum.Enum):
    """ Режимы управления машиной """
    DIRECT = 'direct'
    SMART = 'smart'



class TelemetryFields(str, enum.Enum):
    """ Ключи под которыми хранятся значения телеметрии в ядре."""
    POSITION_X = 'position_x'
    POSITION_Y = 'position_y'
    POSITION_Z = 'position_z'
    SPEED = 'speed'
    YAW = 'yaw'
    REAR_JACK_SPEED = 'rear_jack_speed'
    RIGHT_JACK_SPEED = 'right_jack_speed'
    LEFT_JACK_SPEED = 'left_jack_speed'
    SPINDLE_DEPTH = 'spindle_depth'
    DRILL_ROTATION_SPEED = 'drill_rotation_speed'
    DRILL_FEED_SPEED = 'drill_feed_speed'
    DRILL_ANGULAR_POSE = 'drill_angular_pose'
    DRILL_ROTATION_PRESSURE = 'drill_rotation_pressure'
    DRILL_FEED_PRESSURE = 'drill_feed_pressure'
    AIR_PRESSURE = 'air_pressure'
    VIBRATIONS = 'vibrations'
    REAR_JACK_PULLED = 'rear_jack_pulled'
    LEFT_JACK_PULLED = 'left_jack_pulled'
    RIGHT_JACK_PULLED = 'right_jack_pulled'
    PLATFORM_ROLL = 'platform_roll'
    PLATFORM_PITCH = 'platform_pitch'
    TOWER_ANGLE = 'tower_angle'
    VEH_POLYGON_POINTS = 'veh_polygon_points'
    POINTER_COORDINATES = 'pointer_coordinates'


# Note: CoreDataTranslator and EmergencySetter classes have been moved to their respective modules
# in gui_modules/data/core_data_translator.py and gui_modules/controls/emergency_setter.py

