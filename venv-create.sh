#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status

# Check Python version
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1-2)
echo "Using Python $python_version"

# Define the virtual environment directory
VENV_DIR="venv/"

# Check if venv directory already exists
if [ ! -d "$VENV_DIR" ]; then
    echo "Creating virtual environment..."
    # Create virtual environment using python3
    python3 -m venv $VENV_DIR
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create a virtual environment using python3."
        exit 1
    fi
else
    echo "Virtual environment directory already exists. Skipping creation."
fi

# Activate virtual environment
source ${VENV_DIR}bin/activate

# Upgrade pip to latest version
python -m pip install --upgrade pip

# Install wheel and setuptools first
pip install --upgrade wheel setuptools

# Install requirements
echo "Installing dependencies from requirements.txt..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # Mac OS X - specific handling
    echo "Detected macOS, using platform-specific installation..."
    # Install dependencies that might need special handling on Mac
    pip install -r requirements.txt
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux specific handling
    echo "Detected Linux, using platform-specific installation..."
    pip install -r requirements.txt
else
    # Generic installation
    echo "Using generic installation method..."
    pip install -r requirements.txt
fi

if [ $? -ne 0 ]; then
    echo "Error: Failed to install packages from requirements.txt."
    deactivate
    exit 1
fi

# Install socket_communicator module
echo "Installing socket_communicator module..."
cd common/socket_communicator

# Clean up any previous build artifacts that might cause permission issues
rm -rf build dist *.egg-info 2>/dev/null || true

# Use pip install -e instead of setup.py install (modern approach)
pip install -e .

if [ $? -ne 0 ]; then
    echo "Error: Failed to install socket_communicator module."
    deactivate
    exit 1
fi
cd ../..

echo "Successfully installed all dependencies!"
echo "To activate the virtual environment, run: source ${VENV_DIR}bin/activate"

# Deactivate virtual environment
deactivate
