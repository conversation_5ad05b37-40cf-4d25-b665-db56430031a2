#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from core.customClasses.styledCheckBox import StyledCheckBox

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("StyledCheckBox Test")
        self.setGeometry(100, 100, 400, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Create a StyledCheckBox with tracking=True
        self.checkbox = StyledCheckBox(tracking=True, text="Test Checkbox")
        self.checkbox.clicked.connect(self.on_checkbox_clicked)
        
        layout.addWidget(self.checkbox)
    
    def on_checkbox_clicked(self, state):
        print(f"Checkbox clicked, state: {state}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())
