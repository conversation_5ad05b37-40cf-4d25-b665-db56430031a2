#!/bin/zsh

# ROS2 Route Simulator launcher script
echo "Starting ROS2 Route Simulator..."

# Source ROS2 workspace
source ~/Work/drill2/onboard/install/local_setup.zsh

# Set PYTHONPATH to include drill_msgs
export PYTHONPATH=$PYTHONPATH:~/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages
export PYTHONPATH=$PYTHONPATH:~/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages

# Get namespace from command line argument or use default
NAMESPACE=${1:-local_sim}

echo "Using namespace: $NAMESPACE"
echo "PYTHONPATH: $PYTHONPATH"

# Run the simulator
python3 test_route_simulator.py $NAMESPACE
