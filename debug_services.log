Running on macOS
Starting application...
/Users/<USER>/Work/iplace-3.0/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
DEBUG:root:Preparing Communicator
DEBUG:root:Module Communicator prepared
DEBUG:root:Preparing ZenohClient
DEBUG:ZenohClient:Registering service types: drill_msgs/srv/GetCurrentDriveAction_Request, drill_msgs/srv/GetCurrentDriveAction_Response
DEBUG:ZenohClient:Registered request type: drill_msgs/srv/GetCurrentDriveAction_Request
DEBUG:ZenohClient:Registered response type: drill_msgs/srv/GetCurrentDriveAction_Response
INFO:ZenohClient:Registered 42 custom ROS2 types
INFO:ZenohClient:ZenohClient online · 193 ROS2 types · pool=8
DEBUG:ZenohClient:Zenoh session opened
DEBUG:root:Module ZenohClient prepared
DEBUG:root:Preparing JoystickAdapter
ERROR:root:[Errno 2] could not open port /dev/ttyACM0: [Errno 2] No such file or directory: '/dev/ttyACM0'
ERROR:root:Could not connect to joystick controller /dev/ttyACM0
ERROR:root:[Errno 2] could not open port /dev/ttyACM1: [Errno 2] No such file or directory: '/dev/ttyACM1'
ERROR:root:Could not connect to joystick controller /dev/ttyACM1
ERROR:root:[Errno 2] could not open port /dev/ttyACM2: [Errno 2] No such file or directory: '/dev/ttyACM2'
ERROR:root:Could not connect to joystick controller /dev/ttyACM2
ERROR:root:[Errno 2] could not open port /dev/ttyACM3: [Errno 2] No such file or directory: '/dev/ttyACM3'
ERROR:root:Could not connect to joystick controller /dev/ttyACM3
ERROR:root:[Errno 2] could not open port /dev/ttyACM4: [Errno 2] No such file or directory: '/dev/ttyACM4'
ERROR:root:Could not connect to joystick controller /dev/ttyACM4
INFO:root:Starting UDP-server to accept control data...
INFO:root:Started UDP-server on 0.0.0.0:2000
DEBUG:root:Module JoystickAdapter prepared
DEBUG:root:Preparing PlanReader
DEBUG:root:Module PlanReader prepared
DEBUG:root:Preparing Map
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
DEBUG:root:Module Map prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing AngleIndicator
QWidget::setLayout: Attempting to set QLayout "" on QMainWindow "", which already has a layout
DEBUG:root:Module AngleIndicator prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing GPSWidget
DEBUG:root:Module GPSWidget prepared
DEBUG:root:Preparing TextWidget
DEBUG:root:Module TextWidget prepared
DEBUG:root:Preparing MachineStateManagement
DEBUG:root:Module MachineStateManagement prepared
DEBUG:root:Preparing MovingDataTranslator
DEBUG:root:Module MovingDataTranslator prepared
DEBUG:root:Preparing MovingDataTranslator
DEBUG:root:Module MovingDataTranslator prepared
DEBUG:root:Preparing ConditionalTextWidget
DEBUG:root:Module ConditionalTextWidget prepared
DEBUG:root:Preparing CustomSliderWrapper
DEBUG:root:Module CustomSliderWrapper prepared
DEBUG:root:Preparing CustomSliderWrapper
DEBUG:root:Module CustomSliderWrapper prepared
DEBUG:root:Preparing DepthWidgetWrapper
DEBUG:root:Module DepthWidgetWrapper prepared
DEBUG:root:Preparing JoystickWidgetWrapper
DEBUG:root:Module JoystickWidgetWrapper prepared
DEBUG:root:Preparing VehicleSelector
DEBUG:ZenohWidget:Subscribing to main_state (drill_msgs/msg/StateMachineStatus) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/main_state with type drill_msgs/msg/StateMachineStatus
DEBUG:ZenohWidget:Subscribing to permission (drill_msgs/msg/Permission) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/permission with type drill_msgs/msg/Permission
DEBUG:ZenohWidget:Subscribing to robomode (drill_msgs/msg/BoolStamped) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/robomode with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to emergency_status (drill_msgs/msg/BoolStamped) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/emergency_status with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to planner_state (drill_msgs/msg/StateMachineStatus) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/planner_state with type drill_msgs/msg/StateMachineStatus
DEBUG:ZenohWidget:Subscribing to main_state (drill_msgs/msg/StateMachineStatus) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/main_state with type drill_msgs/msg/StateMachineStatus
DEBUG:ZenohWidget:Subscribing to permission (drill_msgs/msg/Permission) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/permission with type drill_msgs/msg/Permission
DEBUG:ZenohWidget:Subscribing to robomode (drill_msgs/msg/BoolStamped) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/robomode with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to emergency_status (drill_msgs/msg/BoolStamped) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/emergency_status with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to planner_state (drill_msgs/msg/StateMachineStatus) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/planner_state with type drill_msgs/msg/StateMachineStatus
DEBUG:root:Got WATCH request for vehid None
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:root:Module VehicleSelector prepared
DEBUG:root:Preparing DowntimesButton
DEBUG:root:Module DowntimesButton prepared
DEBUG:root:Preparing AuthButton
DEBUG:root:Module AuthButton prepared
DEBUG:root:Preparing DropDrillError
DEBUG:root:Module DropDrillError prepared
DEBUG:root:Preparing SendHoleButton
DEBUG:root:Module SendHoleButton prepared
DEBUG:root:Preparing DropActionButton
DEBUG:root:Module DropActionButton prepared
DEBUG:root:Preparing DropTailingButton
DEBUG:root:Module DropTailingButton prepared
DEBUG:root:Preparing PullupButton
DEBUG:root:Module PullupButton prepared
DEBUG:root:Preparing SmartCheckbox
DEBUG:root:Module SmartCheckbox prepared
DEBUG:root:Preparing SmartButtonGroup
DEBUG:root:Module SmartButtonGroup prepared
DEBUG:root:Preparing SmartButtonGroupForCore
DEBUG:root:Module SmartButtonGroupForCore prepared
DEBUG:root:Preparing PhotoButton
DEBUG:root:Module PhotoButton prepared
DEBUG:root:Preparing SmartButtonGroup
DEBUG:root:Module SmartButtonGroup prepared
DEBUG:root:Preparing SmartButtonGroup
DEBUG:root:Module SmartButtonGroup prepared
DEBUG:root:Preparing MessagesWidget
DEBUG:root:Module MessagesWidget prepared
DEBUG:root:Preparing DiscreteInput
DEBUG:root:Module DiscreteInput prepared
DEBUG:root:Preparing TextWidget
DEBUG:root:Module TextWidget prepared
DEBUG:root:Preparing EliminateFirstLevelRestrictionsButton
DEBUG:root:Module EliminateFirstLevelRestrictionsButton prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing DynamicActionButton
DEBUG:root:Module DynamicActionButton prepared
DEBUG:root:Preparing ResetShaftCounterButton
DEBUG:root:Module ResetShaftCounterButton prepared
ERROR:root:Error starting module ZenohClient: 'ZenohClient' object has no attribute 'start'
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
qt.qpa.fonts: Populating font family aliases took 80 ms. Replace uses of missing font family "Roboto" with one that exists to avoid this cost. 
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohWidget:Source 'main_state' in ns 'local_sim' freshness changed: True
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
INFO:root:Connected to rG5
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:root:Got WATCH request for vehid local_sim
DEBUG:ZenohWidget:Setting up service source 'route_segments' for service get_current_drive_action
DEBUG:ZenohWidget:Subscribing to position (drill_msgs/msg/Position) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/position with type drill_msgs/msg/Position
DEBUG:ZenohWidget:Subscribing to driver_status (drill_msgs/msg/DriveStatus) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/driver_status with type drill_msgs/msg/DriveStatus
DEBUG:ZenohWidget:Subscribing to obstacles_vis (visualization_msgs/msg/MarkerArray) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/obstacles_vis with type visualization_msgs/msg/MarkerArray
DEBUG:ZenohWidget:Subscribing to safety_berm_map (visualization_msgs/msg/MarkerArray) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/safety_berm_map with type visualization_msgs/msg/MarkerArray
DEBUG:ZenohWidget:Subscribing to border_vis (visualization_msgs/msg/MarkerArray) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/border_vis with type visualization_msgs/msg/MarkerArray
DEBUG:ZenohWidget:Subscribing to level (drill_msgs/msg/Level) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/level with type drill_msgs/msg/Level
DEBUG:ZenohWidget:Subscribing to tower_inclinometer (drill_msgs/msg/Vector2d) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/tower_inclinometer with type drill_msgs/msg/Vector2d
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=921468000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=36, last_action_id=35, cur_segment_id=2, cur_point_id=5, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=921468000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=36, last_action_id=35, cur_segment_id=2, cur_point_id=5, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 36
DEBUG:ZenohWidget:Source 'driver_action_id' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 36
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Calling service get_current_drive_action with type drill_msgs/srv/GetCurrentDriveAction in namespace local_sim
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohClient:Calling service local_sim/get_current_drive_action with type GetCurrentDriveAction
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
ERROR:ZenohClient:Service request type drill_msgs/srv/GetCurrentDriveAction_Request not found in registry
DEBUG:ZenohWidget:Service response handler connected
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohClient:Available service types: ['drill_msgs/srv/msg/GetCurrentDriveAction_Request', 'drill_msgs/srv/msg/GetCurrentDriveAction_Response']
DEBUG:ZenohWidget:After part 0: 1 values
WARNING:ZenohClient:Async service call to get_current_drive_action returned None
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:Source 'driver_segment_id' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 5
DEBUG:ZenohWidget:Source 'driver_point_id' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=963826000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=11.211774826049805, y=20.788225173950195, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=963826000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=11.211774826049805, y=20.788225173950195, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 11.211774826049805
DEBUG:ZenohWidget:Source 'position_x' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 11.211774826049805
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 20.788225173950195
DEBUG:ZenohWidget:Source 'position_y' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 20.788225173950195
DEBUG:ZenohWidget:Source 'yaw' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=73420000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=11.141064643859863, y=20.858936309814453, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=73420000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=11.141064643859863, y=20.858936309814453, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 11.141064643859863
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 20.858936309814453
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 11.141064643859863
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 20.858936309814453
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=173482000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=11.070353507995605, y=20.929645538330078, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=173482000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=11.070353507995605, y=20.929645538330078, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 11.070353507995605
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 20.929645538330078
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 11.070353507995605
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 20.929645538330078
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=274118000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.999643325805664, y=21.000356674194336, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=274118000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.999643325805664, y=21.000356674194336, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
ERROR:root:Socket error: Connection closed or empty response
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
ERROR:root:No connection to vehicle rG5
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.999643325805664
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.000356674194336
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.999643325805664
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.000356674194336
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=382720000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.928932189941406, y=21.071067810058594, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=382720000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.928932189941406, y=21.071067810058594, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.928932189941406
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.071067810058594
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.928932189941406
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.071067810058594
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=483158000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.858221054077148, y=21.14177894592285, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=483158000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.858221054077148, y=21.14177894592285, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.858221054077148
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.14177894592285
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.858221054077148
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.14177894592285
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=593336000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.787510871887207, y=21.21249008178711, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=593336000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.787510871887207, y=21.21249008178711, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.787510871887207
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.787510871887207
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.21249008178711
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.21249008178711
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
dev_mode is : True
text color is:
smart button init
smart button init
smart button init
smart button init
smart button init
fontSize is not None:  True
fontSize is not None:  True
smart button init
fontSize is not None:  True
fontSize is not None:  True
smart button init
smart button init
smart button init
Map widget subscribe: current_action_id = None
No action ID available at startup, skipping route request
Namespace changed to 'local_sim', routes cache cleared
handle_driver_action_id called: old=None, new=36
Action ID changed: None -> 36
Requested routes for action ID: 36
handle_driver_action_id called: old=36, new=36
Action ID unchanged: 36
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=703642000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.71679973602295, y=21.283199310302734, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=703642000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.71679973602295, y=21.283199310302734, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.71679973602295
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.71679973602295
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.283199310302734
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.283199310302734
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=813304000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.646089553833008, y=21.353910446166992, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=813304000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.646089553833008, y=21.353910446166992, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.646089553833008
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.646089553833008
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.353910446166992
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.353910446166992
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=922930000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=36, last_action_id=35, cur_segment_id=2, cur_point_id=5, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=922930000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=36, last_action_id=35, cur_segment_id=2, cur_point_id=5, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=922756000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=922756000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 36
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 36
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=23338000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=23338000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=133378000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=133378000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=233777000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=233777000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=343386000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=343386000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=453460000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=453460000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=563396000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=563396000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
handle_driver_action_id called: old=36, new=36
Action ID unchanged: 36
handle_driver_action_id called: old=36, new=36
Action ID unchanged: 36
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=699718000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=699718000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=802823000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=802823000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=10.57537841796875, y=21.42462158203125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 10.57537841796875
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 21.42462158203125
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 10.57537841796875
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 21.42462158203125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=903789000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=903789000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=923440000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=0, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=923440000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=0, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 37
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 37
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Calling service get_current_drive_action with type drill_msgs/srv/GetCurrentDriveAction in namespace local_sim
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohClient:Calling service local_sim/get_current_drive_action with type GetCurrentDriveAction
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
ERROR:ZenohClient:Service request type drill_msgs/srv/GetCurrentDriveAction_Request not found in registry
DEBUG:ZenohClient:Available service types: ['drill_msgs/srv/msg/GetCurrentDriveAction_Request', 'drill_msgs/srv/msg/GetCurrentDriveAction_Response']
WARNING:ZenohClient:Async service call to get_current_drive_action returned None
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 0
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 0
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 0
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 0
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13039000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13039000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=123450000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=123450000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=233404000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=233404000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=334344000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=334344000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
handle_driver_action_id called: old=36, new=37
Action ID changed: 36 -> 37
Requested routes for action ID: 37
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=442414000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=442414000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=553844000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=553844000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=663383000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=663383000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=773466000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=773466000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883424000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883424000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=933614000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=0, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=933614000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=0, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 37
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 37
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 0
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 0
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 0
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=983472000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=983472000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=92520000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=92520000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=192869000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=192869000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
INFO:root:Connected to rG5
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=293387000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=293387000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=402010000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=402010000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=502137000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=502137000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=602753000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=602753000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=703419000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=703419000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=803644000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=803644000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=913409000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=913409000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=943794000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=0, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=943794000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=0, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 37
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 37
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 0
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 0
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 0
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=23312000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=23312000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=133374000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=133374000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=243716000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=243716000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=352596000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=352596000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=453671000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=453671000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=563650000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=563650000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=663967000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=663967000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
ERROR:root:Socket error: Connection closed or empty response
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
ERROR:root:No connection to vehicle rG5
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=773325000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=773325000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883224000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.10000000149011612, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883224000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.10000000149011612, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.10000000149011612
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.10000000149011612
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=953573000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=1, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=953573000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=1, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 37
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 37
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 0
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 1
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 1
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=983335000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.20000000298023224, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=983335000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.20000000298023224, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.20000000298023224
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.20000000298023224
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=93354000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.30000001192092896, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=93354000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.30000001192092896, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.30000001192092896
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.30000001192092896
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=202762000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.4000000059604645, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=202762000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.4000000059604645, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.4000000059604645
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.4000000059604645
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=303482000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.5, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=303482000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.5, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.5
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.5
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=413379000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.6000000238418579, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=413379000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.6000000238418579, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.6000000238418579
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.6000000238418579
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=522924000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.699999988079071, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=522924000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.699999988079071, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.699999988079071
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.699999988079071
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=623390000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.800000011920929, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=623390000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.800000011920929, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.800000011920929
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.800000011920929
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=723877000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.8999999761581421, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=723877000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=0.8999999761581421, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 0.8999999761581421
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 0.8999999761581421
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=833698000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=833698000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=943334000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.100000023841858, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=943334000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.100000023841858, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.100000023841858
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.100000023841858
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=963419000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=1, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=963419000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=1, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 37
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 37
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 0
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 1
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 0
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 1
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=52388000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.2000000476837158, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=52388000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.2000000476837158, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.2000000476837158
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.2000000476837158
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=163868000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.2999999523162842, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=163868000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.2999999523162842, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.2999999523162842
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.2999999523162842
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=273307000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.399999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=273307000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.399999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.399999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
INFO:root:Connected to rG5
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.399999976158142
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=383618000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.5, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=383618000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.5, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.5
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.5
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=492858000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.600000023841858, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=492858000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.600000023841858, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.600000023841858
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
INFO:root:Connected to rG5
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.600000023841858
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=593433000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.7000000476837158, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=593433000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.7000000476837158, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.7000000476837158
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.7000000476837158
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=703334000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.7999999523162842, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=703334000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.7999999523162842, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.7999999523162842
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.7999999523162842
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=813346000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=813346000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=923440000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=923440000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=973511000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=1, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=973511000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=1, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 37
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 0
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 1
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 37
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 0
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 1
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=31816000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=31816000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=133364000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=133364000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=242352000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=242352000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=343810000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=343810000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
handle_driver_action_id called: old=37, new=37
Action ID unchanged: 37
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=453339000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=453339000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=553497000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=553497000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=663398000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=663398000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=773494000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=773494000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=1.899999976158142, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 1.899999976158142
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 1.899999976158142
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883736000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883736000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.0, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 2.0
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 2.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 0.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 0.0
INFO:root:Connected to rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Extracting field 'current_state' from data type: <class 'rosbags.usertypes.drill_msgs__msg__StateMachineStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['current_state']
DEBUG:ZenohWidget:Processing part 0: 'current_state' (field='current_state', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'current_state' = <class 'str'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'str'>
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
INFO:root:halt event
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=983574000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=983574000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=37, last_action_id=36, cur_segment_id=0, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Extracting field 'cur_action_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_action_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_action_id' (field='cur_action_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_action_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 37
DEBUG:ZenohWidget:Extracting field 'cur_segment_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_segment_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_segment_id' (field='cur_segment_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_segment_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 0
DEBUG:ZenohWidget:Extracting field 'cur_point_id' from data type: <class 'rosbags.usertypes.drill_msgs__msg__DriveStatus'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['cur_point_id']
DEBUG:ZenohWidget:Processing part 0: 'cur_point_id' (field='cur_point_id', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'cur_point_id' = <class 'int'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'int'>
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 2
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=992799000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.0999999046325684, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=992799000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.0999999046325684, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 2.0999999046325684
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748461160, nanosec=93441000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.200000047683716, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748461160, nanosec=93441000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.200000047683716, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 2.200000047683716
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748461160, nanosec=193861000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.299999952316284, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748461160, nanosec=193861000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=2.299999952316284, y=0.0, z=0.0, yaw=0.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Extracting field 'x' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['x']
DEBUG:ZenohWidget:Processing part 0: 'x' (field='x', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'x' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 2.299999952316284
DEBUG:ZenohWidget:Extracting field 'y' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['y']
DEBUG:ZenohWidget:Processing part 0: 'y' (field='y', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'y' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 0.0
DEBUG:ZenohWidget:Extracting field 'yaw' from data type: <class 'rosbags.usertypes.drill_msgs__msg__Position'>
DEBUG:ZenohWidget:Starting with 1 values, parts: ['yaw']
DEBUG:ZenohWidget:Processing part 0: 'yaw' (field='yaw', iterate=False)
DEBUG:ZenohWidget:Object item 0: field 'yaw' = <class 'float'>
DEBUG:ZenohWidget:After part 0: 1 values
DEBUG:ZenohWidget:Returning single value: <class 'float'>
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 0.0
DEBUG:ZenohClient:Unsubscribing from local_sim/position
DEBUG:ZenohClient:Unsubscribing from local_sim/driver_status
DEBUG:ZenohClient:Unsubscribing from local_sim/obstacles_vis
DEBUG:ZenohClient:Unsubscribing from local_sim/safety_berm_map
DEBUG:ZenohClient:Unsubscribing from local_sim/border_vis
DEBUG:ZenohClient:Unsubscribing from local_sim/level
DEBUG:ZenohClient:Unsubscribing from local_sim/tower_inclinometer
./run_app.sh: line 31: 90095 Terminated: 15          python3 main.py dev_mode
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 3 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
