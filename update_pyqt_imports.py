#!/usr/bin/env python3

import os
import re

def update_pyqt_imports(file_path):
    with open(file_path, 'r') as file:
        content = file.read()
    
    # Replace PyQt5 imports with PyQt6
    updated_content = content.replace('from PyQt5', 'from PyQt6')
    updated_content = updated_content.replace('import PyQt5', 'import PyQt6')
    
    # Update Qt constants
    updated_content = re.sub(r'Qt\.([A-Za-z0-9_]+)(?!\s*=|\s*\(|\s*\.)', r'Qt.\1', updated_content)
    
    # Update specific enum values
    enum_replacements = {
        'Qt.AlignCenter': 'Qt.AlignmentFlag.AlignCenter',
        'Qt.AlignLeft': 'Qt.AlignmentFlag.AlignLeft',
        'Qt.AlignRight': 'Qt.AlignmentFlag.AlignRight',
        'Qt.AlignTop': 'Qt.AlignmentFlag.AlignTop',
        'Qt.AlignBottom': 'Qt.AlignmentFlag.AlignBottom',
        'Qt.AlignHCenter': 'Qt.AlignmentFlag.AlignHCenter',
        'Qt.AlignVCenter': 'Qt.AlignmentFlag.AlignVCenter',
        'Qt.AlignJustify': 'Qt.AlignmentFlag.AlignJustify',
        'Qt.AlignAbsolute': 'Qt.AlignmentFlag.AlignAbsolute',
        'Qt.AlignLeading': 'Qt.AlignmentFlag.AlignLeading',
        'Qt.AlignTrailing': 'Qt.AlignmentFlag.AlignTrailing',
        'Qt.AlignBaseline': 'Qt.AlignmentFlag.AlignBaseline',
        'Qt.FramelessWindowHint': 'Qt.WindowType.FramelessWindowHint',
        'Qt.WA_TranslucentBackground': 'Qt.WidgetAttribute.WA_TranslucentBackground',
        'Qt.SolidLine': 'Qt.PenStyle.SolidLine',
        'Qt.DashLine': 'Qt.PenStyle.DashLine',
        'Qt.DotLine': 'Qt.PenStyle.DotLine',
        'Qt.DashDotLine': 'Qt.PenStyle.DashDotLine',
        'Qt.DashDotDotLine': 'Qt.PenStyle.DashDotDotLine',
        'Qt.white': 'Qt.GlobalColor.white',
        'Qt.black': 'Qt.GlobalColor.black',
        'Qt.red': 'Qt.GlobalColor.red',
        'Qt.darkRed': 'Qt.GlobalColor.darkRed',
        'Qt.green': 'Qt.GlobalColor.green',
        'Qt.darkGreen': 'Qt.GlobalColor.darkGreen',
        'Qt.blue': 'Qt.GlobalColor.blue',
        'Qt.darkBlue': 'Qt.GlobalColor.darkBlue',
        'Qt.cyan': 'Qt.GlobalColor.cyan',
        'Qt.darkCyan': 'Qt.GlobalColor.darkCyan',
        'Qt.magenta': 'Qt.GlobalColor.magenta',
        'Qt.darkMagenta': 'Qt.GlobalColor.darkMagenta',
        'Qt.yellow': 'Qt.GlobalColor.yellow',
        'Qt.darkYellow': 'Qt.GlobalColor.darkYellow',
        'Qt.gray': 'Qt.GlobalColor.gray',
        'Qt.darkGray': 'Qt.GlobalColor.darkGray',
        'Qt.lightGray': 'Qt.GlobalColor.lightGray',
        'Qt.transparent': 'Qt.GlobalColor.transparent',
        'QPalette.Window': 'QPalette.ColorRole.Window',
        'QPalette.WindowText': 'QPalette.ColorRole.WindowText',
        'QPalette.Base': 'QPalette.ColorRole.Base',
        'QPalette.AlternateBase': 'QPalette.ColorRole.AlternateBase',
        'QPalette.ToolTipBase': 'QPalette.ColorRole.ToolTipBase',
        'QPalette.ToolTipText': 'QPalette.ColorRole.ToolTipText',
        'QPalette.Text': 'QPalette.ColorRole.Text',
        'QPalette.Button': 'QPalette.ColorRole.Button',
        'QPalette.ButtonText': 'QPalette.ColorRole.ButtonText',
        'QPalette.BrightText': 'QPalette.ColorRole.BrightText',
        'QPalette.Link': 'QPalette.ColorRole.Link',
        'QPalette.Highlight': 'QPalette.ColorRole.Highlight',
        'QPalette.HighlightedText': 'QPalette.ColorRole.HighlightedText',
        'QSizePolicy.Fixed': 'QSizePolicy.Policy.Fixed',
        'QSizePolicy.Minimum': 'QSizePolicy.Policy.Minimum',
        'QSizePolicy.Maximum': 'QSizePolicy.Policy.Maximum',
        'QSizePolicy.Preferred': 'QSizePolicy.Policy.Preferred',
        'QSizePolicy.Expanding': 'QSizePolicy.Policy.Expanding',
        'QSizePolicy.MinimumExpanding': 'QSizePolicy.Policy.MinimumExpanding',
        'QSizePolicy.Ignored': 'QSizePolicy.Policy.Ignored',
        'QScroller.TouchGesture': 'QScroller.GestureType.TouchGesture',
        'QScroller.LeftMouseButtonGesture': 'QScroller.GestureType.LeftMouseButtonGesture',
        'QScroller.RightMouseButtonGesture': 'QScroller.GestureType.RightMouseButtonGesture',
        'QScroller.MiddleMouseButtonGesture': 'QScroller.GestureType.MiddleMouseButtonGesture',
    }
    
    for old, new in enum_replacements.items():
        updated_content = updated_content.replace(old, new)
    
    # Replace QFontMetrics.width with QFontMetrics.horizontalAdvance
    updated_content = re.sub(r'([a-zA-Z0-9_]+)\.width\(([^)]+)\)', r'\1.horizontalAdvance(\2)', updated_content)
    
    if content != updated_content:
        with open(file_path, 'w') as file:
            file.write(updated_content)
        return True
    return False

def process_directory(directory):
    updated_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if update_pyqt_imports(file_path):
                    updated_files.append(file_path)
    return updated_files

if __name__ == '__main__':
    directories = ['core/gui_modules', 'core/customClasses', 'core/drillJoystickWidget']
    for directory in directories:
        updated = process_directory(directory)
        print(f"Updated {len(updated)} files in {directory}")
        for file in updated:
            print(f"  - {file}")
